<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\food\controller;

use app\food\model\FoodPackage;
use app\food\model\FoodPackageItem;
use Exception;
use plugin\saiadmin\basic\BaseController;
use app\food\logic\FoodPackageLogic;
use app\food\model\FoodContent;
use app\food\model\FoodPackageConfig;
use app\food\validate\FoodPackageValidate;
use support\Request;
use support\Response;
use hg\apidoc\annotation as Apidoc;
use Tinywan\ExceptionHandler\Exception\BadRequestHttpException;
use app\utils\Excel;

/**
 * @Apidoc\Title("周套餐设置")
 */
class FoodPackageController extends BaseController
{
    protected string $pk = "package_id";

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->logic = new FoodPackageLogic();
        $this->validate = new FoodPackageValidate;
        parent::__construct();
    }

    /**
     * @Apidoc\Title("数据列表")
     * @Apidoc\Url("/food/FoodPackage/index")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("page", type="int", require=false, desc="框架自带-页码,默认1", default="1")
     * @Apidoc\Query("limit", type="int", require=false, desc="框架自带-每页数据,默认10", default="10")
     * @Apidoc\Query("saiType", type="string", require=false, desc="框架自带-获取数据类型;默认list分页;all全部数据", default="list")
     * @Apidoc\Query("orderBy", type="string", require=false, desc="框架自带-排序字段,默认主键", default="")
     * @Apidoc\Query("orderType", type="string", require=false, desc="框架自带-排序方式,默认ASC", default="")
     * @Apidoc\Query("name", type="varchar", require=false, desc="套餐名称", default="")
     * @Apidoc\Query("day_time", type="date", require=false, desc="有效日期", default="")
     * @Apidoc\Returned("data", type="array", require=true, desc="分页数据")
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        $where = $request->more([
            ['name', ''],
            ['day_time', ''],
        ]);
        $query = $this->logic->search($where);
        $data = $this->logic->getList($query);
        return $this->success($data);
    }

    /**
     * @Apidoc\Title("保存数据")
     * @Apidoc\Url("/food/FoodPackage/save")
     * @Apidoc\Method("POST")
     * @Apidoc\Query("config_id", type="int", require=false, desc="套餐模板编号", default="")
     * @Apidoc\Query("name", type="varchar", require=false, desc="套餐名称", default="")
     * @Apidoc\Query("price", type="decimal", require=false, desc="套餐价格", default="")
     * @Apidoc\Query("day_time", type="date", require=false, desc="有效日期", default="")
     * @Apidoc\Query("sort", type="decimal", require=false, desc="排序", default="")
     * @param Request $request
     * @return Response
     */
    public function save(Request $request) : Response
    {
        $data = $request->post();
        if ($this->validate) {
            if (!$this->validate->scene('save')->check($data)) {
                return $this->fail($this->validate->getError());
            }
        }
        $result = $this->logic->save($data);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("修改数据")
     * @Apidoc\Url("/food/FoodPackage/update")
     * @Apidoc\Method("PUT")
     * @Apidoc\Query("id", type="int", require=true, desc="主键", default="")
     * @Apidoc\Query("config_id", type="int", require=false, desc="套餐模板编号", default="")
     * @Apidoc\Query("name", type="varchar", require=false, desc="套餐名称", default="")
     * @Apidoc\Query("price", type="decimal", require=false, desc="套餐价格", default="")
     * @Apidoc\Query("day_time", type="date", require=false, desc="有效日期", default="")
     * @Apidoc\Query("sort", type="decimal", require=false, desc="排序", default="")
     * @param Request $request
     * @param $id
     * @return Response
     */
    public function update(Request $request, $id) : Response
    {
        $id = $request->input('id', $id);
        $data = $request->post();
        if ($this->validate) {
            if (!$this->validate->scene('update')->check($data)) {
                return $this->fail($this->validate->getError());
            }
        }
        $info = $this->logic->find($id);
        if (!$info) {
            return $this->fail('没有找到该数据');
        }
        $result = $this->logic->update($data, [$this->pk => $id]);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("读取数据")
     * @Apidoc\Url("/food/FoodPackage/read")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("id", type="int", require=true, desc="主键", default="")
     * @param Request $request
     * @param $id
     * @return Response
     */
    public function read(Request $request, $id) : Response
    {
        $id = $request->input('id', $id);
        $model = $this->logic->find($id);
        if ($model) {
            $data = is_array($model) ? $model : $model->toArray();
            return $this->success($data);
        } else {
            return $this->fail('未查找到信息');
        }
    }

    /**
     * @Apidoc\Title("修改状态")
     * @Apidoc\Url("/food/FoodPackage/changeStatus")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("id", type="int", require=true, desc="主键", default="")
     * @Apidoc\Param("status", type="int", require=true, desc="状态", default="1")
     * @param Request $request
     * @return Response
     */
    public function changeStatus(Request $request) : Response
    {
        $id = $request->input('id', '');
        $status = $request->input('status', 1);
        $result = $this->logic->where($this->pk, $id)->update(['status' => $status]);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("删除数据")
     * @Apidoc\Url("/food/FoodPackage/destroy")
     * @Apidoc\Method("DELETE")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function destroy(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->destroy($ids);
            return $this->success('操作成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }

    /**
     * @Apidoc\Title("回收站数据")
     * @Apidoc\Url("/food/FoodPackage/recycle")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("page", type="int", require=false, desc="框架自带-页码,默认1", default="1")
     * @Apidoc\Query("limit", type="int", require=false, desc="框架自带-每页数据,默认10", default="10")
     * @Apidoc\Query("saiType", type="string", require=false, desc="框架自带-获取数据类型;默认list分页;all全部数据", default="list")
     * @Apidoc\Query("orderBy", type="string", require=false, desc="框架自带-排序字段,默认主键", default="")
     * @Apidoc\Query("orderType", type="string", require=false, desc="框架自带-排序方式,默认ASC", default="")
     * @param Request $request
     * @return Response
     */
    public function recycle(Request $request) : Response
    {
        $where = $request->more([
            ['create_time', ''],
        ]);
        $query = $this->logic->recycle()->search($where);
        $data = $this->logic->getList($query);
        return $this->success($data);
    }

    /**
     * @Apidoc\Title("恢复数据")
     * @Apidoc\Url("/food/FoodPackage/recovery")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function recovery(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->restore($ids);
            return $this->success('恢复成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }

    /**
     * @Apidoc\Title("销毁数据")
     * @Apidoc\Url("/food/FoodPackage/realDestroy")
     * @Apidoc\Method("DELETE")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function realDestroy(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->destroy($ids, true);
            return $this->success('操作成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }


    /**
     * @Apidoc\Title("周套餐查询")
     * @Apidoc\Url("/food/FoodPackage/list")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("start_time", type="date", require=false, desc="有效日期", default="")
     * @Apidoc\Query("end_time", type="date", require=false, desc="有效日期", default="")
     * @Apidoc\Query("limit", type="int", require=false, desc="框架自带-每页数据,默认10", default="1000")
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function list(Request $request): Response
    {
        $params = $request->only(
            ['start_time', 'end_time'],
        );
        try {
            validate(FoodPackageValidate::class)
                ->scene('init')
                ->check($request->all());
        } catch (Exception $e) {
            // 验证失败 输出错误信息
            throw new BadRequestHttpException($e->getMessage());
        }
        $where = [
            'day_time' => [$params['start_time'],$params['end_time']],
            'type' => 0
        ];

        $query = $this->logic->search($where);
        $data = $this->logic->getAll($query);
        if(empty($data)){
            // 创建一个 Request 对象并传递 day_time
            // 进行初始化
            $this->init1($request);

            // 重新查询
            $data = $this->logic->getAll($query);
        }
        // 根据套餐名进行分组
        $groupedFoods = [];
        if (!empty($data)) {
            foreach ($data as $v) {
                $name = $v['name'];

                // 初始化分组，如果尚不存在
                if (!isset($groupedFoods[$name])) {
                    $groupedFoods[$name] = []; // 创建一个空数组用于存放该组的数据
                }

                // 添加菜品数据
                $v['foods'] = $this->dao->search('food_package_item',[
                    'package_id' => $v['package_id'],
                ],[
                    'id',
                    'package_id',
                    'food_id',
                    'food_name',
                    'food_cate',
                    'num',
                ]);

                // 将当前食品添加到相应的分组
                $groupedFoods[$name][] = $v; // 将整个对象添加到其对应的组
            }
        }
        // 转换为所需格式
        $result = [];
        foreach ($groupedFoods as $name => $group) {
            $result[] = [
                'name' => $name,
                'data' => $group,
            ];
        }
        return $this->success($result);
    }


    /**
     * @Apidoc\Title("周套餐初始化")
     * @Apidoc\Url("/food/FoodPackage/init")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("start_time", type="date", require=false, desc="有效日期", default="")
     * @Apidoc\Query("end_time", type="date", require=false, desc="有效日期", default="")
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     * @throws Exception
     */
    public function init1(Request $request): Response
    {
        try {
            validate(FoodPackageValidate::class)
                ->scene('init')
                ->check($request->all());
        } catch (Exception $e) {
            // 验证失败 输出错误信息
            throw new BadRequestHttpException($e->getMessage());
        }
        $params = $request->only(['start_time', 'end_time']);
        $nowTime = date('Y-m-d H:i:s');
        // 判断是否时间正确
        if ($params['start_time'] > $params['end_time']) {
            throw new BadRequestHttpException('开始时间不能大于结束时间');
        }
        // 查询是否存在已下订单
        if($this->dao->has('order_food_item',[
            'delete_time' => null,
            'delivery_time[<=]' => $params['end_time'],
            'delivery_time[>=]' => $params['start_time'],
            'num[>]' => 0,
        ])) {
            throw new BadRequestHttpException('存在有效订单，禁止重置');
        }
        // 清空原有套餐
        $this->dao->update('food_package', [
            'delete_time' => $nowTime,
        ], [
            'delete_time' => null,
            'day_time[>=]' => $params['start_time'],
            'day_time[<=]' => $params['end_time'],
        ]);
        // 进行初始化
        $packageModel = new FoodPackage();
        $packageItem = new FoodPackageItem();
        // 进行初始化
        $diff = dateDiff($params['start_time'], $params['end_time']) + 1;
                if ($diff > 0) {
            for ($i = 0; $i < $diff; $i++) {
                $time = date('Y-m-d', strtotime($params['start_time']) + $i * 86400);
                // 本周主食专供添加
                // 获取上一周当天套餐
                $lastPackageList = $packageModel
                    ->where('day_time',date('Y-m-d',strtotime('-7 day',strtotime($time))))
                    ->where('type',0)
                    ->order('sort','ASC')
                    ->select()
                    ->toArray();
                if(!empty($lastPackageList)) {
                    // 查询上周主食专供食品 添加进当前套餐
                    foreach ($lastPackageList as $v) {
                        $packageNewId = $this->dao->insert('food_package', [
                            'name' => $v['name'],
                            'price' => $v['price'],
                            'config_id' => $v['config_id'],
                            'created_by' => $request->adminId,
                            'update_time' => $nowTime,
                            'create_time' => $nowTime,
                            'sort' => $v['sort'],
                            'day_time' => $time,
                        ]);
                        // 如果套餐为主食专供添加套餐商品
                        if(str_contains($v['name'],'主食专供')) {
                            $foodList = $packageItem->where('package_id',$v['package_id'])->select()->toArray();
                            foreach ($foodList as $vv) {
                                $this->dao->insert('food_package_item', [
                                    'food_id' => $vv['food_id'],
                                    'food_name' => $vv['food_name'],
                                    'food_cate' => $vv['food_cate'],
                                    'package_id' => $packageNewId,
                                    'create_time' => $nowTime,
                                    'update_time' => $nowTime,
                                    'created_by' => $request->adminId,
                                ]);
                            }
                        }
                    }
                } else {
                    // 查询默认生成套餐
                    $config = $this->dao->search('food_package_config', [
                        'status' => 1,
                        'delete_time' => null,
                        "ORDER" => [
                            "sort" => "ASC",
                        ],
                    ]);
                    if (!empty($config)) {
                        $configData = [];
                        foreach ($config as $v) {
                            $value = [];
                            $value['name'] = $v['name'];
                            $value['price'] = $v['price'];
                            $value['config_id'] = $v['config_id'];
                            $value['created_by'] = $request->adminId;
                            $value['update_time'] = $nowTime;
                            $value['create_time'] = $nowTime;
                            $value['sort'] = $v['sort'];
                            $value['day_time'] = $time;
                            $configData[] = $value;
                        }
                        // 添加套餐
                        $this->dao->insert('food_package', $configData);
                    }
                }
            }
        }
        return $this->success([], '已生成' . $diff . '天套餐数据');
    }

    /**
     * @Apidoc\Title("周套餐列表查询")
     * @Apidoc\Url("/food/FoodPackage/getPackageList")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("day_time", type="date", require=false, desc="有效日期", default="")
     * @Apidoc\Query("limit", type="int", require=false, desc="框架自带-每页数据,默认10", default="1000")
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     * @throws Exception
     */
    public function getPackageList(Request $request): Response
    {
        $where = $request->more([
            ['day_time', ''],
            ['type', 0],
        ]);
        $query = $this->logic->search($where);
        $data = $this->logic->getList($query);
        $data =  $data['data'];
        if (empty($data)) {
            if($where['type'] == 1) {
                // 创建照料套餐
                if(!empty($where['day_time'][0])) {
                    if ($where['day_time'][0] > $where['day_time'][1]) {
                        throw new BadRequestHttpException('开始时间不能大于结束时间');
                    }
                    $diff = dateDiff($where['day_time'][0], $where['day_time'][1]) + 1;
                    $nowTime = date('Y-m-d H:i:s');
                    for ($i = 0; $i < $diff; $i++) {
                        $time = date('Y-m-d', strtotime($where['day_time'][0]) + $i * 86400);
                        // 进行初始化
                        $value = [];
                        $value['name'] = '照料套餐';
                        $value['type'] = 1;
                        $value['price'] = 20;
                        $value['config_id'] = 55;
                        $value['created_by'] = $request->adminId;
                        $value['update_time'] = $nowTime;
                        $value['create_time'] = $nowTime;
                        $value['sort'] = 900;
                        $value['day_time'] = $time;
                        // 添加套餐
                        $value['package_id'] = $this->dao->insert('food_package', $value);
                        $data[] = $value;
                    }
                }
            } else{
                return $this->fail('请先设置周套餐');
            }
        }
        // 根据套餐名进行分组
        $groupedFoods = [];
        if (!empty($data)) {
            $weekdays = ['一', '二', '三', '四', '五', '六', '日']; // 中文星期数组
            foreach ($data as $v) {
                $name = $v['name'];
                // 初始化分组，如果尚不存在
                if (!isset($groupedFoods[$name])) {
                    $groupedFoods[$name] = []; // 创建一个空数组用于存放该组的数据
                }
                $v['foodName']  ='';
                if (str_contains($v['name'], '主食专供') || str_contains($v['name'], '特色面食')) {
                    // 返回一个菜品名称
                    $v['foodName']  = $this->dao->get('food_package_item',[ 'package_id' =>  $v['package_id'] ],'food_name');
                }
                $v['weekDay']  ='周' .$weekdays[date('N', strtotime($v['day_time']))-1];
                // 将当前食品添加到相应的分组
                $groupedFoods[$name][] = [
                    'name' => $v['name'],// 套餐名称
                    'price' => $v['price'],// 套餐价格
                    'packageId' => $v['package_id'],// 套餐编号
                    'dayTime' =>$v['day_time'],// 有效日期
                    'weekDay' => $v['weekDay'],// 星期几
                    'num' =>0,// 数量
                    'foodName' => $v['foodName'],// 菜品名称
                ]; // 将整个对象添加到其对应的组
            }
        }
        // // 转换为所需格式
        $result = [];
        foreach ($groupedFoods as $name => $group) {
            $result[] =  $group ;
        }
        return $this->success($result);
    }

    /**
     * 导入套餐接口
     * @Apidoc\Title("导入套餐")
     * @Apidoc\Url("/food/FoodPackage/importExcel")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("file", type="file", require=true, desc="文件", default="")
     * @param Request $request
     * @return Response
     */
    public function importExcel(Request $request): Response
    {
        $params = $request->only(['start_time', 'end_time']);
        if ($params['start_time'] > $params['end_time']) {
            throw new BadRequestHttpException('开始时间不能大于结束时间');
        }
        // 获取数据
        $data = Excel::readExcel([
            'A',
            'B',
            '0',
        ]);
        // 格式化数据 转为商品表、套餐表
        $timeArr = static::getTimeArr($params);
        foreach ($data as $k=>$v) {
            // A套餐
            if($k == 'A') {
                foreach ($v as $kk => $vv) {
                    // 周一
                    if($kk == 5 || $kk == 6) {
                        static::setPackageValue($timeArr[0],$vv[1],$vv);
                    }
                    // 周二
                    if($kk == 7 || $kk == 8) {
                        static::setPackageValue($timeArr[1],$vv[1],$vv);
                    }
                    // 周三
                    if($kk == 9 || $kk == 10) {
                        static::setPackageValue($timeArr[2],$vv[1],$vv);
                    }
                    // 周四
                    if($kk == 11 || $kk == 12) {
                        static::setPackageValue($timeArr[3],$vv[1],$vv);
                    }
                    // 周五
                    if($kk == 13 || $kk == 14) {
                        static::setPackageValue($timeArr[4],$vv[1],$vv);
                    }
                    // 周六
                    if($kk == 17 || $kk == 18) {
                        static::setPackageValue($timeArr[5],$vv[1],$vv);
                    }
                    // 周日
                    if($kk == 19 || $kk == 20) {
                        static::setPackageValue($timeArr[6],$vv[1],$vv);
                    }
                }
            }
            // B套餐
            if($k == 'B') {
                foreach ($v as $kk => $vv) {
                    // 周一
                    if($kk == 5 || $kk == 6) {
                        static::setPackageValue($timeArr[0],$vv[1],$vv);
                    }
                    // 周二
                    if($kk == 7 || $kk == 8) {
                        static::setPackageValue($timeArr[1],$vv[1],$vv);
                    }
                    // 周三
                    if($kk == 9 || $kk == 10) {
                        static::setPackageValue($timeArr[2],$vv[1],$vv);
                    }
                    // 周四
                    if($kk == 11 || $kk == 12) {
                        static::setPackageValue($timeArr[3],$vv[1],$vv);
                    }
                    // 周五
                    if($kk == 13 || $kk == 14) {
                        static::setPackageValue($timeArr[4],$vv[1],$vv);
                    }
                    // 周六
                    if($kk == 17 || $kk == 18) {
                        static::setPackageValue($timeArr[5],$vv[1],$vv);
                    }
                    // 周日
                    if($kk == 19 || $kk == 20) {
                        static::setPackageValue($timeArr[6],$vv[1],$vv);
                    }
                }
            }
            // 0套餐
            if($k == '0') {
                foreach ($v as $kk => $vv) {
                    // 周一
                    if($kk == 5 || $kk == 6) {
                        static::setPackageValue($timeArr[0],$vv[2],$vv);
                    }
                    // 周二
                    if($kk == 7 || $kk == 8) {
                        static::setPackageValue($timeArr[1],$vv[2],$vv);
                    }
                    // 周三
                    if($kk == 9 || $kk == 10) {
                        static::setPackageValue($timeArr[2],$vv[2],$vv);
                    }
                    // 周四
                    if($kk == 11 || $kk == 12) {
                        static::setPackageValue($timeArr[3],$vv[2],$vv);
                    }
                    // 周五
                    if($kk == 13 || $kk == 14) {
                        static::setPackageValue($timeArr[4],$vv[2],$vv);
                    }
                }
            }

        }
        return $this->success('导入成功');
    }

    /**
     * 返回日期
     * @param $params
     * @return array
     */
    public static function getTimeArr($params): array
    {
        $start_time = strtotime($params['start_time']);
        $end_time = strtotime($params['end_time']);
        $res = [];
        while ($start_time <= $end_time) {
            $res[] = date('Y-m-d',$start_time);
            $start_time = strtotime('+1 day', $start_time);
        }
        return $res;
    }

        /**
     * 设置套餐值
     * @param $time
     * @param $packageName
     * @param $vv
     * @return bool
     */
    public static function setPackageValue($time,$packageName,$vv): bool
    {
        $packageModel = new FoodPackage();
        $foodConfigModel = new FoodPackageConfig();
        // 查询套餐配置
        if($packageName == 1 || $packageName == 2) {
            $packageName = '零'.$packageName;
        }
        $configValue = $foodConfigModel->where('name',$packageName)->findOrEmpty()->toArray();
        if(empty($configValue)) {
            return false;
        }
        // 创建套餐
        $packageId = $packageModel->where('day_time',$time)->where('config_id',$configValue['config_id'])->value('package_id');
        if($packageName == 'A1' || $packageName == 'A2') {
            foreach ($vv as $kkk=>$vvv) {
                // A套餐
                if($kkk == 2) {
                    // 荤菜
                    static::setFood($packageId,'荤菜',$vvv);
                }
                if($kkk == 3 || $kkk == 4) {
                    // 素菜
                    static::setFood($packageId,'素菜',$vvv);
                }
                if($kkk == 5) {
                    // 主食
                    static::setFood($packageId,'主食',$vvv);
                }
                if($kkk == 6) {
                    // 汤粥
                    static::setFood($packageId,'汤粥',$vvv);
                }
            }
        }
        if($packageName == 'B1' || $packageName == 'B2') {
            foreach ($vv as $kkk=>$vvv) {
                // A套餐
                if($kkk == 2 || $kkk == 3) {
                    // 荤菜
                    static::setFood($packageId,'荤菜',$vvv);
                }
                if($kkk == 4 || $kkk == 5) {
                    // 素菜
                    static::setFood($packageId,'素菜',$vvv);
                }
                if($kkk == 6) {
                    // 主食
                    static::setFood($packageId,'主食',$vvv);
                }
                if($kkk == 7) {
                    // 汤粥
                    static::setFood($packageId,'汤粥',$vvv);
                }
            }
        }
        if($packageName == '零1' || $packageName == '零2') {
            foreach ($vv as $kkk=>$vvv) {
                // A套餐
                if($kkk == 3) {
                    // 荤菜
                    static::setFood($packageId,'荤菜',$vvv);
                }
                if($kkk == 4) {
                    // 素菜
                    static::setFood($packageId,'素菜',$vvv);
                }
                if($kkk == 5) {
                    // 主食
                    static::setFood($packageId,'主食',$vvv);
                }
                if($kkk == 6) {
                    // 汤粥
                    static::setFood($packageId,'汤粥',$vvv);
                }
            }
        }
        return true;
    }

    /**
     * 设置套餐食品
     * @param $packageId
     * @param $cateName
     * @param $foodName
     */
    public static function setFood($packageId,$cateName,$foodName): void
    {
        $foodModel = new FoodContent();
        $packageItem = new FoodPackageItem();
        // 查询商品是否存在
        $foodId = $foodModel->where('name',$foodName)->value('id');
        if(empty($foodId)) {
            $foodId = $foodModel->create([
                'name'=>$foodName,
                'cate_name'=>$cateName,
            ])->id;
        }
        // 查询套餐商品是否存在
        $packageItemId = $packageItem
            ->where('food_id',$foodId)
            ->where('package_id',$packageId)
            ->where('food_name',$foodName)
            ->value('id');
        if(empty($packageItemId)) {
            // 添加套餐商品
            $packageItem->create([
                'food_id'=>$foodId,
                'package_id'=>$packageId,
                'food_name'=>$foodName,
                'food_cate'=>$cateName,
            ]);
        }
    }
}
