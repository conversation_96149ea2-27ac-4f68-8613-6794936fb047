<?php

namespace app\controller\tool;

use app\cust\model\CustBalance;
use app\cust\model\CustBalanceRecord;
use app\domain\service\FoodOrderService;
use app\domain\service\SmartService;
use app\utils\NocoDbApi;
use app\utils\Water;
use app\validate\CommonValidate;
use Exception;
use Medoo\Medoo;
use plugin\saiadmin\utils\Cache;
use Ramsey\Uuid\Uuid;
use support\Redis;
use support\Request;
use support\Response;
use hg\apidoc\annotation as Apidoc;
use Tinywan\ExceptionHandler\Exception\BadRequestHttpException;
use Webman\Captcha\CaptchaBuilder;
use Webman\Captcha\PhraseBuilder;
use yzh52521\EasyHttp\Http;
use app\utils\Excel;


class SyncOrderController
{
    /**
     * 同步旧版微信支付订单
     */
    public function syncOldOrder(Request $request): Response
    {
        try {
            // 旧db
            $oldDb = \Webman\Medoo\Medoo::instance('v1');
            // 新db
            $newDb = \Webman\Medoo\Medoo::instance('default');

            // 计算上周一的日期
            $lastMonday = date('Y-m-d', strtotime('last monday', strtotime('-1 week')));

            // 配送点映射关系 (旧表值 => 新表值)
            $deliverySiteMapping = [
                '中关村黄庄社区801服务站' => '801服务点',
                '中关村黄庄社区803服务站' => '803服务点',
                '中关村科源社区养老驿站' => '海淀科源社区养老驿站',
                '中关村科星社区养老驿站' => '海淀科星社区养老驿站',
                '中关村科苑酒店' => '科苑酒店',
                '奥运村枫林绿洲社区服务站' => '枫林绿洲',
                '奥运村科学园社区南里三区站' => '科学院南里三区',
                '奥运村科学园社区南里五区站' => '科学院南里五区',
                '奥运村博世祥园社区站' => '博世祥园',
                '奥运村龙祥社区服务站' => '龙翔社区',
                '亚运村华严北里社区服务站' => '华严北里',
            ];

            // 查询旧版订单 food_order, 查询条件delivery_start_date >= 上周一
            $oldOrders = $oldDb->select('food_order', [
                'order_id',
                'user_id',
                'staff_id',
                'delivery_num',
                'delivery_option',
                'delivery_site',
                'package_order_time',
                'package_type',
                'payment_amount',
                'delivery_cost',
                'discount_setting',
                'discount_price',
                'remark',
                'created_by',
                'created_at',
                'user_longitude',
                'user_latitude',
                'user_address'
            ], [
                'delivery_start_date[>=]' => $lastMonday,
                'deleted_at' => null
            ]);

            $syncResults = [
                'total_orders' => count($oldOrders),
                'synced_orders' => 0,
                'created_users' => 0,
                'skipped_orders' => 0,
                'errors' => []
            ];

            foreach ($oldOrders as $oldOrder) {
                try {
                    // 处理用户映射
                    $userMappingResult = $this->handleUserMapping($oldDb, $newDb, $oldOrder['user_id']);
                    if (!$userMappingResult['cust_uid']) {
                        $syncResults['errors'][] = "订单 {$oldOrder['order_id']} 用户映射失败";
                        $syncResults['skipped_orders']++;
                        continue;
                    }

                    $custUid = $userMappingResult['cust_uid'];
                    if ($userMappingResult['is_new_user']) {
                        $syncResults['created_users']++;
                    }

                    // 检查新系统中是否已存在该订单
                    $existingOrder = $newDb->get('order_food', 'order_id', [
                        'cust_uid' => $custUid,
                        'delivery_time' => $oldOrder['package_order_time']
                    ]);

                    if ($existingOrder) {
                        // 订单已存在，检查数据是否匹配
                        $this->validateOrderData($newDb, $oldOrder, $existingOrder, $deliverySiteMapping);
                        $syncResults['skipped_orders']++;
                        continue;
                    }

                    // 同步订单数据
                    $this->syncOrderData($oldDb, $newDb, $oldOrder, $custUid, $deliverySiteMapping);
                    $syncResults['synced_orders']++;

                } catch (\Exception $e) {
                    $syncResults['errors'][] = "订单 {$oldOrder['order_id']} 同步失败: " . $e->getMessage();
                    $syncResults['skipped_orders']++;
                }
            }

            return $this->success($syncResults);

        } catch (\Exception $e) {
            return $this->fail('同步失败: ' . $e->getMessage());
        }
    }

    /**
     * 处理用户映射
     * @param $oldDb
     * @param $newDb
     * @param $oldUserId
     * @return array
     */
    private function handleUserMapping($oldDb, $newDb, $oldUserId)
    {
        // 首先检查cust_sync表中是否已存在该用户的映射关系
        $syncRecord = $newDb->get('cust_sync', [
            'old_cust_uid',
            'new_cust_uid'
        ], [
            'old_cust_uid' => $oldUserId
        ]);

        if ($syncRecord && $syncRecord['new_cust_uid']) {
            // 验证新用户是否真实存在
            $existingUser = $newDb->get('cust_user', 'cust_uid', [
                'cust_uid' => $syncRecord['new_cust_uid'],
                'delete_time' => null
            ]);

            if ($existingUser) {
                return [
                    'cust_uid' => $syncRecord['new_cust_uid'],
                    'is_new_user' => false
                ];
            } else {
                // 删除记录
                $newDb->delete('cust_sync', [
                    'old_cust_uid' => $oldUserId
                ]);
            }
        }

        // 如果不存在，从旧系统获取用户信息并创建新用户
        $oldUser = $oldDb->get('main_user', [
            'id',
            'v1', // 姓名
            'v11', // 手机号
            'v4', // 性别
            'v5', // 身份类别
            'v8', // 出生年月
            'v22', // 所属片区
            'v23', // 是否家床
            'v25', // 社区
            'v26', // 居住详细地址
            'v36', // 评估等级
            'v37', // 服务机构
            'created_by',
            'created_at'
        ], [
            'id' => $oldUserId,
            'deleted_at' => null
        ]);

        if (!$oldUser) {
            return [
                'cust_uid' => null,
                'is_new_user' => false
            ];
        }

        // 创建新用户，使用自增ID而不是旧系统ID
        $newUserData = [
            'cust_name' => $oldUser['v1'] ?? '',
            'cust_private_phone' => $oldUser['v11'] ?? '',
            'cust_sex' => $this->mapGender($oldUser['v4'] ?? ''),
            'cust_identity_type' => $this->mapIdentityType($oldUser['v5'] ?? ''),
            'cust_birth' => $oldUser['v8'] ?? null,
            'cust_area' => $oldUser['v22'] ?? '',
            'cust_is_bed' => $this->mapBedType($oldUser['v23'] ?? ''),
            'cust_community' => $oldUser['v25'] ?? '',
            'cust_live_address' => $oldUser['v26'] ?? '',
            'cust_evaluation_level' => $this->mapEvaluationLevel($oldUser['v36'] ?? ''),
            'cust_service_site' => $this->mapServiceSite($oldUser['v37'] ?? ''),
            'cust_city' => '{"province":"北京市","city":"市辖区","area":"海淀区","street":"中关村街道","community":"科源社区居委会"}',
            'cust_content' => '[{"label":"身份证正面","type":"upload","dict":[],"accept":"image","multiple":false,"value":null}]',
            'created_by' => $oldUser['created_by'] ?? 1,
            'create_time' => $oldUser['created_at'] ?? date('Y-m-d H:i:s')
        ];

        try {
            // 开始事务
            $newDb->pdo->beginTransaction();

            // 插入新用户并获取新的用户ID
            $newDb->insert('cust_user', $newUserData);
            $newCustUid = $newDb->id();

            // 创建用户映射记录
            $syncData = [
                'old_cust_uid' => $oldUser['id'],
                'new_cust_uid' => $newCustUid,
                'create_time' => date('Y-m-d H:i:s')
            ];
            $newDb->insert('cust_sync', $syncData);

            // 提交事务
            $newDb->pdo->commit();

            return [
                'cust_uid' => $newCustUid,
                'is_new_user' => true
            ];
        } catch (\Exception $e) {
            // 回滚事务
            $newDb->pdo->rollBack();
            return [
                'cust_uid' => null,
                'is_new_user' => false
            ];
        }
    }

    /**
     * 验证订单数据
     * @param $newDb
     * @param $oldOrder
     * @param $existingOrderId
     * @param $deliverySiteMapping
     */
    private function validateOrderData($newDb, $oldOrder, $existingOrderId, $deliverySiteMapping)
    {
        $existingOrder = $newDb->get('order_food', '*', [
            'order_id' => $existingOrderId
        ]);

        if (!$existingOrder) {
            return;
        }

        // 检查配送点映射
        $expectedDeliverySite = $deliverySiteMapping[$oldOrder['delivery_site']] ?? $oldOrder['delivery_site'];
        if ($existingOrder['delivery_site'] !== $expectedDeliverySite) {
            // 可以记录不匹配的情况，但不抛出异常
            error_log("订单 {$oldOrder['order_id']} 配送点不匹配: 期望 {$expectedDeliverySite}, 实际 {$existingOrder['delivery_site']}");
        }

        // 检查金额
        if (abs($existingOrder['pay_price'] - $oldOrder['payment_amount']) > 0.01) {
            error_log("订单 {$oldOrder['order_id']} 金额不匹配");
        }
    }

    /**
     * 同步订单数据
     * @param $oldDb
     * @param $newDb
     * @param $oldOrder
     * @param $custUid
     * @param $deliverySiteMapping
     */
    private function syncOrderData($oldDb, $newDb, $oldOrder, $custUid, $deliverySiteMapping)
    {
        // 获取旧订单的子项
        $oldOrderItems = $oldDb->select('food_order_item', [
            'order_id',
            'package_id',
            'package_name',
            'num',
            'price',
            'pay_price'
        ], [
            'order_id' => $oldOrder['order_id'],
            'deleted_at' => null
        ]);

        if (empty($oldOrderItems)) {
            throw new \Exception("订单 {$oldOrder['order_id']} 没有找到子项");
        }

        // 映射配送点 (旧表值 => 新表值)
        $deliverySite = $deliverySiteMapping[$oldOrder['delivery_site']] ?? $oldOrder['delivery_site'];

        // 计算总数量
        $totalNum = array_sum(array_column($oldOrderItems, 'num'));

        // 插入主订单
        $orderData = [
            'order_id' => $oldOrder['order_id'],
            'cust_uid' => $custUid,
            'employee_uid' => $oldOrder['staff_id'] ?? null,
            'delivery_num' => $oldOrder['delivery_num'] ?? 1,
            'delivery_option' => $oldOrder['delivery_option'] ?? '',
            'delivery_site' => $deliverySite,
            'delivery_time' => $oldOrder['package_order_time'],
            'package_type' => $oldOrder['package_type'] ?? '',
            'total_num' => $totalNum,
            'pay_price' => $oldOrder['payment_amount'] ?? 0,
            'sum_amount' => ($oldOrder['payment_amount'] ?? 0) + ($oldOrder['delivery_cost'] ?? 0),
            'cust_delivery_fee' => $oldOrder['delivery_cost'] ?? 0,
            'remark' => $oldOrder['remark'] ?? '',
            'order_status' => 1, // 默认已完成
            'discounts_text' => $oldOrder['discount_setting'] ?? '',
            'discounts_price' => $oldOrder['discount_price'] ?? 0,
            'created_by' => $oldOrder['created_by'] ?? 1,
            'create_time' => $oldOrder['created_at'] ?? date('Y-m-d H:i:s'),
            'user_longitude' => $oldOrder['user_longitude'] ?? null,
            'user_latitude' => $oldOrder['user_latitude'] ?? null,
            'user_address' => $oldOrder['user_address'] ?? ''
        ];

        $newDb->insert('order_food', $orderData);

        // 插入订单子项
        $orderItemsData = [];
        foreach ($oldOrderItems as $item) {
            $orderItemsData[] = [
                'order_id' => $oldOrder['order_id'],
                'cust_uid' => $custUid,
                'employee_uid' => $oldOrder['staff_id'] ?? null,
                'delivery_option' => $oldOrder['delivery_option'] ?? '',
                'delivery_site' => $deliverySite,
                'delivery_time' => $oldOrder['package_order_time'],
                'num' => $item['num'],
                'price' => $item['price'],
                'pay_price' => $item['pay_price'],
                'package_id' => $item['package_id'],
                'package_type' => $oldOrder['package_type'] ?? '',
                'package_name' => $item['package_name'],
                'created_by' => $oldOrder['created_by'] ?? 1,
                'create_time' => $oldOrder['created_at'] ?? date('Y-m-d H:i:s')
            ];
        }

        $newDb->insert('order_food_item', $orderItemsData);
    }

    /**
     * 映射性别
     * @param string $gender
     * @return int
     */
    private function mapGender($gender)
    {
        return match ($gender) {
            '男' => 1,
            '女' => 2,
            default => 0
        };
    }

    /**
     * 映射身份类别
     * @param string $identityType
     * @return int
     */
    private function mapIdentityType($identityType)
    {
        return match ($identityType) {
            '特困家庭' => 2,
            '低保' => 3,
            '计划生育特殊家庭' => 4,
            '中科院退休老人' => 7,
            '退伍军人' => 5,
            '其他' => 6,
            default => 1
        };
    }

    /**
     * 映射家床类型
     * @param string $bedType
     * @return int
     */
    private function mapBedType($bedType)
    {
        return match ($bedType) {
            '是', '家床' => 2,
            default => 3
        };
    }

    /**
     * 映射评估等级
     * @param string $evaluationLevel
     * @return int
     */
    private function mapEvaluationLevel($evaluationLevel)
    {
        return match ($evaluationLevel) {
            '轻度失能' => 2,
            '中度失能' => 3,
            '重度失能' => 4,
            default => 1
        };
    }

    /**
     * 映射服务机构
     * @param string $serviceSite
     * @return int
     */
    private function mapServiceSite($serviceSite)
    {
        return match ($serviceSite) {
            '龙翔社区', '奥运村龙祥社区服务站' => 13,
            '科苑酒店' => 12,
            '科苑家政服务中心' => 14,
            '科源驿站', '海淀科源社区养老驿站', '中关村科源社区养老驿站', '中关村科源社区养老服务驿站' => 3,
            '科学院南里五区', '奥运村科学园社区南里五区站' => 8,
            '海淀科星社区养老驿站', '中关村科星社区养老驿站' => 4,
            '奥运村科学园社区南里三区站' => 7,
            '奥运村枫林绿洲社区服务站' => 9,
            '亚运村华严北里社区服务站' => 11,
            '中关村黄庄社区803服务站' => 5,
            '中关村黄庄社区801服务站', '801服务点' => 6,
            default => 3
        };
    }

    /**
     * 成功响应
     * @param mixed $data
     * @param string $message
     * @return Response
     */
    private function success($data = [], $message = 'success')
    {
        return json([
            'code' => 200,
            'message' => $message,
            'data' => $data
        ]);
    }

    /**
     * 失败响应
     * @param string $message
     * @param int $code
     * @return Response
     */
    private function fail($message = 'error', $code = 400)
    {
        return json([
            'code' => $code,
            'message' => $message,
            'data' => []
        ]);
    }
}
