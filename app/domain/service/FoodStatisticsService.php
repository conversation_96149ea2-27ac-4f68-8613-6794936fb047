<?php

namespace app\domain\service;

use app\company\model\CompanyDepart;
use app\cust\model\CustUser;
use app\domain\model\Order;
use app\domain\model\OrderFood;
use app\domain\model\OrderFoodItem;
use app\food\model\FoodPackage;
use app\food\model\FoodPackageItem;
use app\employee\model\EmployeeUser;
use app\food\model\FoodPackageConfig;
use app\revenue\model\CustBill;
use app\utils\Dao;
use app\utils\Excel;
use Webman\Medoo\Medoo;
use plugin\saiadmin\app\model\system\SystemUser;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use Tinywan\ExceptionHandler\Exception\BadRequestHttpException;
use DateTime;
use Exception;

class FoodStatisticsService
{
    /**
     * 计算两个日期之间的时间差
     * @param $date1
     * @param $date2
     * @return int
     * @throws Exception
     */
    protected static function dateDiff($date1, $date2): int
    {
        $datetime1 = new DateTime($date1);
        $datetime2 = new DateTime($date2);
        $interval = $datetime1->diff($datetime2);
        return $interval->format('%a');
    }

    /**
     * 查询当天套餐名称
     * @param $time
     * @return array
     * @throws DbException
     */
    public static function getPackageName($time): array
    {
        $name = 'PACKAGE_NAME_' . $time;
        return getRedisCache($name, static function () use ($time) {
            $foodPackageModel = new FoodPackage();
            $packageItemModel = new FoodPackageItem();
            $data = $foodPackageModel->where('day_time', $time)->field('name,package_id')->order('sort')->select()->toArray();
            $value = [];
            if (!empty($data)) {
                foreach ($data as $v) {
                    $name = $v['name'];
                    if (str_contains($v['name'], '主食专供')) {
                        $res = $packageItemModel->where('package_id', $v['package_id'])->value('food_name');
                        if (!empty($res)) {
                            $name = $v['name'] . '(' . $res . ')';
                        }
                    }
                    $value[] = $name;
                }
            }
            return $value;
        }, 574560);
    }

    /**
     * 获取套餐数量
     * @param $packageId
     * @param $data
     * @return int|string
     */
    protected static function getPackageNum($packageId, $data): int|string
    {
        $nums = array_column($data, 'num', 'package_id');
        return $nums[$packageId] ?? '';
    }

    /**
     * 获取套餐数量
     * @param $packageId
     * @param $data
     * @return int|string
     */
    protected static function getNamePackageNum($name, $data): int|string
    {
        $nums = array_column($data, 'num', 'package_name');
        return $nums[$name] ?? '';
    }

    /**
     * 查询订餐信息
     * @throws DbException
     */
    public static function getOrderList(array $params): array
    {
        $orderModel = new OrderFood();
        $packageModel = new FoodPackage();
        $orderItemModel = new OrderFoodItem();
        // 查询数据
        $model = $orderModel->hasWhere('package', function ($query) {
            $query->where('num', '<>', 0);
        })->with([
            'createdByInfo',
            'custInfo',
            'employee',
            'package' => function ($query) {
                return $query->where('num', '<>', 0);
            }
        ])->when(isset($params['delivery_site']) && $params['delivery_site'] !== '', function ($query) use ($params) {
            $query->where('order_food.delivery_site', $params['delivery_site']);
        })->when(isset($params['delivery_option']) && $params['delivery_option'] !== '', function ($query) use ($params) {
            $query->where('order_food.delivery_option', $params['delivery_option']);
        })->when(isset($params['time']) && $params['time'] !== '', function ($query) use ($params) {
            $query->where('order_food.delivery_time', $params['time']);
        })
            ->order(['delivery_time']);
        $data['total'] = $model->count();
        // 判断是否分页
        if (!empty($params['page']) && !empty($params['limit'])) {
            $rows = $model->order(['update_time' => 'desc'])->page($params['page'], $params['limit'])
                ->select()
                ->toArray();
        } else {
            $rows = $model->order(['update_time' => 'desc'])
                ->select()
                ->toArray();
        }
        // 获取所有套餐信息
        $packageList = $packageModel->where('day_time', $params['time'])->field('name,package_id')->order('sort')->select()->toArray();
        // 取数据
        $data['row'] = [];
        $i = 1;
        // 支付总金额
        $payPriceTotal = 0;
        foreach ($rows as $v) {
            $value = [];
            // 序号
            $value['number'] = $i;
            $i++;
            // 供餐日期
            $value['delivery_time'] = $v['delivery_time'];
            // 供餐站点
            $value['delivery_site'] = $v['delivery_site'];
            // 用户信息
            if (!empty($v['custInfo'])) {
                $value['cust_name'] = $v['custInfo']['cust_name'];
                $value['cust_private_phone'] = $v['custInfo']['cust_private_phone'];
                $value['cust_live_address'] = $v['custInfo']['cust_live_address'];
            } else {
                $value['cust_name'] = '用户已删除';
                $value['cust_private_phone'] = '用户已删除';
                $value['cust_live_address'] = '用户已删除';
            }
            // 取餐方式
            $value['delivery_option'] = $v['delivery_option'];
            // 设置套餐详情
            $packageText = '';
            if (!empty($params['page']) && !empty($params['limit'])) {
                foreach ($v['package'] as $vv) {
                    $packageText .= $vv['package_name'] . ':' . $vv['num'] . '份 ';
                }
                $value['packageText'] = $packageText;
            } else {
                foreach ($packageList as $vv) {
                    $value[$vv['name']] = self::getPackageNum($vv['package_id'], $v['package']);
                }
                $payPriceTotal += $v['pay_price'];
            }
            // 金额
            $value['pay_price'] = $v['pay_price'];
            // 备注
            $value['remark'] = $v['remark'];

            $data['row'][] = $value;
        }
        // 获取总金额
        if (empty($params['page'])) {
            $packageNameArray = array_column($packageList, 'package_id');
            // 遍历查询
            $totalAmount = [];
            foreach ($packageNameArray as $v) {
                $totalAmount[] = $orderItemModel->when(isset($params['time']) && $params['time'], function ($query) use ($params) {
                    return $query->where('delivery_time', $params['time']);
                })->when(isset($params['delivery_site']) && $params['delivery_site'], function ($query) use ($params) {
                    return $query->where('delivery_site', $params['delivery_site']);
                })->when(isset($params['delivery_option']) && $params['delivery_option'], function ($query) use ($params) {
                    return $query->where('delivery_option', $params['delivery_option']);
                })
                    ->where('delivery_time', $params['time'])
                    ->where('package_id', $v)
                    ->sum('num');
            }
            $totalAmount[] = $payPriceTotal;
            $data['totalAmount'] = $totalAmount;
            $data['packageName'] = self::getPackageName($params['time']);
        }
        return $data;
    }

    /**
     * 获取送餐名单-天
     * @param $params
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function getDeliveryList($params): array
    {
        // 添加where条件
        $where = [
            ['order_food.delete_time', '=', null],
            ['order_food.delivery_option', '=', '送餐'],
        ];
        if (!empty($params['employee_uid'])) {
            $where[] = ['order_food.employee_uid', '=', $params['employee_uid']];
        }
        if (!empty($params['time'])) {
            $where[] = ['order_food.delivery_time', '=', $params['time']];
        }
        // 获取模型
        $orderModel = new OrderFood();
        $packageModel = new FoodPackage();
        $orderItemModel = new OrderFoodItem();
        $model = $orderModel
            ->hasWhere('package', function ($query) {
                $query->where('num', '<>', 0);
            })->with([
                'createdByInfo',
                'custInfo',
                'employee',
                'package' => function ($query) {
                    return $query->where('num', '<>', 0);
                }
            ])
            ->where($where)
            ->order(['order_food.delivery_time']);
        $data['total'] = $model->count();
        // 判断是否分页
        if (!empty($params['page']) && !empty($params['limit'])) {
            $rows = $model->order(['user_address' => 'desc'])->page($params['page'], $params['limit'])
                ->select()
                ->toArray();
        } else {
            $rows = $model->order(['user_address' => 'desc'])
                ->select()
                ->toArray();
        }
        // 获取所有套餐信息
        $packageList = $packageModel->where('day_time', $params['time'])->field('name,package_id')->order('sort')->select()->toArray();
        // 取数据
        $data['row'] = [];
        $i = 1;
        // 支付总金额
        $payPriceTotal = 0;
        foreach ($rows as $v) {
            $value = [];
            // 序号
            $value['number'] = $i;
            $i++;
            // 送餐时间
            $value['delivery_time'] = $v['delivery_time'];
            // 送餐员
            $value['employee_name'] = $v['employee']['employee_name'];
            // 用户信息
            if (!empty($v['custInfo'])) {
                $value['cust_name'] = $v['custInfo']['cust_name'];
                $value['cust_private_phone'] = $v['custInfo']['cust_private_phone'];
                $value['cust_live_address'] = $v['custInfo']['cust_live_address'];
            } else {
                $value['cust_name'] = '用户已删除';
                $value['cust_private_phone'] = '用户已删除';
                $value['cust_live_address'] = '用户已删除';
            }
            // 金额
            $deliveryCost = round($v['cust_delivery_fee'] * 0.9, 2);
            // 设置套餐详情
            $packageText = '';
            if (!empty($params['page']) && !empty($params['limit'])) {
                foreach ($v['package'] as $vv) {
                    $packageText .= $vv['package_name'] . ':' . $vv['num'] . '份 ';
                }
                $value['packageText'] = $packageText;
            } else {
                foreach ($packageList as $vv) {
                    $value[$vv['name']] = self::getPackageNum($vv['package_id'], $v['package']);
                }
                $payPriceTotal += $deliveryCost;
            }
            $value['pay_price'] = $deliveryCost;
            // 订餐人
            $value['createBy'] = $v['createdByInfo']['nickname'];
            $data['row'][] = $value;
        }
        // 获取总金额
        if (empty($params['page'])) {
            $packageNameArray = array_column($packageList, 'package_id');
            // 遍历查询
            $totalAmount = [];
            foreach ($packageNameArray as $v) {
                $totalAmount[] = $orderItemModel->when(isset($params['time']) && $params['time'], function ($query) use ($params) {
                    return $query->where('delivery_time', $params['time']);
                })->when(isset($params['employee_uid']) && $params['employee_uid'], function ($query) use ($params) {
                    return $query->where('employee_uid', $params['employee_uid']);
                })
                    ->where('delivery_option', '送餐')
                    ->where('package_id', $v)->sum('num');
            }
            $totalAmount[] = $payPriceTotal;
            $data['totalAmount'] = $totalAmount;
            $data['packageName'] = self::getPackageName($params['time']);
        }
        return $data;
    }

    /**
     * 获取送餐名单-月
     * @param $params
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function getDeliveryMonthList($params): array
    {
        // 添加where条件
        $where = [
            ['order_food.delete_time', '=', null],
            ['order_food.delivery_option', '=', '送餐'],
        ];
        if (!empty($params['employee_uid'])) {
            $where[] = ['order_food.employee_uid', '=', $params['employee_uid']];
        }
        if (!empty($params['time'])) {
            // 月份转换
            $where[] = ['order_food.delivery_time', '>=', $params['time'][0]];
            $where[] = ['order_food.delivery_time', '<=', $params['time'][1]];
        }
        // 获取模型
        $orderModel = new OrderFood();
        $model = $orderModel
            ->hasWhere('package', function ($query) {
                $query->where('num', '<>', 0);
            })->with([
                'createdByInfo',
                'custInfo',
                'employee',
                'package' => function ($query) {
                    return $query->where('num', '<>', 0);
                }
            ])
            ->where($where)
            ->order(['order_food.delivery_time']);
        $data['total'] = $model->count();
        // 判断是否分页
        if (!empty($params['page']) && !empty($params['limit'])) {
            $rows = $model->order(['user_address' => 'desc'])->page($params['page'], $params['limit'])
                ->select()
                ->toArray();
        } else {
            $rows = $model->order(['user_address' => 'desc'])
                ->select()
                ->toArray();
        }
        // 取数据
        $data['row'] = [];
        $i = 1;
        // 送餐费用总金额
        $payPriceTotal = 0;
        foreach ($rows as $v) {
            $value = [];
            // 金额
            $deliveryCost = round($v['cust_delivery_fee'] * 0.9, 2);
            $payPriceTotal +=  $deliveryCost;
            // 序号
            $value['number'] = $i;
            $i++;
            // 送餐时间
            $value['delivery_time'] = $v['delivery_time'];
            // 服务站点
            $value['delivery_site'] = $v['delivery_site'];
            // 用户信息
            if (!empty($v['custInfo'])) {
                $value['cust_name'] = $v['custInfo']['cust_name'];
                $value['cust_live_address'] = $v['custInfo']['cust_live_address'];
            } else {
                $value['cust_name'] = '用户已删除';
                $value['cust_live_address'] = '用户已删除';
            }
            // 送餐员
            $value['employee_name'] = !empty($v['employee']) ? $v['employee']['employee_name'] : '无名称';
            // 送餐数量
            $value['packageText'] = $v['delivery_num'];
            // 金额
            $value['paymentAmount'] = $deliveryCost;
            // 备注
            $value['remark'] = $v['remark'];
            // 订餐人
            $value['createBy'] = $v['createdByInfo']['nickname'];
            $data['row'][] = $value;
        }
        // 获取总金额
        if (empty($params['page'])) {
            $data['totalAmount'] = $payPriceTotal;
        }
        return $data;
    }

    /**
     * 获取送餐统计总表
     * @param $params
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function getTotalDelivery($params): array
    {
        $orderModel = new OrderFood();
        $foodPackage = new FoodPackage();
        // 添加where条件
        $where = [
            ['order_food.delete_time', '=', null],
            ['order_food.delivery_option', '=', '送餐'],
            ['order_food.delivery_time', '>=', $params['start_time']],
            ['order_food.delivery_time', '<=', $params['end_time']],
            ['order_food.pay_price', '>', 0],
        ];
        // 查询送餐员
        $staffInfo = $orderModel->where($where)->Distinct(true)->column('employee_uid');
        // 查询套餐类型
        $configArr = $foodPackage->where('day_time', '=', $params['end_time'])
            ->order('sort', 'ASC')
            ->field('name,price')
            ->select()
            ->toArray();
        // 处理数据
        $result = [];
        foreach ($staffInfo as $v) {
            $res = [];
            // 查询送餐员信息
            $data = $orderModel
                ->with([
                    'custInfo',
                    'employee',
                    'createdByInfo',
                    'package' => function ($query) {
                        return $query->where('num', '<>', 0);
                    }
                ])
                ->where($where)
                ->where('order_food.employee_uid', $v)
                ->order(['order_food.employee_uid', 'order_food.delivery_time'])
                ->select()
                ->toArray();
            $res['employee_name'] = '';
            $res['data'] = [];
            $res['total'] = [];
            // 获取��餐类型
            $menuArr = [];
            foreach ($configArr as $vv) {
                $menuArr[$vv['name']] = 0;
            }
            foreach ($data as $vv) {
                $value = [];
                // 订单号
                $value['order_id'] = $vv['order_id'];
                // 订单日期
                $value['delivery_time'] = $vv['delivery_time'];
                // 姓名
                if (empty($vv['custInfo'])) {
                    $vv['custInfo']['cust_name'] = '用户已删除';
                    $vv['custInfo']['cust_evaluation_level'] = '未录入';
                    $vv['custInfo']['cust_live_address'] = '用户已删除';
                }
                $value['name'] = $vv['custInfo']['cust_name'];
                // 自理程度
                $value['health'] = $vv['custInfo']['cust_evaluation_level'];
                // 套餐
                $packageText = '';
                foreach ($vv['package'] as $vvv) {
                    $packageText .= $vvv['package_name'] . ':' . $vvv['num'] . '份 ';
                    $menuArr[$vvv['package_name']] = $menuArr[$vvv['package_name']] + $vvv['num'];
                }
                $value['packageText'] = $packageText;
                // 地址
                $value['address'] = $vv['custInfo']['cust_live_address'];
                // 订餐人
                $value['createBy'] = $vv['createdByInfo']['nickname'];
                // 送餐员是否添加
                if (!empty($vv['employee'])) {
                    $value['employee_name'] = $vv['employee']['employee_name'];
                    $res['employee_name'] = $vv['employee']['employee_name'];
                } else {
                    $value['employee_name'] = '送餐员未配置';
                    $res['employee_name'] = '送餐员未配置';
                }
                // 取餐点
                $value['site'] = $vv['delivery_site'];
                // 备注
                $value['remark'] = $vv['remark'] ?? '无';
                $res['data'][] = $value;
                $res['total'] = $menuArr;
            }
            $result[] = $res;
        }
        return $result;
    }

    /**
     * 获取站点送餐总列表
     * @param $params
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function getSiteStatistics($params): array
    {
        $siteModel = new CompanyDepart();
        $orderItemModel = new OrderFoodItem();
        $foodPackage = new FoodPackage();
        // 查询套餐类型
        $configArr = $foodPackage->where('day_time', '=', $params['end_time'])
            ->order('sort', 'ASC')
            ->field('name,price')
            ->select()
            ->toArray();
        // 查询站点
        $siteData = $siteModel->whereLike('company_label', '%801营养餐站点%')->column('company_name');
        // 查询套餐
        $where = [
            ['num', '>', 0],
            ['delivery_time', '>=', $params['start_time']],
            ['delivery_time', '<=', $params['end_time']],
        ];
        // 查询套餐类型
        $orderItem = $orderItemModel
            ->where($where)
            ->select()
            ->order('day_time', 'desc')
            ->toArray();
        // 获取数据
        // 获取套餐类型
        $menuArr = [];
        foreach ($configArr as $vv) {
            $menuArr[$vv['name']] = [
                '总数' => 0,
                '员工餐' => 0,
            ];
        }
        // 获取站点
        $siteArr = [];
        $cache = [];
        foreach ($siteData as $kk => $vv) {
            $value = $menuArr;
            $value['站点名称'] = $vv;
            $value['日期'] = $params['start_time'];
            $siteArr[] = $value;
            $cache[$vv] = $kk;
        }
        // 计数
        foreach ($orderItem as $v) {
            $key = $cache[$v['delivery_site']];
            // 获取套餐名
            $name = $v['package_name'];
            if (empty($siteArr[$key][$name])) {
                continue;
            }
            $siteArr[$key][$name]['总数'] = $siteArr[$key][$name]['总数'] + $v['num'];
            // 计算员工餐
            if (!empty($v['orderInfo']) && $v['package_type'] == '工作餐') {
                $siteArr[$key][$name]['员工餐'] = $siteArr[$key][$name]['员工餐'] + $v['num'];
            }
        }
        // 更新主食名称
        $foodPackageModel = new FoodPackage();
        $foodPackageItemModel = new FoodPackageItem();
        
        // 预先获取所有主食专供套餐的映射关系
        $packageMap = [];
        $packageNames = [];
        foreach($siteArr as $v) {
            foreach($v as $kk => $vv) {
                if(str_contains($kk, '主食专供')) {
                    $packageNames[] = $kk;
                }
            }
        }
        
        if(!empty($packageNames)) {
            $packages = $foodPackageModel
                ->where('day_time', $params['start_time'])
                ->where('name', 'in', array_unique($packageNames))
                ->column('package_id', 'name');
                
            if(!empty($packages)) {
                $foodNames = $foodPackageItemModel
                    ->where('package_id', 'in', array_values($packages))
                    ->column('food_name', 'package_id');
                    
                foreach($packages as $name => $id) {
                    if(isset($foodNames[$id])) {
                        $packageMap[$name] = $foodNames[$id];
                    }
                }
            }
        }
        
        // 使用映射关系重组数据
        $newSiteArr = [];
        foreach ($siteArr as $k => $v) {
            $value = [];
            foreach ($v as $kk => $vv) {
                if(str_contains($kk, '主食专供') && isset($packageMap[$kk])) {
                    $value[$packageMap[$kk]] = $vv;
                } else {
                    $value[$kk] = $vv;
                }
            }
            $newSiteArr[$k] = $value;
        }
        return $newSiteArr;
    }

    /**
     * 获取站点送餐总列表-导出使用
     * @param $params
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function getSiteStatisticsV2($params): array
    {
        $siteModel = new CompanyDepart();
        $orderItemModel = new OrderFoodItem();
        $foodPackage = new FoodPackage();
        // 查询套餐类型
        $configArr = $foodPackage->where('day_time', '=', $params['end_time'])
            ->order('sort', 'ASC')
            ->field('name,price')
            ->select()
            ->toArray();
        // 查询站点
        $siteData = $siteModel->whereLike('company_label', '%801营养餐站点%')->column('company_name');
        // 查询套餐
        $where = [
            ['num', '>', 0],
            ['delivery_time', '>=', $params['start_time']],
            ['delivery_time', '<=', $params['end_time']],
        ];
        // 查询套餐类型
        $orderItem = $orderItemModel
            ->where($where)
            ->select()
            ->order('day_time', 'desc')
            ->toArray();
        // 获取数据
        // 获取套餐类型
        $menuArr = [];
        foreach ($configArr as $kk => $vv) {
            if($kk == 0) {
                $menuArr['站点名称'] = '';
                $menuArr['日期'] = '';
            }
            $menuArr[$vv['name']] = 0;
        }
        // 获取站点
        $siteArr = [];
        $cache = [];
        foreach ($siteData as $kk => $vv) {
            $value = $menuArr;
            $value['站点名称'] = $vv;
            $value['日期'] = $params['start_time'];
            $siteArr[] = $value;
            $cache[$vv] = $kk;
        }
        // 计数
        foreach ($orderItem as $v) {
            $key = $cache[$v['delivery_site']];
            // 获取套餐名
            $name = $v['package_name'];
            if (!isset($siteArr[$key][$name])) {
                continue;
            }
            $siteArr[$key][$name] = $siteArr[$key][$name] + $v['num'];
        }
        return $siteArr;
    }

    /**
     *导出表函数
     * @param $params
     * @return string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws BadRequestHttpException
     */
    public static function exportHotel($params): string
    {
        $data = [];
        // 获取送餐统计数据
        $table = [];
        $staffData = static::getTotalDelivery($params);
        $table['title'] = '送餐统计总表';
        $table['header'] = [
            'id号',
            '日期',
            '姓名',
            '自理程度',
            '套餐详情',
            '地址',
            '订餐人',
            '送餐员',
            '取餐点',
            '备注'
        ];
        $export = [];
        foreach ($staffData as $v) {
            $endArr = ['合计', '送餐员:' . $v['employee_name']];
            foreach ($v['total'] as $kk => $vv) {
                $endArr[] = $kk . ':' . $vv;
            }
            $export = array_merge($export, $v['data']);
            $export[] = $endArr;
        }
        $table['export'] = $export;
        $data[] = $table;
        // 获取统计数据
        $table = [];
        $siteData = static::getSiteStatisticsV2($params);
        $table['title'] = '餐点发餐统计总表';
        $export = [];
        $header = [];
        foreach ($siteData as $k => $v) {
            $value = [];
            if ($k == 0) {
                foreach ($v as $kk => $vv) {
                    $header[] = $kk;
                    $value[] = $vv;
                }
            } else {
                foreach ($v as $vv) {
                    $value[] = $vv;
                }
            }
            $export[] = $value;
        }
        $table['header'] = $header;
        $table['export'] = $export;
        $data[] = $table;
        $title = '酒店数据统计_' . date('YmdHis') . '.xlsx';
        return Excel::exportTables($title, $data);
    }

    /**
     * 订餐人数据统计接口
     * @param $params
     * @return array
     * @throws Exception
     */
    public static function getOrderPersonList($params): array
    {
        // 查询该用户的订餐数量
        $foodOrderModel = new OrderFood();
        $sysUserModel = new SystemUser();
        // 计算时间差
        $diff = self::dateDiff($params['start_time'], $params['end_time']);
        if ($diff == 0) {
            // 查询日数据
            $model = $foodOrderModel->hasWhere('package', function ($query) {
                $query->where('num', '<>', 0);
            })->with([
                'createdByInfo',
                'custInfo',
                'package' => function ($query) {
                    return $query->where('num', '<>', 0);
                }
            ])->when(!empty($params['id']), function ($query) use ($params) {
                $query->where('order_food.created_by', '=', $params['id']);
            })->when(!empty($params['start_time']), function ($query) use ($params) {
                $query->where('order_food.delivery_time', '=', $params['start_time']);
            })
                ->order(['order_food.cust_uid', 'delivery_time']);
            $count = 0;
            if (!empty($params['page'])) {
                $count = $model->count();
                $data = $model->page($params['page'], $params['limit'])
                    ->select()
                    ->toArray();
            } else {
                $data = $model->select()->toArray();
            }
            $res = [];
            if (!empty($data)) {
                // 处理数据
                foreach ($data as $v) {
                    $value = [];
                    $value['日期'] = $v['delivery_time'];
                    if (!empty($v['custInfo'])) {
                        $value['姓名'] = $v['custInfo']['cust_name'];
                        $value['地址'] = $v['custInfo']['cust_live_address'];
                    } else {
                        $value['姓名'] = '';
                        $value['地址'] = '';
                    }
                    $packageText = '';
                    foreach ($v['package'] as $vv) {
                        $packageText .= $vv['package_name'] . ':' . $vv['num'] . '份 ';
                    }
                    $value['订餐详情'] = $packageText;
                    $value['取餐方式'] = $v['delivery_option'];
                    $value['订餐人'] = '';
                    if (!empty($v['createdByInfo'])) {
                        $value['订餐人'] = $v['createdByInfo']['nickname'];
                    }
                    $res[] = $value;
                }
            }
            if (!empty($params['page'])) {
                return [
                    'rows' => $res,
                    'total' => $count
                ];
            }
            return $res;
        }
        // 查询订餐人 总金额
        if ($diff >= 100) {
            $timeName = date('Y', strtotime($params['start_time']));
        } else {
            $timeName = date('Y-m', strtotime($params['start_time']));
        }
        // 以创建人分组查询数量
        $arr = $foodOrderModel->where('delivery_num', '>', 0)->when(!empty($params['id']), function ($query) use ($params) {
            $query->where('created_by', '=', $params['id']);
        })->when(!empty($params['start_time']), function ($query) use ($params) {
            $query->where('delivery_time', '>=', $params['start_time']);
        })->when(!empty($params['end_time']), function ($query) use ($params) {
            $query->where('delivery_time', '<=', $params['end_time']);
        })->group('created_by')->field('created_by,SUM(delivery_num) as sum_num,SUM(pay_price) as sum_price')->select()->toArray();
        $data = [];
        if (!empty($arr)) {
            foreach ($arr as $v) {
                $value['日期'] = $timeName;
                $value['订餐人姓名'] = $sysUserModel->where('id', '=', $v['created_by'])->value('nickname');
                if ($value['订餐人姓名'] == '微信小程序支付自动审核') {
                    $value['订餐人姓名'] = '微信小程序下单';
                }
                $value['订餐份数'] = $v['sum_num'];
                $value['总金额'] = $v['sum_price'];
                $data[] = $value;
            }
        }
        return $data;
    }

    /**
     *查询订单人统计明细数据
     * @param $params
     * @return array
     * @throws Exception
     */
    public static function getOrderPersonDetail($params): array
    {
        $foodOrderModel = new OrderFood();
        $sysUserModel = new SystemUser();
        // 计算时间差
        $diff = self::dateDiff($params['start_time'], $params['end_time']);
        // 查询订餐人 总金额
        if ($diff >= 100) {
            $timeName = date('Y', strtotime($params['start_time']));
        } elseif ($diff == 1) {
            $timeName = $params['start_time'];
        } else {
            $timeName = date('Y-m', strtotime($params['start_time']));
        }
        // 以创建人分组查询数量
        $model = $foodOrderModel->where('delivery_time', '>=', $params['start_time'])
            ->where('delivery_time', '<=', $params['end_time'])
            ->when(!empty($params['id']), function ($query) use ($params) {
                $query->where('order_food.created_by', '=', $params['id']);
            })
            ->where('delivery_num', '>', 0)
            ->group('order_food.created_by')
            ->group('cust_uid')
            ->field('created_by,SUM(delivery_num) as sum_num,cust_uid,SUM(pay_price) as sum_payment,SUM(cust_delivery_fee) as sum_delivery_cost');
        $count = 0;
        if (!empty($params['page'])) {
            $count = $model->count();
            $orderInfo = $model->page($params['page'], $params['limit'])
                ->select()
                ->toArray();
        } else {
            $orderInfo = $model->select()->toArray();
        }
        $data = [];
        if (!empty($orderInfo)) {
            $userModel = new CustUser();
            foreach ($orderInfo as $v) {
                $value = [];
                // 查询用户信息
                $userInfo = $userModel->where('cust_uid', $v['cust_uid'])->field('cust_name,cust_evaluation_level')->findOrEmpty()->toArray();
                $value['time'] = $timeName;
                $value['userName'] = '';
                $value['v36'] = '';
                if (!empty($userInfo)) {
                    $value['userName'] = $userInfo['cust_name'];
                    $value['v36'] = $userInfo['cust_evaluation_level'];
                }
                $value['number'] = $v['sum_num'];
                $value['delivery_option'] = '自取';
                if ($v['sum_delivery_cost'] > 0) {
                    $value['delivery_option'] = '送餐';
                }
                $value['sumTotalPayment'] = $v['sum_payment'] + $v['sum_delivery_cost'];
                $value['createdBy'] = $sysUserModel->where('id', $v['created_by'])->value('nickname');
                $data[] = $value;
            }
        }
        if (!empty($params['page'])) {
            return [
                'rows' => $data,
                'total' => $count
            ];
        }
        return $data;
    }

    /**
     * 获取财务报表
     * @param $params
     * @return array
     * @throws DbException
     */
    public static function getTable($params): array
    {
        $orderItemModel = new OrderFoodItem();
        $orderModel = new OrderFood();
        $dao = new Dao();

        // 获取所有套餐类型
        $packageTypes = [
            ['classify' => '套餐', 'name' => 'A餐'],
            ['classify' => '套餐', 'name' => 'B餐'],
            ['classify' => '套餐', 'name' => '零餐'],
            ['classify' => '套餐', 'name' => '周末餐1'],
            ['classify' => '套餐', 'name' => '周末餐2'],
            ['classify' => '面食', 'name' => '馒头'],
            ['classify' => '面食', 'name' => '全麦'],
            ['classify' => '面食', 'name' => '鲜肉包'],
            ['classify' => '面食', 'name' => '发糕'],
            ['classify' => '面食', 'name' => '豆包'],
            ['classify' => '面食', 'name' => '肉馅烧饼'],
            ['classify' => '面食', 'name' => '芝麻烧饼'],
            ['classify' => '面食', 'name' => '素团子'],
            ['classify' => '面食', 'name' => '肉团子/猪肉包'],
            ['classify' => '面食', 'name' => '肉饼'],
            ['classify' => '水饺', 'name' => '猪肉大葱'],
            ['classify' => '水饺', 'name' => '猪肉三鲜'],
            ['classify' => '水饺', 'name' => '猪肉茴香'],
            ['classify' => '水饺', 'name' => '猪肉白菜'],
            ['classify' => '照料套餐', 'name' => '照料套餐'],
            ['classify' => '单点', 'name' => '龙翔单点'],
            ['classify' => '年夜饭', 'name' => '年夜饭588元'],
            ['classify' => '年夜饭', 'name' => '年夜饭888元'],
            ['classify' => '运费', 'name' => '运费'],
            ['classify' => '政府补贴', 'name' => '政府补贴'],
            ['classify' => '员工餐优惠', 'name' => '员工餐优惠'],
            ['classify' => '其他', 'name' => '其他'],
        ];

        // 初始化结果数组
        $result = array_map(function($item) {
            return [
                'classify' => $item['classify'],
                'name' => $item['name'],
                'price' => 0,
                'num1' => 0,
                'money1' => 0.00,
                'num2' => 0,
                'money2' => 0.00,
                'num3' => 0,
                'money3' => 0.00,
                'num4' => 0,
                'money4' => 0.00
            ];
        }, $packageTypes);

        // 基础查询条件
        $timeRange = [
            ['delivery_time', '>=', $params['start_time']],
            ['delivery_time', '<=', $params['end_time']]
        ];

        // 获取订单数据
        $orderData = $orderItemModel->where(array_merge([
                ['delete_time', '=', null],
                ['num', '>', 0]
            ], $timeRange))
            ->field(['package_name', 'num', 'price', 'pay_price', 'created_by', 'package_id', 'delivery_time'])
            ->select()
            ->toArray();

        // 处理订单数据
        foreach ($orderData as $order) {
            if(str_contains($order['package_name'], '主食专供')) {
                $packageName = $dao->get('food_package_item', ['package_id' => $order['package_id']], 'food_name');
                $key = match($packageName) {
                    '面肥馒头1个' => 5, '全麦馒头1个' => 6, '鲜肉包1个' => 7, '小枣发糕','紫米发糕','紫米葡萄干发糕' => 8, '豆包1个' => 9,
                    '肉馅烧饼1个' => 10, '芝麻烧饼1个' => 11, '素馅菜团子1个' => 12, '肉馅菜团子1个','梅菜猪肉包1个','猪肉白菜包1个' => 13,'肉饼1个' => 14, 
                    '猪肉大葱水饺' => 15, '猪肉三鲜水饺' => 16, '猪肉茴香水饺' => 17, '猪肉白菜水饺' => 18, 
                    '年夜饭588元' => 21, '年夜饭888元' => 22, default => 26
                };
            } else {
                $key = match($order['package_name']) {
                    'B1','B2' => 1, '零1','零2' => 2, '周末餐1' => 3,
                    '周末餐2' => 4, '照料套餐' => 19, default => 0
                };
            }

            if ($result[$key]['price'] == 0) {
                $result[$key]['price'] = $order['price'];
            }
            
            $siteIndex = match($order['created_by']) {
                46=>2, 48 => 3, 51 => 4, 65 => 4, default => 1
            };
            
            $result[$key]['num'.$siteIndex] += $order['num'];
            $result[$key]['money'.$siteIndex] += $order['pay_price'];
        }

        // 处理龙翔单点数据
        $billInfo = (new CustBill())->where([
                ['create_time', '>=', $params['start_time'].' 00:00:00'],
                ['create_time', '<=', $params['end_time'].' 23:59:59'],
                ['service_type', '=', '龙翔单点']
            ])
            ->whereNotIn('payment_status', ['已取消', '已作废', '已驳回'])
            ->field('COUNT(payment_id) as num,SUM(amount_paid) as pay_price')
            ->findOrEmpty()
            ->toArray();

        if(!empty($billInfo)) {
            $result[20]['num4'] += $billInfo['num'];
            $result[20]['money4'] += $billInfo['pay_price'];
        }

        // 处理送餐费和政府补贴数据
        $siteConfigs = [
            ['index' => 1, 'users' => [57]],
            ['index' => 2, 'users' => [46, 48]],
            ['index' => 3, 'users' => [51, 65]]
        ];

        foreach($siteConfigs as $config) {
            // 送餐费
            $deliveryData = $orderModel->where(array_merge($timeRange, [
                    ['pay_price', '>', 0],
                    ['delivery_option', '=', '送餐']
                ]))
                ->whereIn('created_by', $config['users'])
                ->field([
                    'SUM(cust_delivery_fee) as fee',
                    'SUM(delivery_num) as num'
                ])
                ->find();

            if($deliveryData['fee'] > 0) {
                $result[23]['money'.$config['index']] += $deliveryData['fee'];
            }

            // 政府补贴
            $govData = $orderModel->where(array_merge($timeRange, [
                    ['pay_price', '>', 0],
                    ['gov_fee', '>', 0]
                ]))
                ->whereIn('created_by', $config['users'])
                ->field([
                    'SUM(gov_fee) as fee',
                    'COUNT(DISTINCT cust_uid) as num'
                ])
                ->find();

            if($govData['fee'] > 0) {
                $result[24]['money'.$config['index']] += $govData['fee'];
            }

            // 员工餐优惠
            $staffData = $orderModel->where(array_merge($timeRange, [
                    ['pay_price', '>', 0],
                    ['package_type', '=', '工作餐']
                ]))
                ->whereIn('created_by', $config['users'])
                ->sum('discounts_price');

            if($staffData > 0) {
                $result[25]['money'.$config['index']] -= $staffData;
            }
        }

        // 计算合计
        $total = [
            'classify' => '合计', 'name' => '', 'price' => '',
            'num1' => 0, 'money1' => 0.00, 'num2' => 0, 'money2' => 0.00,
            'num3' => 0, 'money3' => 0.00, 'num4' => 0, 'money4' => 0.00
        ];

        array_walk($result, function($row) use (&$total) {
            for ($i = 1; $i <= 4; $i++) {
                $total['num'.$i] += $row['num'.$i];
                $total['money'.$i] += $row['money'.$i];
            }
        });
        
        $result[] = $total;

        return $result;
    }

    /**
     * 导出财务报表
     * @param array $params
     * @return array
     * @throws DbException
     */
    public static function exportBalance(array $params): array
    {
        // 站点配置
        $sites = [
            '科星驿站' => [
                'created_by' => [1, 43, 57],
                'title' => '科星驿站'
            ],
            '科源驿站' => [
                'created_by' => [46],
                'title' => '科源驿站'
            ],
            '803站点' => [
                'created_by' => [48],
                'title' => '803站点'
            ],
            '奥运村' => [
                'created_by' => [51, 65],
                'title' => '奥运村'
            ]
        ];

        // 准备主表数据
        $data = [
            [
                'header' => [
                    '分类', '名称', '单价',
                    '科星驿站数量', '科星驿站金额',
                    '科源驿站数量', '科源驿站金额',
                    '803站点数量', '803站点金额',
                    '奥运村数量', '奥运村金额'
                ],
                'export' => self::getTable($params),
                'title' => '营养餐财务报表'
            ]
        ];

        // 添加各站点明细表
        foreach ($sites as $site) {
            $siteData = self::getOrderData([
                'start_time' => $params['start_time'],
                'end_time' => $params['end_time'],
                'created_by' => $site['created_by']
            ]);
            
            $data[] = [
                'header' => $siteData['headers'],
                'export' => $siteData['data'],
                'title' => $site['title']
            ];
        }

        // 导出Excel
        $title = '财务报表_' . date('YmdHis') . '.xlsx';
        $url = Excel::exportTables($title, $data);
        
        return ['url' => $url];
    }

    /**
     * 查询订餐信息总
     * @throws DbException
     */
    public static function getOrderData(array $params): array
    {
        $orderModel = new OrderFood();
        $packageModel = new FoodPackage();
        $orderItemModel = new OrderFoodItem();
        // 查询数据
        $model = $orderModel->hasWhere('package', function ($query) {
            $query->where('num', '<>', 0);
        })->with([
            'createdByInfo',
            'custInfo',
            'employee',
            'package' => function ($query) {
                return $query->where('num', '<>', 0);
            }
        ])->when(isset($params['delivery_site']) && $params['delivery_site'] !== '', function ($query) use ($params) {
            $query->where('order_food.delivery_site', $params['delivery_site']);
        })->when(isset($params['delivery_option']) && $params['delivery_option'] !== '', function ($query) use ($params) {
            $query->where('order_food.delivery_option', $params['delivery_option']);
        })->when(isset($params['start_time']) && $params['start_time'] !== '', function ($query) use ($params) {
            $query->where('order_food.delivery_time','>=', $params['start_time']);
        })->when(isset($params['end_time']) && $params['end_time'] !== '', function ($query) use ($params) {
            $query->where('order_food.delivery_time','<=', $params['end_time']);
        })->when(isset($params['created_by']) && $params['created_by'] !== '', function ($query) use ($params) {
            $query->whereIn('order_food.created_by', $params['created_by']);
        })->order(['delivery_time']);
        $rows = $model->order(['update_time' => 'desc'])
        ->select()
        ->toArray();
        // 获取所有套餐信息
        $packageList = $packageModel->where('day_time', $params['start_time'])->field('name,package_id')->order('sort')->select()->toArray();
        // 取数据
        $data['row'] = [];
        $i = 1;
        // 支付总金额
        $payPriceTotal = 0;
        $feeTotal = 0;// 送餐费
        $govFeeTotal = 0;// 政府补贴
        $discountsPriceTotal = 0;// 折扣金额
        foreach ($rows as $v) {
            $value = [];
            // 序号
            $value['number'] = $i;
            $i++;
            // 供餐日期
            $value['delivery_time'] = $v['delivery_time'];
            // 供餐站点
            $value['delivery_site'] = $v['delivery_site'];
            // 用户信息
            if (!empty($v['custInfo'])) {
                $value['cust_name'] = $v['custInfo']['cust_name'];
                $value['cust_private_phone'] = $v['custInfo']['cust_private_phone'];
                $value['cust_live_address'] = $v['custInfo']['cust_live_address'];
            } else {
                $value['cust_name'] = '用户已删除';
                $value['cust_private_phone'] = '用户已删除';
                $value['cust_live_address'] = '用户已删除';
            }
            // 取餐方式
            $value['delivery_option'] = $v['delivery_option'];
            // 设置套餐详情
            foreach ($packageList as $vv) {
                $value[$vv['name']] = self::getNamePackageNum($vv['name'], $v['package']);
            }

            $payPriceTotal += $v['pay_price'] - $v['cust_delivery_fee'];
            // 金额
            $value['pay_price'] = $v['pay_price']- $v['cust_delivery_fee'];
            // 送餐费
            $feeTotal += $v['cust_delivery_fee'];
            $value['fee'] = $v['cust_delivery_fee'];
            // 政府补贴
            $govFeeTotal += $v['gov_fee'];
            $value['gov_fee'] = $v['gov_fee'];
            // 政府补贴
            $discountsPriceTotal += $v['discounts_price'];
            $value['discounts_price'] = $v['discounts_price'];
            // 备注
            $value['remark'] = $v['remark'];

            $data['row'][] = $value;
        }
        // 获取总金额
        $packageNameArray = array_column($packageList, 'name');
        // 遍历查询
        $totalAmount = [];
        foreach ($packageNameArray as $v) {
            $totalAmount[] = $orderItemModel
            ->when(isset($params['created_by']) && $params['created_by'] !== '', function ($query) use ($params) {
                $query->whereIn('created_by', $params['created_by']);
            })
            ->where('delivery_time','>=', $params['start_time'])
            ->where('delivery_time','<=', $params['end_time'])
            ->where('package_id', $v)
            ->sum('num');
        }
        $totalAmount[] = $payPriceTotal;
        $totalAmount[] = $feeTotal;
        $totalAmount[] = $govFeeTotal;
        $totalAmount[] = $discountsPriceTotal;
        $data['totalAmount'] = $totalAmount;
        $packageName = self::getPackageName($params['start_time']);
        $headers = [
            '序号',
            '供餐日期',
            '供餐站点',
            '姓名',
            '联系方式',
            '地址',
            '取餐方式',
        ];
        $headers = array_merge($headers,$packageName);
        $headers[] = '金额';
        $headers[] = '送餐费';
        $headers[] = '政府补贴';
        $headers[] = '折扣金额';
        $headers[] = '备注';
        $popArray = [
            '总计',
            '',
            '',
            '',
            '',
            '',
            '',
        ];
        $popArray = array_merge($popArray,$data['totalAmount']);
        $data['row'][] = $popArray;
        return [
            'headers' => $headers,
            'data' => $data['row']
        ];
    }

    /**
     * 员工数据获取
     * @param $params
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function getHotelMaterial($params): array
    {
        $dao = new Dao();
        $data = $dao->search('order_food_item',[
            'delivery_time[>=]' => $params['start_time'],
            'delivery_time[<=]' => $params['end_time'],
            'num[>]' => 0,
            'delete_time' => null,
            'package_type' => '员工餐',
            'GROUP' => 'package_name',
            "ORDER" => "package_name",
        ],[
            'package_name',
            'num' => Medoo::raw('SUM(num)'),
        ]);
        $dataV1 = $dao->search('order_food_item',[
            'delivery_time[>=]' => $params['start_time'],
            'delivery_time[<=]' => $params['end_time'],
            'num[>]' => 0,
            'delete_time' => null,
            'GROUP' => 'package_name',
            "ORDER" => "package_name",
        ],[
            'package_name',
            'num' => Medoo::raw('SUM(num)'),
        ]);
        // 当天查询处理名称
        if($params['start_time'] == $params['end_time']) {
            if(!empty($dataV1)) {
                foreach ($dataV1 as &$v) {
                    if(str_contains($v['package_name'],'主食专供')) {
                        // 查询商品名称
                        $name = $dao->get('food_package_item',[
                            'food_package.name' => $v['package_name'],
                            'food_package.day_time' => $params['start_time'],
                        ],'food_name',[
                            "[>]food_package" => ["package_id" => "package_id"]
                        ]);
                        if($name) {
                            $v['package_name'] = $name;
                        }
                    }
                }
            }
        }
        return [
            'packageNum' => $dataV1,
            'staffFoodNum' => $data,
        ];
    }

        /**
     * 获取送餐员数据表
     * @param $params
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Exception
     */
    public static function getStaffTable($params): array
    {
        $foodOrderItemModel = new OrderFoodItem();
        $mainStaffModel = new EmployeeUser();
        $foodPackageConfig = new FoodPackageConfig();
        // 查询送餐员
        $staffArr = $mainStaffModel->when(!empty($params['employee_uid']), function ($query) use ($params) {
            $query->where('employee_uid', '=', $params['employee_uid']);
        })->where('employee_position', 'like', '%送餐员%')->column('employee_uid as uid,employee_name as name');
        // 计算时间差
        $diff = self::dateDiff($params['start_time'], $params['end_time']);
        if ($diff == 0) {
            $timeName = $params['start_time'];
        } elseif ($diff == 364) {
            $timeName = date('Y', strtotime($params['start_time']));
        } else {
            $timeName = date('Y-m', strtotime($params['start_time']));
        }
        // 查询改日期内套餐名
        $packageArr = $foodPackageConfig->column('name');
        // 查询数据
        $data = [];
        foreach ($staffArr as $v) {
            $value = [];
            $value['日期'] = $timeName;
            $value['送餐员姓名'] = $v['name'];
            // 套餐
            $total = 0;
            foreach ($packageArr as $vv) {
                $num = $foodOrderItemModel
                    ->where('delivery_option', '=', '送餐')
                    ->where('delivery_time', '>=', $params['start_time'])
                    ->where('delivery_time', '<=', $params['end_time'])
                    ->where('employee_uid', '=', $v['uid'])
                    ->where('package_name', '=', $vv)
                    ->sum('num');
                $total += $num;
                if ($num == 0) {
                    $num = '';
                }
                $value[$vv] = $num;
            }
            $value['合计'] = $total;
            $data[] = $value;
        }
        return $data;
    }

        /**
     * 获取驿站数据
     * @param $params
     * @return array
     * @throws Exception
     */
    public static function getStationData($params): array
    {
        // 查询该用户的订餐数量
        $foodOrderItemModel = new OrderFoodItem();
        // 计算时间差
        $diff = self::dateDiff($params['start_time'], $params['end_time']);
        // 查询订餐人 总金额
        if($diff == 364) {
            $timeName = date('Y', strtotime($params['start_time']));
        } else {
            $timeName = date('Y-m', strtotime($params['start_time']));
        }
        // 以创建人分组查询数量
        $arr = $foodOrderItemModel->where('num','>',0)->whereIn('package_name',[
            'A1','A2','B1','B2','零1','零2'
        ])->when(!empty($params['delivery_option']), function ($query) use ($params) {
            $query->where('delivery_option', '=', $params['delivery_option']);
        })->when(!empty($params['site_name']), function ($query) use ($params) {
            $query->where('delivery_site', '=', $params['site_name']);
        })->when(!empty($params['start_time']), function ($query) use ($params) {
            $query->where('delivery_time', '>=', $params['start_time']);
        })->when(!empty($params['end_time']), function ($query) use ($params) {
            $query->where('delivery_time', '<=', $params['end_time']);
        })->group('delivery_site')->field('delivery_site,SUM(num) as sum_num,SUM(pay_price) as sum_price')->select()->toArray();
        $data = [];
        if (!empty($arr)) {
            foreach ($arr as $v) {
                $value['日期'] = $timeName;
                $value['站点'] = $v['delivery_site'];
                $value['订餐份数'] = $v['sum_num'];
                $value['总金额'] = $v['sum_price'];
                $data[] = $value;
            }
        }
        return $data;
    }
}

