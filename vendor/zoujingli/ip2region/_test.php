<?php

// 建议使用 php _test.php 命令行运行测试文件

require 'Ip2Region.php';

$ip2region = new Ip2Region();

// array (
//     'city_id' => 1713,
//     'region' => '中国|0|广东省|广州市|电信',
// )

for ($i = 0; $i < 10; $i++) {
    test();
}

function getIp()
{
    $ip_long = array(
        array('607649792', '608174079'), // *********-*************
        array('1038614528', '1039007743'), // **********-**************
        array('1783627776', '1784676351'), // **********-**************
        array('2035023872', '2035154943'), // **********-**************
        array('2078801920', '2079064063'), // ***********-***************
        array('-1950089216', '-1948778497'), // ***********-***************
        array('-1425539072', '-1425014785'), // *********-**************
        array('-1236271104', '-1235419137'), // **********-**************
        array('-770113536', '-768606209'), // **********-**************
        array('-569376768', '-564133889'), // **********-**************
    );
    $rkey = mt_rand(0, 9);
    return long2ip(mt_rand($ip_long[$rkey][0], $ip_long[$rkey][1]));
}

function test()
{
    $ip = getIp();
    global $ip2region;

    echo PHP_EOL . "===============================";
    echo PHP_EOL . "测试 IP 地址: {$ip}";
    echo PHP_EOL . "--------【完整查询】------------" . PHP_EOL;
    $info = $ip2region->memorySearch($ip);
    var_export($info);

    echo PHP_EOL . "---------【简易查询】----------" . PHP_EOL;
    var_export($ip2region->simple($ip));
    echo PHP_EOL . "===============================" . PHP_EOL . PHP_EOL;
}