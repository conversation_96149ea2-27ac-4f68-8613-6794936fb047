{"name": "workerman/crontab", "type": "library", "keywords": ["crontab"], "homepage": "http://www.workerman.net", "license": "MIT", "description": "A crontab written in PHP based on workerman", "authors": [{"name": "walkor", "email": "<EMAIL>", "homepage": "http://www.workerman.net", "role": "Developer"}], "support": {"email": "<EMAIL>", "issues": "https://github.com/walkor/workerman/issues", "forum": "http://wenda.workerman.net/", "wiki": "http://doc.workerman.net/", "source": "https://github.com/walkor/crontab"}, "require": {"php": ">=7.0", "workerman/workerman": ">=4.0.20"}, "autoload": {"psr-4": {"Workerman\\Crontab\\": "./src"}}}