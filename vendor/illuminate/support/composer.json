{"name": "illuminate/support", "description": "The Illuminate Support package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.1", "ext-ctype": "*", "ext-filter": "*", "ext-mbstring": "*", "doctrine/inflector": "^2.0", "illuminate/collections": "^10.0", "illuminate/conditionable": "^10.0", "illuminate/contracts": "^10.0", "illuminate/macroable": "^10.0", "nesbot/carbon": "^2.67", "voku/portable-ascii": "^2.0"}, "conflict": {"tightenco/collect": "<5.5.33"}, "autoload": {"psr-4": {"Illuminate\\Support\\": ""}, "files": ["helpers.php"]}, "extra": {"branch-alias": {"dev-master": "10.x-dev"}}, "suggest": {"illuminate/filesystem": "Required to use the composer class (^10.0).", "league/commonmark": "Required to use Str::markdown() and Stringable::markdown() (^2.0.2).", "ramsey/uuid": "Required to use Str::uuid() (^4.7).", "symfony/process": "Required to use the composer class (^6.2).", "symfony/uid": "Required to use Str::ulid() (^6.2).", "symfony/var-dumper": "Required to use the dd function (^6.2).", "vlucas/phpdotenv": "Required to use the Env class and env helper (^5.4.1)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}