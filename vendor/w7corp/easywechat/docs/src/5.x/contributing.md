# 贡献代码

## 开发

我们欢迎广大开发者贡献大家的智慧，让我们共同让它变得更完美.

### 开始之前

请严格遵循以下代码标准:

> - [PSR-2](https://github.com/php-fig/fig-standards/blob/master/accepted/PSR-2-coding-style-guide.md).
> - 使用 4 个空格作为缩进。

### 流程

1. Fork [overtrue/wechat](https://github.com/overtrue/wechat) 到本地.
2. 创建新的分支：

```shell
    $ git checkout -b new_feature
```

3. 编写代码。
4. Push 到你的分支:

```shell
    $ git push origin new_feature
```

5. 创建 Pull Request 并描述你完成的功能或者做出的修改。

> 注意：注释请使用英文

## 更新文档

我们的文档也是开源的，源代码在 [w7corp/EasyWeChat/docs](https://github.com/w7corp/easywechat/tree/master/docs)。

### 流程

1. Fork [w7corp/EasyWeChat](https://github.com/w7corp/easywechat)
2. Clone 到你的电脑：

```shell
    $ git clone https://github.com/<username>/site.git
    $ cd docs
```

3. 创建新的分支，编辑文档
4. Push 到你的分支。
5. 创建 Pull Request 并描述你完成的功能或者做出的修改。

## 报告 Bug

当你在使用过程中遇到问题，请查阅 [疑难解答](troubleshooting.html) 或者在这里提问 [GitHub](https://github.com/overtrue/wechat/issues). 如果还是不能解决你的问题，请到 GitHub 联系我们。

[overtrue/wechat]: https://github.com/overtrue/wechat
