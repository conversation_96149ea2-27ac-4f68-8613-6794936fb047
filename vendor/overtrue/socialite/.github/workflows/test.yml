name: Test
on: [push, pull_request]

jobs:
  phpunit:
    name: PHP-${{ matrix.php_version }}-${{ matrix.perfer }}
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        php_version:
          - 8.0
          - 8.1
        perfer:
          - stable
          - lowest
    steps:
    - uses: actions/checkout@v3
    - run: composer validate --strict
    - run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT
      id: composer-cache
    - uses: actions/cache@v3
      with:
        path: ${{ steps.composer-cache.outputs.dir }}
        key: dependencies-caches-php-${{ hashFiles('**/composer.lock') }}
        restore-keys: dependencies-caches-php-
    - run: composer update --prefer-dist --no-interaction --no-suggest --prefer-${{ matrix.perfer }}
    - run: ./vendor/bin/phpunit
