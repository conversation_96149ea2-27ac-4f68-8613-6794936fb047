name: Lint
on: [push, pull_request]

jobs:
  phpstan:
    name: PHP<PERSON><PERSON>
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - run: composer validate --strict
    - run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT
      id: composer-cache
    - uses: actions/cache@v3
      with:
        path: ${{ steps.composer-cache.outputs.dir }}
        key: dependencies-caches-php-${{ hashFiles('**/composer.lock') }}
        restore-keys: dependencies-caches-php-
    - run: composer install --no-progress
    - run: composer phpstan

  php_cs_fixer:
    name: PHP-CS-Fxier
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT
      id: composer-cache
    - uses: actions/cache@v3
      with:
        path: ${{ steps.composer-cache.outputs.dir }}
        key: dependencies-caches-php-${{ hashFiles('**/composer.lock') }}
        restore-keys: dependencies-caches-php-
    - run: composer install --no-progress
    - run: composer check-style
