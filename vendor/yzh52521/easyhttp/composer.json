{"name": "yzh52521/easyhttp", "description": "EasyHttp 是一个轻量级、语义化、对IDE友好的HTTP客户端，支持常见的HTTP请求、异步请求和并发请求，让你可以快速地使用 HTTP 请求与其他 Web 应用进行通信。", "license": "MIT", "keywords": ["easyhttp", "EasyHttp", "php-http", "phphttp", "easy-http", "php", "http", "curl"], "homepage": "https://github.com/yzh52521/easyhttp", "authors": [{"name": "yzh52521", "email": "<EMAIL>"}], "require": {"php": ">=7.2.5", "guzzlehttp/guzzle": "^6.0|^7.0", "psr/log": "^1.0|^2.0|^3.0"}, "autoload": {"psr-4": {"yzh52521\\EasyHttp\\": "src/"}}, "minimum-stability": "stable"}