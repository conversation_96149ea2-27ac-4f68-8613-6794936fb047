<?php

/**
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

return array_replace_recursive(require __DIR__.'/en.php', [
    'meridiem' => ['KI', 'UT'],
    'weekdays' => ['<PERSON><PERSON><PERSON>', 'Nju<PERSON><PERSON>', 'N<PERSON><PERSON><PERSON>', 'Nju<PERSON>no', 'Ara<PERSON>i', '<PERSON><PERSON><PERSON><PERSON>', 'NJuma<PERSON>hi<PERSON>'],
    'weekdays_short' => ['Kma', 'Tat', 'Ine', 'Tan', 'Arm', 'Maa', 'NMM'],
    'weekdays_min' => ['Kma', 'Tat', 'Ine', 'Tan', 'Arm', 'Maa', 'NMM'],
    'months' => ['Mweri wa mbere', 'Mweri wa kaĩri', 'Mweri wa kathatũ', '<PERSON>weri wa kana', '<PERSON>weri wa gatano', '<PERSON>weri wa gatantatũ', '<PERSON>weri wa mũgwanja', '<PERSON>weri wa kanana', '<PERSON>weri wa kenda', '<PERSON>weri wa ikũmi', '<PERSON>weri wa ikũmi na ũmwe', '<PERSON>weri wa ikũmi na Kaĩrĩ'],
    'months_short' => ['Mbe', 'Kai', 'Kat', 'Kan', 'Gat', 'Gan', 'Mug', 'Knn', 'Ken', 'Iku', 'Imw', 'Igi'],
    'formats' => [
        'LT' => 'HH:mm',
        'LTS' => 'HH:mm:ss',
        'L' => 'DD/MM/YYYY',
        'LL' => 'D MMM YYYY',
        'LLL' => 'D MMMM YYYY HH:mm',
        'LLLL' => 'dddd, D MMMM YYYY HH:mm',
    ],
]);
