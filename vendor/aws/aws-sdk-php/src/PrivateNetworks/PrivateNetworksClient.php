<?php
namespace Aws\PrivateNetworks;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AWS Private 5G** service.
 * @method \Aws\Result acknowledgeOrderReceipt(array $args = [])
 * @method \GuzzleHttp\Promise\Promise acknowledgeOrderReceiptAsync(array $args = [])
 * @method \Aws\Result activateDeviceIdentifier(array $args = [])
 * @method \GuzzleHttp\Promise\Promise activateDeviceIdentifierAsync(array $args = [])
 * @method \Aws\Result activateNetworkSite(array $args = [])
 * @method \GuzzleHttp\Promise\Promise activateNetworkSiteAsync(array $args = [])
 * @method \Aws\Result configureAccessPoint(array $args = [])
 * @method \GuzzleHttp\Promise\Promise configureAccessPointAsync(array $args = [])
 * @method \Aws\Result createNetwork(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createNetworkAsync(array $args = [])
 * @method \Aws\Result createNetworkSite(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createNetworkSiteAsync(array $args = [])
 * @method \Aws\Result deactivateDeviceIdentifier(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deactivateDeviceIdentifierAsync(array $args = [])
 * @method \Aws\Result deleteNetwork(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteNetworkAsync(array $args = [])
 * @method \Aws\Result deleteNetworkSite(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteNetworkSiteAsync(array $args = [])
 * @method \Aws\Result getDeviceIdentifier(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getDeviceIdentifierAsync(array $args = [])
 * @method \Aws\Result getNetwork(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getNetworkAsync(array $args = [])
 * @method \Aws\Result getNetworkResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getNetworkResourceAsync(array $args = [])
 * @method \Aws\Result getNetworkSite(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getNetworkSiteAsync(array $args = [])
 * @method \Aws\Result getOrder(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getOrderAsync(array $args = [])
 * @method \Aws\Result listDeviceIdentifiers(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDeviceIdentifiersAsync(array $args = [])
 * @method \Aws\Result listNetworkResources(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listNetworkResourcesAsync(array $args = [])
 * @method \Aws\Result listNetworkSites(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listNetworkSitesAsync(array $args = [])
 * @method \Aws\Result listNetworks(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listNetworksAsync(array $args = [])
 * @method \Aws\Result listOrders(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listOrdersAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result ping(array $args = [])
 * @method \GuzzleHttp\Promise\Promise pingAsync(array $args = [])
 * @method \Aws\Result startNetworkResourceUpdate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startNetworkResourceUpdateAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateNetworkSite(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateNetworkSiteAsync(array $args = [])
 * @method \Aws\Result updateNetworkSitePlan(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateNetworkSitePlanAsync(array $args = [])
 */
class PrivateNetworksClient extends AwsClient {}
