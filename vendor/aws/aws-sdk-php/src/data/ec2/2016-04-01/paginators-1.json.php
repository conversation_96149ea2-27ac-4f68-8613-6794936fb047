<?php
// This file was auto-generated from sdk-root/src/data/ec2/2016-04-01/paginators-1.json
return [ 'pagination' => [ 'DescribeAccountAttributes' => [ 'result_key' => 'AccountAttributes', ], 'DescribeAddresses' => [ 'result_key' => 'Addresses', ], 'DescribeAvailabilityZones' => [ 'result_key' => 'AvailabilityZones', ], 'DescribeBundleTasks' => [ 'result_key' => 'BundleTasks', ], 'DescribeConversionTasks' => [ 'result_key' => 'ConversionTasks', ], 'DescribeCustomerGateways' => [ 'result_key' => 'CustomerGateways', ], 'DescribeDhcpOptions' => [ 'result_key' => 'DhcpOptions', ], 'DescribeExportTasks' => [ 'result_key' => 'ExportTasks', ], 'DescribeImages' => [ 'result_key' => 'Images', ], 'DescribeInstanceStatus' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'InstanceStatuses', ], 'DescribeInstances' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'Reservations', ], 'DescribeInternetGateways' => [ 'result_key' => 'InternetGateways', ], 'DescribeKeyPairs' => [ 'result_key' => 'KeyPairs', ], 'DescribeNetworkAcls' => [ 'result_key' => 'NetworkAcls', ], 'DescribeNetworkInterfaces' => [ 'result_key' => 'NetworkInterfaces', ], 'DescribePlacementGroups' => [ 'result_key' => 'PlacementGroups', ], 'DescribeRegions' => [ 'result_key' => 'Regions', ], 'DescribeReservedInstances' => [ 'result_key' => 'ReservedInstances', ], 'DescribeReservedInstancesListings' => [ 'result_key' => 'ReservedInstancesListings', ], 'DescribeReservedInstancesOfferings' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'ReservedInstancesOfferings', ], 'DescribeReservedInstancesModifications' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'result_key' => 'ReservedInstancesModifications', ], 'DescribeRouteTables' => [ 'result_key' => 'RouteTables', ], 'DescribeSecurityGroups' => [ 'result_key' => 'SecurityGroups', ], 'DescribeSnapshots' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'Snapshots', ], 'DescribeSpotInstanceRequests' => [ 'result_key' => 'SpotInstanceRequests', ], 'DescribeSpotFleetRequests' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'SpotFleetRequestConfigs', ], 'DescribeSpotPriceHistory' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'SpotPriceHistory', ], 'DescribeSubnets' => [ 'result_key' => 'Subnets', ], 'DescribeTags' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'Tags', ], 'DescribeVolumeStatus' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'VolumeStatuses', ], 'DescribeVolumes' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'Volumes', ], 'DescribeVpcs' => [ 'result_key' => 'Vpcs', ], 'DescribeVpcPeeringConnections' => [ 'result_key' => 'VpcPeeringConnections', ], 'DescribeVpnConnections' => [ 'result_key' => 'VpnConnections', ], 'DescribeVpnGateways' => [ 'result_key' => 'VpnGateways', ], ],];
