<?php
// This file was auto-generated from sdk-root/src/data/migrationhuborchestrator/2021-08-28/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2021-08-28', 'endpointPrefix' => 'migrationhub-orchestrator', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'AWS Migration Hub Orchestrator', 'serviceId' => 'MigrationHubOrchestrator', 'signatureVersion' => 'v4', 'signingName' => 'migrationhub-orchestrator', 'uid' => 'migrationhuborchestrator-2021-08-28', ], 'operations' => [ 'CreateTemplate' => [ 'name' => 'CreateTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/template', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateTemplateRequest', ], 'output' => [ 'shape' => 'CreateTemplateResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'CreateWorkflow' => [ 'name' => 'CreateWorkflow', 'http' => [ 'method' => 'POST', 'requestUri' => '/migrationworkflow/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateMigrationWorkflowRequest', ], 'output' => [ 'shape' => 'CreateMigrationWorkflowResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'CreateWorkflowStep' => [ 'name' => 'CreateWorkflowStep', 'http' => [ 'method' => 'POST', 'requestUri' => '/workflowstep', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateWorkflowStepRequest', ], 'output' => [ 'shape' => 'CreateWorkflowStepResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'CreateWorkflowStepGroup' => [ 'name' => 'CreateWorkflowStepGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/workflowstepgroups', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateWorkflowStepGroupRequest', ], 'output' => [ 'shape' => 'CreateWorkflowStepGroupResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'DeleteTemplate' => [ 'name' => 'DeleteTemplate', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/template/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteTemplateRequest', ], 'output' => [ 'shape' => 'DeleteTemplateResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteWorkflow' => [ 'name' => 'DeleteWorkflow', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/migrationworkflow/{id}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteMigrationWorkflowRequest', ], 'output' => [ 'shape' => 'DeleteMigrationWorkflowResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteWorkflowStep' => [ 'name' => 'DeleteWorkflowStep', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/workflowstep/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteWorkflowStepRequest', ], 'output' => [ 'shape' => 'DeleteWorkflowStepResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteWorkflowStepGroup' => [ 'name' => 'DeleteWorkflowStepGroup', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/workflowstepgroup/{id}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteWorkflowStepGroupRequest', ], 'output' => [ 'shape' => 'DeleteWorkflowStepGroupResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'GetTemplate' => [ 'name' => 'GetTemplate', 'http' => [ 'method' => 'GET', 'requestUri' => '/migrationworkflowtemplate/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMigrationWorkflowTemplateRequest', ], 'output' => [ 'shape' => 'GetMigrationWorkflowTemplateResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetTemplateStep' => [ 'name' => 'GetTemplateStep', 'http' => [ 'method' => 'GET', 'requestUri' => '/templatestep/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTemplateStepRequest', ], 'output' => [ 'shape' => 'GetTemplateStepResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetTemplateStepGroup' => [ 'name' => 'GetTemplateStepGroup', 'http' => [ 'method' => 'GET', 'requestUri' => '/templates/{templateId}/stepgroups/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTemplateStepGroupRequest', ], 'output' => [ 'shape' => 'GetTemplateStepGroupResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetWorkflow' => [ 'name' => 'GetWorkflow', 'http' => [ 'method' => 'GET', 'requestUri' => '/migrationworkflow/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMigrationWorkflowRequest', ], 'output' => [ 'shape' => 'GetMigrationWorkflowResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetWorkflowStep' => [ 'name' => 'GetWorkflowStep', 'http' => [ 'method' => 'GET', 'requestUri' => '/workflowstep/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetWorkflowStepRequest', ], 'output' => [ 'shape' => 'GetWorkflowStepResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetWorkflowStepGroup' => [ 'name' => 'GetWorkflowStepGroup', 'http' => [ 'method' => 'GET', 'requestUri' => '/workflowstepgroup/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetWorkflowStepGroupRequest', ], 'output' => [ 'shape' => 'GetWorkflowStepGroupResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListPlugins' => [ 'name' => 'ListPlugins', 'http' => [ 'method' => 'GET', 'requestUri' => '/plugins', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPluginsRequest', ], 'output' => [ 'shape' => 'ListPluginsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListTemplateStepGroups' => [ 'name' => 'ListTemplateStepGroups', 'http' => [ 'method' => 'GET', 'requestUri' => '/templatestepgroups/{templateId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTemplateStepGroupsRequest', ], 'output' => [ 'shape' => 'ListTemplateStepGroupsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListTemplateSteps' => [ 'name' => 'ListTemplateSteps', 'http' => [ 'method' => 'GET', 'requestUri' => '/templatesteps', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTemplateStepsRequest', ], 'output' => [ 'shape' => 'ListTemplateStepsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListTemplates' => [ 'name' => 'ListTemplates', 'http' => [ 'method' => 'GET', 'requestUri' => '/migrationworkflowtemplates', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMigrationWorkflowTemplatesRequest', ], 'output' => [ 'shape' => 'ListMigrationWorkflowTemplatesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListWorkflowStepGroups' => [ 'name' => 'ListWorkflowStepGroups', 'http' => [ 'method' => 'GET', 'requestUri' => '/workflowstepgroups', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListWorkflowStepGroupsRequest', ], 'output' => [ 'shape' => 'ListWorkflowStepGroupsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListWorkflowSteps' => [ 'name' => 'ListWorkflowSteps', 'http' => [ 'method' => 'GET', 'requestUri' => '/workflow/{workflowId}/workflowstepgroups/{stepGroupId}/workflowsteps', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListWorkflowStepsRequest', ], 'output' => [ 'shape' => 'ListWorkflowStepsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListWorkflows' => [ 'name' => 'ListWorkflows', 'http' => [ 'method' => 'GET', 'requestUri' => '/migrationworkflows', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMigrationWorkflowsRequest', ], 'output' => [ 'shape' => 'ListMigrationWorkflowsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'RetryWorkflowStep' => [ 'name' => 'RetryWorkflowStep', 'http' => [ 'method' => 'POST', 'requestUri' => '/retryworkflowstep/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RetryWorkflowStepRequest', ], 'output' => [ 'shape' => 'RetryWorkflowStepResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'StartWorkflow' => [ 'name' => 'StartWorkflow', 'http' => [ 'method' => 'POST', 'requestUri' => '/migrationworkflow/{id}/start', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartMigrationWorkflowRequest', ], 'output' => [ 'shape' => 'StartMigrationWorkflowResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'StopWorkflow' => [ 'name' => 'StopWorkflow', 'http' => [ 'method' => 'POST', 'requestUri' => '/migrationworkflow/{id}/stop', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopMigrationWorkflowRequest', ], 'output' => [ 'shape' => 'StopMigrationWorkflowResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdateTemplate' => [ 'name' => 'UpdateTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/template/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateTemplateRequest', ], 'output' => [ 'shape' => 'UpdateTemplateResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateWorkflow' => [ 'name' => 'UpdateWorkflow', 'http' => [ 'method' => 'POST', 'requestUri' => '/migrationworkflow/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateMigrationWorkflowRequest', ], 'output' => [ 'shape' => 'UpdateMigrationWorkflowResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateWorkflowStep' => [ 'name' => 'UpdateWorkflowStep', 'http' => [ 'method' => 'POST', 'requestUri' => '/workflowstep/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateWorkflowStepRequest', ], 'output' => [ 'shape' => 'UpdateWorkflowStepResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateWorkflowStepGroup' => [ 'name' => 'UpdateWorkflowStepGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/workflowstepgroup/{id}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateWorkflowStepGroupRequest', ], 'output' => [ 'shape' => 'UpdateWorkflowStepGroupResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'ApplicationConfigurationName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[-a-zA-Z0-9_.+]+[-a-zA-Z0-9_.+ ]*', ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'ClientToken' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[-a-zA-Z0-9]*', ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'CreateMigrationWorkflowRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'templateId', 'inputParameters', ], 'members' => [ 'name' => [ 'shape' => 'CreateMigrationWorkflowRequestNameString', ], 'description' => [ 'shape' => 'CreateMigrationWorkflowRequestDescriptionString', ], 'templateId' => [ 'shape' => 'CreateMigrationWorkflowRequestTemplateIdString', ], 'applicationConfigurationId' => [ 'shape' => 'CreateMigrationWorkflowRequestApplicationConfigurationIdString', ], 'inputParameters' => [ 'shape' => 'StepInputParameters', ], 'stepTargets' => [ 'shape' => 'StringList', ], 'tags' => [ 'shape' => 'StringMap', ], ], ], 'CreateMigrationWorkflowRequestApplicationConfigurationIdString' => [ 'type' => 'string', 'max' => 100, 'min' => 0, 'pattern' => '[-a-zA-Z0-9_.+]*', ], 'CreateMigrationWorkflowRequestDescriptionString' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'pattern' => '[-a-zA-Z0-9_.+, ]*', ], 'CreateMigrationWorkflowRequestNameString' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[-a-zA-Z0-9_.+]+[-a-zA-Z0-9_.+ ]*', ], 'CreateMigrationWorkflowRequestTemplateIdString' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[-a-zA-Z0-9_.+]+[-a-zA-Z0-9_.+ ]*', ], 'CreateMigrationWorkflowResponse' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'MigrationWorkflowId', ], 'arn' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'String', ], 'templateId' => [ 'shape' => 'String', ], 'adsApplicationConfigurationId' => [ 'shape' => 'String', ], 'workflowInputs' => [ 'shape' => 'StepInputParameters', ], 'stepTargets' => [ 'shape' => 'StringList', ], 'status' => [ 'shape' => 'MigrationWorkflowStatusEnum', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'tags' => [ 'shape' => 'StringMap', ], ], ], 'CreateTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'templateName', 'templateSource', ], 'members' => [ 'templateName' => [ 'shape' => 'CreateTemplateRequestTemplateNameString', ], 'templateDescription' => [ 'shape' => 'CreateTemplateRequestTemplateDescriptionString', ], 'templateSource' => [ 'shape' => 'TemplateSource', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateTemplateRequestTemplateDescriptionString' => [ 'type' => 'string', 'max' => 250, 'min' => 0, 'pattern' => '.*', ], 'CreateTemplateRequestTemplateNameString' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[ a-zA-Z0-9]*', ], 'CreateTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'templateId' => [ 'shape' => 'String', ], 'templateArn' => [ 'shape' => 'String', ], 'tags' => [ 'shape' => 'StringMap', ], ], ], 'CreateWorkflowStepGroupRequest' => [ 'type' => 'structure', 'required' => [ 'workflowId', 'name', ], 'members' => [ 'workflowId' => [ 'shape' => 'MigrationWorkflowId', ], 'name' => [ 'shape' => 'StepGroupName', ], 'description' => [ 'shape' => 'StepGroupDescription', ], 'next' => [ 'shape' => 'StringList', ], 'previous' => [ 'shape' => 'StringList', ], ], ], 'CreateWorkflowStepGroupResponse' => [ 'type' => 'structure', 'members' => [ 'workflowId' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'String', ], 'tools' => [ 'shape' => 'ToolsList', ], 'next' => [ 'shape' => 'StringList', ], 'previous' => [ 'shape' => 'StringList', ], 'creationTime' => [ 'shape' => 'Timestamp', ], ], ], 'CreateWorkflowStepRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'stepGroupId', 'workflowId', 'stepActionType', ], 'members' => [ 'name' => [ 'shape' => 'MigrationWorkflowName', ], 'stepGroupId' => [ 'shape' => 'StepGroupId', ], 'workflowId' => [ 'shape' => 'MigrationWorkflowId', ], 'stepActionType' => [ 'shape' => 'StepActionType', ], 'description' => [ 'shape' => 'MigrationWorkflowDescription', ], 'workflowStepAutomationConfiguration' => [ 'shape' => 'WorkflowStepAutomationConfiguration', ], 'stepTarget' => [ 'shape' => 'StringList', ], 'outputs' => [ 'shape' => 'WorkflowStepOutputList', ], 'previous' => [ 'shape' => 'StringList', ], 'next' => [ 'shape' => 'StringList', ], ], ], 'CreateWorkflowStepResponse' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'String', ], 'stepGroupId' => [ 'shape' => 'String', ], 'workflowId' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], ], ], 'DataType' => [ 'type' => 'string', 'enum' => [ 'STRING', 'INTEGER', 'STRINGLIST', 'STRINGMAP', ], ], 'DeleteMigrationWorkflowRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'MigrationWorkflowId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'DeleteMigrationWorkflowResponse' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'MigrationWorkflowId', ], 'arn' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'MigrationWorkflowStatusEnum', ], ], ], 'DeleteTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'TemplateId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'DeleteTemplateResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteWorkflowStepGroupRequest' => [ 'type' => 'structure', 'required' => [ 'workflowId', 'id', ], 'members' => [ 'workflowId' => [ 'shape' => 'MigrationWorkflowId', 'location' => 'querystring', 'locationName' => 'workflowId', ], 'id' => [ 'shape' => 'StepGroupId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'DeleteWorkflowStepGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteWorkflowStepRequest' => [ 'type' => 'structure', 'required' => [ 'id', 'stepGroupId', 'workflowId', ], 'members' => [ 'id' => [ 'shape' => 'StepId', 'location' => 'uri', 'locationName' => 'id', ], 'stepGroupId' => [ 'shape' => 'StepGroupId', 'location' => 'querystring', 'locationName' => 'stepGroupId', ], 'workflowId' => [ 'shape' => 'MigrationWorkflowId', 'location' => 'querystring', 'locationName' => 'workflowId', ], ], ], 'DeleteWorkflowStepResponse' => [ 'type' => 'structure', 'members' => [], ], 'GetMigrationWorkflowRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'MigrationWorkflowId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'GetMigrationWorkflowResponse' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'MigrationWorkflowId', ], 'arn' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'String', ], 'templateId' => [ 'shape' => 'String', ], 'adsApplicationConfigurationId' => [ 'shape' => 'String', ], 'adsApplicationName' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'MigrationWorkflowStatusEnum', ], 'statusMessage' => [ 'shape' => 'String', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'lastStartTime' => [ 'shape' => 'Timestamp', ], 'lastStopTime' => [ 'shape' => 'Timestamp', ], 'lastModifiedTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'tools' => [ 'shape' => 'ToolsList', ], 'totalSteps' => [ 'shape' => 'Integer', ], 'completedSteps' => [ 'shape' => 'Integer', ], 'workflowInputs' => [ 'shape' => 'StepInputParameters', ], 'tags' => [ 'shape' => 'StringMap', ], 'workflowBucket' => [ 'shape' => 'String', ], ], ], 'GetMigrationWorkflowTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'TemplateId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'GetMigrationWorkflowTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'String', ], 'templateArn' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'String', ], 'inputs' => [ 'shape' => 'TemplateInputList', ], 'tools' => [ 'shape' => 'ToolsList', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'owner' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'TemplateStatus', ], 'statusMessage' => [ 'shape' => 'String', ], 'templateClass' => [ 'shape' => 'String', ], 'tags' => [ 'shape' => 'StringMap', ], ], ], 'GetTemplateStepGroupRequest' => [ 'type' => 'structure', 'required' => [ 'templateId', 'id', ], 'members' => [ 'templateId' => [ 'shape' => 'TemplateId', 'location' => 'uri', 'locationName' => 'templateId', ], 'id' => [ 'shape' => 'StepGroupId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'GetTemplateStepGroupResponse' => [ 'type' => 'structure', 'members' => [ 'templateId' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'StepGroupStatus', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'lastModifiedTime' => [ 'shape' => 'Timestamp', ], 'tools' => [ 'shape' => 'ToolsList', ], 'previous' => [ 'shape' => 'StringList', ], 'next' => [ 'shape' => 'StringList', ], ], ], 'GetTemplateStepRequest' => [ 'type' => 'structure', 'required' => [ 'id', 'templateId', 'stepGroupId', ], 'members' => [ 'id' => [ 'shape' => 'StepId', 'location' => 'uri', 'locationName' => 'id', ], 'templateId' => [ 'shape' => 'TemplateId', 'location' => 'querystring', 'locationName' => 'templateId', ], 'stepGroupId' => [ 'shape' => 'StepGroupId', 'location' => 'querystring', 'locationName' => 'stepGroupId', ], ], ], 'GetTemplateStepResponse' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'StepId', ], 'stepGroupId' => [ 'shape' => 'StepGroupId', ], 'templateId' => [ 'shape' => 'TemplateId', ], 'name' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'String', ], 'stepActionType' => [ 'shape' => 'StepActionType', ], 'creationTime' => [ 'shape' => 'String', ], 'previous' => [ 'shape' => 'StringList', ], 'next' => [ 'shape' => 'StringList', ], 'outputs' => [ 'shape' => 'StepOutputList', ], 'stepAutomationConfiguration' => [ 'shape' => 'StepAutomationConfiguration', ], ], ], 'GetWorkflowStepGroupRequest' => [ 'type' => 'structure', 'required' => [ 'id', 'workflowId', ], 'members' => [ 'id' => [ 'shape' => 'StepGroupId', 'location' => 'uri', 'locationName' => 'id', ], 'workflowId' => [ 'shape' => 'MigrationWorkflowId', 'location' => 'querystring', 'locationName' => 'workflowId', ], ], ], 'GetWorkflowStepGroupResponse' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'StepGroupId', ], 'workflowId' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'StepGroupStatus', ], 'owner' => [ 'shape' => 'Owner', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'lastModifiedTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'tools' => [ 'shape' => 'ToolsList', ], 'previous' => [ 'shape' => 'StringList', ], 'next' => [ 'shape' => 'StringList', ], ], ], 'GetWorkflowStepRequest' => [ 'type' => 'structure', 'required' => [ 'workflowId', 'stepGroupId', 'id', ], 'members' => [ 'workflowId' => [ 'shape' => 'MigrationWorkflowId', 'location' => 'querystring', 'locationName' => 'workflowId', ], 'stepGroupId' => [ 'shape' => 'StepGroupId', 'location' => 'querystring', 'locationName' => 'stepGroupId', ], 'id' => [ 'shape' => 'StepId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'GetWorkflowStepResponse' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'stepGroupId' => [ 'shape' => 'String', ], 'workflowId' => [ 'shape' => 'String', ], 'stepId' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'String', ], 'stepActionType' => [ 'shape' => 'StepActionType', ], 'owner' => [ 'shape' => 'Owner', ], 'workflowStepAutomationConfiguration' => [ 'shape' => 'WorkflowStepAutomationConfiguration', ], 'stepTarget' => [ 'shape' => 'StringList', ], 'outputs' => [ 'shape' => 'GetWorkflowStepResponseOutputsList', ], 'previous' => [ 'shape' => 'StringList', ], 'next' => [ 'shape' => 'StringList', ], 'status' => [ 'shape' => 'StepStatus', ], 'statusMessage' => [ 'shape' => 'String', ], 'scriptOutputLocation' => [ 'shape' => 'String', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'lastStartTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'noOfSrvCompleted' => [ 'shape' => 'Integer', ], 'noOfSrvFailed' => [ 'shape' => 'Integer', ], 'totalNoOfSrv' => [ 'shape' => 'Integer', ], ], ], 'GetWorkflowStepResponseOutputsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkflowStepOutput', ], 'max' => 5, 'min' => 0, ], 'IPAddress' => [ 'type' => 'string', 'max' => 15, 'min' => 0, 'pattern' => '(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])', ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'ListMigrationWorkflowTemplatesRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'name' => [ 'shape' => 'TemplateName', 'location' => 'querystring', 'locationName' => 'name', ], ], ], 'ListMigrationWorkflowTemplatesResponse' => [ 'type' => 'structure', 'required' => [ 'templateSummary', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'templateSummary' => [ 'shape' => 'TemplateSummaryList', ], ], ], 'ListMigrationWorkflowsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'templateId' => [ 'shape' => 'TemplateId', 'location' => 'querystring', 'locationName' => 'templateId', ], 'adsApplicationConfigurationName' => [ 'shape' => 'ApplicationConfigurationName', 'location' => 'querystring', 'locationName' => 'adsApplicationConfigurationName', ], 'status' => [ 'shape' => 'MigrationWorkflowStatusEnum', 'location' => 'querystring', 'locationName' => 'status', ], 'name' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'name', ], ], ], 'ListMigrationWorkflowsResponse' => [ 'type' => 'structure', 'required' => [ 'migrationWorkflowSummary', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'migrationWorkflowSummary' => [ 'shape' => 'MigrationWorkflowSummaryList', ], ], ], 'ListPluginsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListPluginsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'plugins' => [ 'shape' => 'PluginSummaries', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ListTemplateStepGroupsRequest' => [ 'type' => 'structure', 'required' => [ 'templateId', ], 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'templateId' => [ 'shape' => 'TemplateId', 'location' => 'uri', 'locationName' => 'templateId', ], ], ], 'ListTemplateStepGroupsResponse' => [ 'type' => 'structure', 'required' => [ 'templateStepGroupSummary', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'templateStepGroupSummary' => [ 'shape' => 'TemplateStepGroupSummaryList', ], ], ], 'ListTemplateStepsRequest' => [ 'type' => 'structure', 'required' => [ 'templateId', 'stepGroupId', ], 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'templateId' => [ 'shape' => 'TemplateId', 'location' => 'querystring', 'locationName' => 'templateId', ], 'stepGroupId' => [ 'shape' => 'StepGroupId', 'location' => 'querystring', 'locationName' => 'stepGroupId', ], ], ], 'ListTemplateStepsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'templateStepSummaryList' => [ 'shape' => 'TemplateStepSummaryList', ], ], ], 'ListWorkflowStepGroupsRequest' => [ 'type' => 'structure', 'required' => [ 'workflowId', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'workflowId' => [ 'shape' => 'MigrationWorkflowId', 'location' => 'querystring', 'locationName' => 'workflowId', ], ], ], 'ListWorkflowStepGroupsResponse' => [ 'type' => 'structure', 'required' => [ 'workflowStepGroupsSummary', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'workflowStepGroupsSummary' => [ 'shape' => 'WorkflowStepGroupsSummaryList', ], ], ], 'ListWorkflowStepsRequest' => [ 'type' => 'structure', 'required' => [ 'workflowId', 'stepGroupId', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'workflowId' => [ 'shape' => 'MigrationWorkflowId', 'location' => 'uri', 'locationName' => 'workflowId', ], 'stepGroupId' => [ 'shape' => 'StepGroupId', 'location' => 'uri', 'locationName' => 'stepGroupId', ], ], ], 'ListWorkflowStepsResponse' => [ 'type' => 'structure', 'required' => [ 'workflowStepsSummary', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'workflowStepsSummary' => [ 'shape' => 'WorkflowStepsSummaryList', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'MaxStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MaxStringValue', ], ], 'MaxStringValue' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, ], 'MigrationWorkflowDescription' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'pattern' => '[-a-zA-Z0-9_.+, ]*', ], 'MigrationWorkflowId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[a-zA-Z0-9-]+', ], 'MigrationWorkflowName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[-a-zA-Z0-9_.+]+[-a-zA-Z0-9_.+ ]*', ], 'MigrationWorkflowStatusEnum' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'NOT_STARTED', 'CREATION_FAILED', 'STARTING', 'IN_PROGRESS', 'WORKFLOW_FAILED', 'PAUSED', 'PAUSING', 'PAUSING_FAILED', 'USER_ATTENTION_REQUIRED', 'DELETING', 'DELETION_FAILED', 'DELETED', 'COMPLETED', ], ], 'MigrationWorkflowSummary' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'MigrationWorkflowId', ], 'name' => [ 'shape' => 'String', ], 'templateId' => [ 'shape' => 'String', ], 'adsApplicationConfigurationName' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'MigrationWorkflowStatusEnum', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'statusMessage' => [ 'shape' => 'String', ], 'completedSteps' => [ 'shape' => 'Integer', ], 'totalSteps' => [ 'shape' => 'Integer', ], ], ], 'MigrationWorkflowSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MigrationWorkflowSummary', ], ], 'NextToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '.*\\S.*', ], 'Owner' => [ 'type' => 'string', 'enum' => [ 'AWS_MANAGED', 'CUSTOM', ], ], 'PlatformCommand' => [ 'type' => 'structure', 'members' => [ 'linux' => [ 'shape' => 'String', ], 'windows' => [ 'shape' => 'String', ], ], ], 'PlatformScriptKey' => [ 'type' => 'structure', 'members' => [ 'linux' => [ 'shape' => 'S3Key', ], 'windows' => [ 'shape' => 'S3Key', ], ], ], 'PluginHealth' => [ 'type' => 'string', 'enum' => [ 'HEALTHY', 'UNHEALTHY', ], ], 'PluginId' => [ 'type' => 'string', 'max' => 60, 'min' => 1, 'pattern' => '.*\\S.*', ], 'PluginSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'PluginSummary', ], ], 'PluginSummary' => [ 'type' => 'structure', 'members' => [ 'pluginId' => [ 'shape' => 'PluginId', ], 'hostname' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'PluginHealth', ], 'ipAddress' => [ 'shape' => 'IPAddress', ], 'version' => [ 'shape' => 'PluginVersion', ], 'registeredTime' => [ 'shape' => 'String', ], ], ], 'PluginVersion' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '.*', ], 'ResourceArn' => [ 'type' => 'string', 'pattern' => 'arn:aws:migrationhub-orchestrator:[a-z0-9-]+:[0-9]+:(template|workflow)/[.]*', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'RetryWorkflowStepRequest' => [ 'type' => 'structure', 'required' => [ 'workflowId', 'stepGroupId', 'id', ], 'members' => [ 'workflowId' => [ 'shape' => 'MigrationWorkflowId', 'location' => 'querystring', 'locationName' => 'workflowId', ], 'stepGroupId' => [ 'shape' => 'StepGroupId', 'location' => 'querystring', 'locationName' => 'stepGroupId', ], 'id' => [ 'shape' => 'StepId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'RetryWorkflowStepResponse' => [ 'type' => 'structure', 'members' => [ 'stepGroupId' => [ 'shape' => 'String', ], 'workflowId' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'StepStatus', ], ], ], 'RunEnvironment' => [ 'type' => 'string', 'enum' => [ 'AWS', 'ONPREMISE', ], ], 'S3Bucket' => [ 'type' => 'string', 'max' => 63, 'min' => 0, 'pattern' => '[0-9a-z]+[0-9a-z\\.\\-]*[0-9a-z]+', ], 'S3Key' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'StartMigrationWorkflowRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'MigrationWorkflowId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'StartMigrationWorkflowResponse' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'MigrationWorkflowId', ], 'arn' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'MigrationWorkflowStatusEnum', ], 'statusMessage' => [ 'shape' => 'String', ], 'lastStartTime' => [ 'shape' => 'Timestamp', ], ], ], 'StepActionType' => [ 'type' => 'string', 'enum' => [ 'MANUAL', 'AUTOMATED', ], ], 'StepAutomationConfiguration' => [ 'type' => 'structure', 'members' => [ 'scriptLocationS3Bucket' => [ 'shape' => 'String', ], 'scriptLocationS3Key' => [ 'shape' => 'PlatformScriptKey', ], 'command' => [ 'shape' => 'PlatformCommand', ], 'runEnvironment' => [ 'shape' => 'RunEnvironment', ], 'targetType' => [ 'shape' => 'TargetType', ], ], ], 'StepDescription' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'pattern' => '[-a-zA-Z0-9_.+, ]*', ], 'StepGroupDescription' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'pattern' => '[-a-zA-Z0-9_.+, ]*', ], 'StepGroupId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[a-zA-Z0-9-]+', ], 'StepGroupName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[-a-zA-Z0-9_.+]+[-a-zA-Z0-9_.+ ]*', ], 'StepGroupStatus' => [ 'type' => 'string', 'enum' => [ 'AWAITING_DEPENDENCIES', 'READY', 'IN_PROGRESS', 'COMPLETED', 'FAILED', 'PAUSED', 'PAUSING', 'USER_ATTENTION_REQUIRED', ], ], 'StepId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[a-zA-Z0-9-]+', ], 'StepInput' => [ 'type' => 'structure', 'members' => [ 'integerValue' => [ 'shape' => 'Integer', ], 'stringValue' => [ 'shape' => 'StringValue', ], 'listOfStringsValue' => [ 'shape' => 'StringList', ], 'mapOfStringValue' => [ 'shape' => 'StringMap', ], ], 'union' => true, ], 'StepInputParameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'StepInputParametersKey', ], 'value' => [ 'shape' => 'StepInput', ], 'sensitive' => true, ], 'StepInputParametersKey' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[a-zA-Z0-9-_ ()]+', ], 'StepName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[-a-zA-Z0-9_.+]+[-a-zA-Z0-9_.+ ]*', ], 'StepOutput' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'dataType' => [ 'shape' => 'DataType', ], 'required' => [ 'shape' => 'Boolean', ], ], ], 'StepOutputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StepOutput', ], ], 'StepStatus' => [ 'type' => 'string', 'enum' => [ 'AWAITING_DEPENDENCIES', 'SKIPPED', 'READY', 'IN_PROGRESS', 'COMPLETED', 'FAILED', 'PAUSED', 'USER_ATTENTION_REQUIRED', ], ], 'StopMigrationWorkflowRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'MigrationWorkflowId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'StopMigrationWorkflowResponse' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'MigrationWorkflowId', ], 'arn' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'MigrationWorkflowStatusEnum', ], 'statusMessage' => [ 'shape' => 'String', ], 'lastStopTime' => [ 'shape' => 'Timestamp', ], ], ], 'String' => [ 'type' => 'string', ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StringListMember', ], ], 'StringListMember' => [ 'type' => 'string', 'max' => 500, 'min' => 0, ], 'StringMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'StringMapKey', ], 'value' => [ 'shape' => 'StringMapValue', ], ], 'StringMapKey' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[a-zA-Z0-9-_ ()]+', ], 'StringMapValue' => [ 'type' => 'string', 'max' => 100, 'min' => 0, ], 'StringValue' => [ 'type' => 'string', 'max' => 100, 'min' => 0, ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '(?!aws:)[a-zA-Z+-=._:/]+', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 0, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TargetType' => [ 'type' => 'string', 'enum' => [ 'SINGLE', 'ALL', 'NONE', ], ], 'TemplateId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[-a-zA-Z0-9_.+]+[-a-zA-Z0-9_.+ ]*', ], 'TemplateInput' => [ 'type' => 'structure', 'members' => [ 'inputName' => [ 'shape' => 'TemplateInputName', ], 'dataType' => [ 'shape' => 'DataType', ], 'required' => [ 'shape' => 'Boolean', ], ], ], 'TemplateInputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TemplateInput', ], ], 'TemplateInputName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[-a-zA-Z0-9_.+]+[-a-zA-Z0-9_.+ ]*', ], 'TemplateName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[-a-zA-Z0-9_.+]+[-a-zA-Z0-9_.+ ]*', ], 'TemplateSource' => [ 'type' => 'structure', 'members' => [ 'workflowId' => [ 'shape' => 'MigrationWorkflowId', ], ], 'union' => true, ], 'TemplateStatus' => [ 'type' => 'string', 'enum' => [ 'CREATED', 'READY', 'PENDING_CREATION', 'CREATING', 'CREATION_FAILED', ], ], 'TemplateStepGroupSummary' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], 'previous' => [ 'shape' => 'StringList', ], 'next' => [ 'shape' => 'StringList', ], ], ], 'TemplateStepGroupSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TemplateStepGroupSummary', ], ], 'TemplateStepSummary' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'String', ], 'stepGroupId' => [ 'shape' => 'String', ], 'templateId' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], 'stepActionType' => [ 'shape' => 'StepActionType', ], 'targetType' => [ 'shape' => 'TargetType', ], 'owner' => [ 'shape' => 'Owner', ], 'previous' => [ 'shape' => 'StringList', ], 'next' => [ 'shape' => 'StringList', ], ], ], 'TemplateStepSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TemplateStepSummary', ], ], 'TemplateSummary' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], 'arn' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'String', ], ], ], 'TemplateSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TemplateSummary', ], ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'Tool' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'url' => [ 'shape' => 'String', ], ], ], 'ToolsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tool', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateMigrationWorkflowRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'MigrationWorkflowId', 'location' => 'uri', 'locationName' => 'id', ], 'name' => [ 'shape' => 'UpdateMigrationWorkflowRequestNameString', ], 'description' => [ 'shape' => 'UpdateMigrationWorkflowRequestDescriptionString', ], 'inputParameters' => [ 'shape' => 'StepInputParameters', ], 'stepTargets' => [ 'shape' => 'StringList', ], ], ], 'UpdateMigrationWorkflowRequestDescriptionString' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'pattern' => '[-a-zA-Z0-9_.+, ]*', ], 'UpdateMigrationWorkflowRequestNameString' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[-a-zA-Z0-9_.+]+[-a-zA-Z0-9_.+ ]*', ], 'UpdateMigrationWorkflowResponse' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'MigrationWorkflowId', ], 'arn' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'String', ], 'templateId' => [ 'shape' => 'String', ], 'adsApplicationConfigurationId' => [ 'shape' => 'String', ], 'workflowInputs' => [ 'shape' => 'StepInputParameters', ], 'stepTargets' => [ 'shape' => 'StringList', ], 'status' => [ 'shape' => 'MigrationWorkflowStatusEnum', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'lastModifiedTime' => [ 'shape' => 'Timestamp', ], 'tags' => [ 'shape' => 'StringMap', ], ], ], 'UpdateTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'TemplateId', 'location' => 'uri', 'locationName' => 'id', ], 'templateName' => [ 'shape' => 'UpdateTemplateRequestTemplateNameString', ], 'templateDescription' => [ 'shape' => 'UpdateTemplateRequestTemplateDescriptionString', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'UpdateTemplateRequestTemplateDescriptionString' => [ 'type' => 'string', 'max' => 250, 'min' => 0, 'pattern' => '.*', ], 'UpdateTemplateRequestTemplateNameString' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[ a-zA-Z0-9]*', ], 'UpdateTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'templateId' => [ 'shape' => 'String', ], 'templateArn' => [ 'shape' => 'String', ], 'tags' => [ 'shape' => 'StringMap', ], ], ], 'UpdateWorkflowStepGroupRequest' => [ 'type' => 'structure', 'required' => [ 'workflowId', 'id', ], 'members' => [ 'workflowId' => [ 'shape' => 'MigrationWorkflowId', 'location' => 'querystring', 'locationName' => 'workflowId', ], 'id' => [ 'shape' => 'StepGroupId', 'location' => 'uri', 'locationName' => 'id', ], 'name' => [ 'shape' => 'StepGroupName', ], 'description' => [ 'shape' => 'StepGroupDescription', ], 'next' => [ 'shape' => 'StringList', ], 'previous' => [ 'shape' => 'StringList', ], ], ], 'UpdateWorkflowStepGroupResponse' => [ 'type' => 'structure', 'members' => [ 'workflowId' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'String', ], 'tools' => [ 'shape' => 'ToolsList', ], 'next' => [ 'shape' => 'StringList', ], 'previous' => [ 'shape' => 'StringList', ], 'lastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateWorkflowStepRequest' => [ 'type' => 'structure', 'required' => [ 'id', 'stepGroupId', 'workflowId', ], 'members' => [ 'id' => [ 'shape' => 'StepId', 'location' => 'uri', 'locationName' => 'id', ], 'stepGroupId' => [ 'shape' => 'StepGroupId', ], 'workflowId' => [ 'shape' => 'MigrationWorkflowId', ], 'name' => [ 'shape' => 'StepName', ], 'description' => [ 'shape' => 'StepDescription', ], 'stepActionType' => [ 'shape' => 'StepActionType', ], 'workflowStepAutomationConfiguration' => [ 'shape' => 'WorkflowStepAutomationConfiguration', ], 'stepTarget' => [ 'shape' => 'StringList', ], 'outputs' => [ 'shape' => 'WorkflowStepOutputList', ], 'previous' => [ 'shape' => 'StringList', ], 'next' => [ 'shape' => 'StringList', ], 'status' => [ 'shape' => 'StepStatus', ], ], ], 'UpdateWorkflowStepResponse' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'StepId', ], 'stepGroupId' => [ 'shape' => 'String', ], 'workflowId' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'WorkflowStepAutomationConfiguration' => [ 'type' => 'structure', 'members' => [ 'scriptLocationS3Bucket' => [ 'shape' => 'S3Bucket', ], 'scriptLocationS3Key' => [ 'shape' => 'PlatformScriptKey', ], 'command' => [ 'shape' => 'PlatformCommand', ], 'runEnvironment' => [ 'shape' => 'RunEnvironment', ], 'targetType' => [ 'shape' => 'TargetType', ], ], ], 'WorkflowStepGroupSummary' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], 'owner' => [ 'shape' => 'Owner', ], 'status' => [ 'shape' => 'StepGroupStatus', ], 'previous' => [ 'shape' => 'StringList', ], 'next' => [ 'shape' => 'StringList', ], ], ], 'WorkflowStepGroupsSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkflowStepGroupSummary', ], ], 'WorkflowStepOutput' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'WorkflowStepOutputName', ], 'dataType' => [ 'shape' => 'DataType', ], 'required' => [ 'shape' => 'Boolean', ], 'value' => [ 'shape' => 'WorkflowStepOutputUnion', ], ], ], 'WorkflowStepOutputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkflowStepOutput', ], ], 'WorkflowStepOutputName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[-a-zA-Z0-9_.+]+[-a-zA-Z0-9_.+ ]*', ], 'WorkflowStepOutputUnion' => [ 'type' => 'structure', 'members' => [ 'integerValue' => [ 'shape' => 'Integer', ], 'stringValue' => [ 'shape' => 'MaxStringValue', ], 'listOfStringValue' => [ 'shape' => 'MaxStringList', ], ], 'union' => true, ], 'WorkflowStepSummary' => [ 'type' => 'structure', 'members' => [ 'stepId' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], 'stepActionType' => [ 'shape' => 'StepActionType', ], 'owner' => [ 'shape' => 'Owner', ], 'previous' => [ 'shape' => 'StringList', ], 'next' => [ 'shape' => 'StringList', ], 'status' => [ 'shape' => 'StepStatus', ], 'statusMessage' => [ 'shape' => 'String', ], 'noOfSrvCompleted' => [ 'shape' => 'Integer', ], 'noOfSrvFailed' => [ 'shape' => 'Integer', ], 'totalNoOfSrv' => [ 'shape' => 'Integer', ], 'description' => [ 'shape' => 'String', ], 'scriptLocation' => [ 'shape' => 'String', ], ], ], 'WorkflowStepsSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkflowStepSummary', ], ], ],];
