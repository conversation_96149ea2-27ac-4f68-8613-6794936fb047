<?php
// This file was auto-generated from sdk-root/src/data/sagemaker-geospatial/2020-05-27/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-05-27', 'endpointPrefix' => 'sagemaker-geospatial', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'Amazon SageMaker geospatial capabilities', 'serviceId' => 'SageMaker Geospatial', 'signatureVersion' => 'v4', 'signingName' => 'sagemaker-geospatial', 'uid' => 'sagemaker-geospatial-2020-05-27', ], 'operations' => [ 'DeleteEarthObservationJob' => [ 'name' => 'DeleteEarthObservationJob', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/earth-observation-jobs/{Arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteEarthObservationJobInput', ], 'output' => [ 'shape' => 'DeleteEarthObservationJobOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteVectorEnrichmentJob' => [ 'name' => 'DeleteVectorEnrichmentJob', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/vector-enrichment-jobs/{Arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteVectorEnrichmentJobInput', ], 'output' => [ 'shape' => 'DeleteVectorEnrichmentJobOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'ExportEarthObservationJob' => [ 'name' => 'ExportEarthObservationJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/export-earth-observation-job', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ExportEarthObservationJobInput', ], 'output' => [ 'shape' => 'ExportEarthObservationJobOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], ], 'ExportVectorEnrichmentJob' => [ 'name' => 'ExportVectorEnrichmentJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/export-vector-enrichment-jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ExportVectorEnrichmentJobInput', ], 'output' => [ 'shape' => 'ExportVectorEnrichmentJobOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetEarthObservationJob' => [ 'name' => 'GetEarthObservationJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/earth-observation-jobs/{Arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetEarthObservationJobInput', ], 'output' => [ 'shape' => 'GetEarthObservationJobOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetRasterDataCollection' => [ 'name' => 'GetRasterDataCollection', 'http' => [ 'method' => 'GET', 'requestUri' => '/raster-data-collection/{Arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetRasterDataCollectionInput', ], 'output' => [ 'shape' => 'GetRasterDataCollectionOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetTile' => [ 'name' => 'GetTile', 'http' => [ 'method' => 'GET', 'requestUri' => '/tile/{z}/{x}/{y}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTileInput', ], 'output' => [ 'shape' => 'GetTileOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetVectorEnrichmentJob' => [ 'name' => 'GetVectorEnrichmentJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/vector-enrichment-jobs/{Arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetVectorEnrichmentJobInput', ], 'output' => [ 'shape' => 'GetVectorEnrichmentJobOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListEarthObservationJobs' => [ 'name' => 'ListEarthObservationJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-earth-observation-jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListEarthObservationJobInput', ], 'output' => [ 'shape' => 'ListEarthObservationJobOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListRasterDataCollections' => [ 'name' => 'ListRasterDataCollections', 'http' => [ 'method' => 'GET', 'requestUri' => '/raster-data-collections', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListRasterDataCollectionsInput', ], 'output' => [ 'shape' => 'ListRasterDataCollectionsOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListVectorEnrichmentJobs' => [ 'name' => 'ListVectorEnrichmentJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-vector-enrichment-jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListVectorEnrichmentJobInput', ], 'output' => [ 'shape' => 'ListVectorEnrichmentJobOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'SearchRasterDataCollection' => [ 'name' => 'SearchRasterDataCollection', 'http' => [ 'method' => 'POST', 'requestUri' => '/search-raster-data-collection', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchRasterDataCollectionInput', ], 'output' => [ 'shape' => 'SearchRasterDataCollectionOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'StartEarthObservationJob' => [ 'name' => 'StartEarthObservationJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/earth-observation-jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartEarthObservationJobInput', ], 'output' => [ 'shape' => 'StartEarthObservationJobOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'StartVectorEnrichmentJob' => [ 'name' => 'StartVectorEnrichmentJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/vector-enrichment-jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartVectorEnrichmentJobInput', ], 'output' => [ 'shape' => 'StartVectorEnrichmentJobOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'StopEarthObservationJob' => [ 'name' => 'StopEarthObservationJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/earth-observation-jobs/stop', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopEarthObservationJobInput', ], 'output' => [ 'shape' => 'StopEarthObservationJobOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], ], 'StopVectorEnrichmentJob' => [ 'name' => 'StopVectorEnrichmentJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/vector-enrichment-jobs/stop', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopVectorEnrichmentJobInput', ], 'output' => [ 'shape' => 'StopVectorEnrichmentJobOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'PUT', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AlgorithmNameCloudRemoval' => [ 'type' => 'string', 'enum' => [ 'INTERPOLATION', ], ], 'AlgorithmNameGeoMosaic' => [ 'type' => 'string', 'enum' => [ 'NEAR', 'BILINEAR', 'CUBIC', 'CUBICSPLINE', 'LANCZOS', 'AVERAGE', 'RMS', 'MODE', 'MAX', 'MIN', 'MED', 'Q1', 'Q3', 'SUM', ], ], 'AlgorithmNameResampling' => [ 'type' => 'string', 'enum' => [ 'NEAR', 'BILINEAR', 'CUBIC', 'CUBICSPLINE', 'LANCZOS', 'AVERAGE', 'RMS', 'MODE', 'MAX', 'MIN', 'MED', 'Q1', 'Q3', 'SUM', ], ], 'AreaOfInterest' => [ 'type' => 'structure', 'members' => [ 'AreaOfInterestGeometry' => [ 'shape' => 'AreaOfInterestGeometry', ], ], 'union' => true, ], 'AreaOfInterestGeometry' => [ 'type' => 'structure', 'members' => [ 'MultiPolygonGeometry' => [ 'shape' => 'MultiPolygonGeometryInput', ], 'PolygonGeometry' => [ 'shape' => 'PolygonGeometryInput', ], ], 'union' => true, ], 'Arn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'AssetValue' => [ 'type' => 'structure', 'members' => [ 'Href' => [ 'shape' => 'String', ], ], ], 'AssetsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'AssetValue', ], ], 'BandMathConfigInput' => [ 'type' => 'structure', 'members' => [ 'CustomIndices' => [ 'shape' => 'CustomIndicesInput', ], 'PredefinedIndices' => [ 'shape' => 'StringListInput', ], ], ], 'BinaryFile' => [ 'type' => 'blob', 'streaming' => true, ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'CloudMaskingConfigInput' => [ 'type' => 'structure', 'members' => [], ], 'CloudRemovalConfigInput' => [ 'type' => 'structure', 'members' => [ 'AlgorithmName' => [ 'shape' => 'AlgorithmNameCloudRemoval', ], 'InterpolationValue' => [ 'shape' => 'String', ], 'TargetBands' => [ 'shape' => 'StringListInput', ], ], ], 'ComparisonOperator' => [ 'type' => 'string', 'enum' => [ 'EQUALS', 'NOT_EQUALS', 'STARTS_WITH', ], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ResourceId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CustomIndicesInput' => [ 'type' => 'structure', 'members' => [ 'Operations' => [ 'shape' => 'OperationsListInput', ], ], ], 'DataCollectionArn' => [ 'type' => 'string', 'pattern' => '^arn:aws[a-z-]{0,12}:sagemaker-geospatial:[a-z0-9-]{1,25}:[0-9]{12}:raster-data-collection/(public|premium|user)/[a-z0-9]{12,}$', ], 'DataCollectionType' => [ 'type' => 'string', 'enum' => [ 'PUBLIC', 'PREMIUM', 'USER', ], ], 'DataCollectionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RasterDataCollectionMetadata', ], ], 'DeleteEarthObservationJobInput' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'EarthObservationJobArn', 'location' => 'uri', 'locationName' => 'Arn', ], ], ], 'DeleteEarthObservationJobOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteVectorEnrichmentJobInput' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'VectorEnrichmentJobArn', 'location' => 'uri', 'locationName' => 'Arn', ], ], ], 'DeleteVectorEnrichmentJobOutput' => [ 'type' => 'structure', 'members' => [], ], 'Double' => [ 'type' => 'double', 'box' => true, ], 'EarthObservationJobArn' => [ 'type' => 'string', 'pattern' => '^arn:aws[a-z-]{0,12}:sagemaker-geospatial:[a-z0-9-]{1,25}:[0-9]{12}:earth-observation-job/[a-z0-9]{12,}$', ], 'EarthObservationJobErrorDetails' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'Type' => [ 'shape' => 'EarthObservationJobErrorType', ], ], ], 'EarthObservationJobErrorType' => [ 'type' => 'string', 'enum' => [ 'CLIENT_ERROR', 'SERVER_ERROR', ], ], 'EarthObservationJobExportStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'SUCCEEDED', 'FAILED', ], ], 'EarthObservationJobList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListEarthObservationJobOutputConfig', ], ], 'EarthObservationJobOutputBands' => [ 'type' => 'list', 'member' => [ 'shape' => 'OutputBand', ], ], 'EarthObservationJobStatus' => [ 'type' => 'string', 'enum' => [ 'INITIALIZING', 'IN_PROGRESS', 'STOPPING', 'COMPLETED', 'STOPPED', 'FAILED', 'DELETING', 'DELETED', ], ], 'EoCloudCoverInput' => [ 'type' => 'structure', 'required' => [ 'LowerBound', 'UpperBound', ], 'members' => [ 'LowerBound' => [ 'shape' => 'Float', ], 'UpperBound' => [ 'shape' => 'Float', ], ], ], 'ExecutionRoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:(aws[a-z-]*):iam::([0-9]{12}):role/[a-zA-Z0-9+=,.@_/-]+$', ], 'ExportEarthObservationJobInput' => [ 'type' => 'structure', 'required' => [ 'Arn', 'ExecutionRoleArn', 'OutputConfig', ], 'members' => [ 'Arn' => [ 'shape' => 'EarthObservationJobArn', ], 'ClientToken' => [ 'shape' => 'ExportEarthObservationJobInputClientTokenString', 'idempotencyToken' => true, ], 'ExecutionRoleArn' => [ 'shape' => 'ExecutionRoleArn', ], 'ExportSourceImages' => [ 'shape' => 'Boolean', ], 'OutputConfig' => [ 'shape' => 'OutputConfigInput', ], ], ], 'ExportEarthObservationJobInputClientTokenString' => [ 'type' => 'string', 'max' => 64, 'min' => 36, ], 'ExportEarthObservationJobOutput' => [ 'type' => 'structure', 'required' => [ 'Arn', 'CreationTime', 'ExecutionRoleArn', 'ExportStatus', 'OutputConfig', ], 'members' => [ 'Arn' => [ 'shape' => 'EarthObservationJobArn', ], 'CreationTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'ExecutionRoleArn' => [ 'shape' => 'ExecutionRoleArn', ], 'ExportSourceImages' => [ 'shape' => 'Boolean', ], 'ExportStatus' => [ 'shape' => 'EarthObservationJobExportStatus', ], 'OutputConfig' => [ 'shape' => 'OutputConfigInput', ], ], ], 'ExportErrorDetails' => [ 'type' => 'structure', 'members' => [ 'ExportResults' => [ 'shape' => 'ExportErrorDetailsOutput', ], 'ExportSourceImages' => [ 'shape' => 'ExportErrorDetailsOutput', ], ], ], 'ExportErrorDetailsOutput' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'Type' => [ 'shape' => 'ExportErrorType', ], ], ], 'ExportErrorType' => [ 'type' => 'string', 'enum' => [ 'CLIENT_ERROR', 'SERVER_ERROR', ], ], 'ExportS3DataInput' => [ 'type' => 'structure', 'required' => [ 'S3Uri', ], 'members' => [ 'KmsKeyId' => [ 'shape' => 'KmsKey', ], 'S3Uri' => [ 'shape' => 'S3Uri', ], ], ], 'ExportVectorEnrichmentJobInput' => [ 'type' => 'structure', 'required' => [ 'Arn', 'ExecutionRoleArn', 'OutputConfig', ], 'members' => [ 'Arn' => [ 'shape' => 'VectorEnrichmentJobArn', ], 'ClientToken' => [ 'shape' => 'ExportVectorEnrichmentJobInputClientTokenString', 'idempotencyToken' => true, ], 'ExecutionRoleArn' => [ 'shape' => 'ExecutionRoleArn', ], 'OutputConfig' => [ 'shape' => 'ExportVectorEnrichmentJobOutputConfig', ], ], ], 'ExportVectorEnrichmentJobInputClientTokenString' => [ 'type' => 'string', 'max' => 64, 'min' => 36, ], 'ExportVectorEnrichmentJobOutput' => [ 'type' => 'structure', 'required' => [ 'Arn', 'CreationTime', 'ExecutionRoleArn', 'ExportStatus', 'OutputConfig', ], 'members' => [ 'Arn' => [ 'shape' => 'VectorEnrichmentJobArn', ], 'CreationTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'ExecutionRoleArn' => [ 'shape' => 'ExecutionRoleArn', ], 'ExportStatus' => [ 'shape' => 'VectorEnrichmentJobExportStatus', ], 'OutputConfig' => [ 'shape' => 'ExportVectorEnrichmentJobOutputConfig', ], ], ], 'ExportVectorEnrichmentJobOutputConfig' => [ 'type' => 'structure', 'required' => [ 'S3Data', ], 'members' => [ 'S3Data' => [ 'shape' => 'VectorEnrichmentJobS3Data', ], ], ], 'Filter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Type', ], 'members' => [ 'Maximum' => [ 'shape' => 'Float', ], 'Minimum' => [ 'shape' => 'Float', ], 'Name' => [ 'shape' => 'String', ], 'Type' => [ 'shape' => 'String', ], ], ], 'FilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], ], 'Float' => [ 'type' => 'float', 'box' => true, ], 'GeoMosaicConfigInput' => [ 'type' => 'structure', 'members' => [ 'AlgorithmName' => [ 'shape' => 'AlgorithmNameGeoMosaic', ], 'TargetBands' => [ 'shape' => 'StringListInput', ], ], ], 'Geometry' => [ 'type' => 'structure', 'required' => [ 'Coordinates', 'Type', ], 'members' => [ 'Coordinates' => [ 'shape' => 'LinearRings', ], 'Type' => [ 'shape' => 'String', ], ], ], 'GetEarthObservationJobInput' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'EarthObservationJobArn', 'location' => 'uri', 'locationName' => 'Arn', ], ], ], 'GetEarthObservationJobOutput' => [ 'type' => 'structure', 'required' => [ 'Arn', 'CreationTime', 'DurationInSeconds', 'InputConfig', 'JobConfig', 'Name', 'Status', ], 'members' => [ 'Arn' => [ 'shape' => 'String', ], 'CreationTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'DurationInSeconds' => [ 'shape' => 'Integer', ], 'ErrorDetails' => [ 'shape' => 'EarthObservationJobErrorDetails', ], 'ExecutionRoleArn' => [ 'shape' => 'ExecutionRoleArn', ], 'ExportErrorDetails' => [ 'shape' => 'ExportErrorDetails', ], 'ExportStatus' => [ 'shape' => 'EarthObservationJobExportStatus', ], 'InputConfig' => [ 'shape' => 'InputConfigOutput', ], 'JobConfig' => [ 'shape' => 'JobConfigInput', ], 'KmsKeyId' => [ 'shape' => 'KmsKey', ], 'Name' => [ 'shape' => 'String', ], 'OutputBands' => [ 'shape' => 'EarthObservationJobOutputBands', ], 'Status' => [ 'shape' => 'EarthObservationJobStatus', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'GetRasterDataCollectionInput' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'DataCollectionArn', 'location' => 'uri', 'locationName' => 'Arn', ], ], ], 'GetRasterDataCollectionOutput' => [ 'type' => 'structure', 'required' => [ 'Arn', 'Description', 'DescriptionPageUrl', 'ImageSourceBands', 'Name', 'SupportedFilters', 'Type', ], 'members' => [ 'Arn' => [ 'shape' => 'DataCollectionArn', ], 'Description' => [ 'shape' => 'String', ], 'DescriptionPageUrl' => [ 'shape' => 'String', ], 'ImageSourceBands' => [ 'shape' => 'ImageSourceBandList', ], 'Name' => [ 'shape' => 'String', ], 'SupportedFilters' => [ 'shape' => 'FilterList', ], 'Tags' => [ 'shape' => 'Tags', ], 'Type' => [ 'shape' => 'DataCollectionType', ], ], ], 'GetTileInput' => [ 'type' => 'structure', 'required' => [ 'Arn', 'ImageAssets', 'Target', 'x', 'y', 'z', ], 'members' => [ 'Arn' => [ 'shape' => 'EarthObservationJobArn', 'location' => 'querystring', 'locationName' => 'Arn', ], 'ExecutionRoleArn' => [ 'shape' => 'ExecutionRoleArn', 'location' => 'querystring', 'locationName' => 'ExecutionRoleArn', ], 'ImageAssets' => [ 'shape' => 'StringListInput', 'location' => 'querystring', 'locationName' => 'ImageAssets', ], 'ImageMask' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'ImageMask', ], 'OutputDataType' => [ 'shape' => 'OutputType', 'location' => 'querystring', 'locationName' => 'OutputDataType', ], 'OutputFormat' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'OutputFormat', ], 'PropertyFilters' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'PropertyFilters', ], 'Target' => [ 'shape' => 'TargetOptions', 'location' => 'querystring', 'locationName' => 'Target', ], 'TimeRangeFilter' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'TimeRangeFilter', ], 'x' => [ 'shape' => 'Integer', 'location' => 'uri', 'locationName' => 'x', ], 'y' => [ 'shape' => 'Integer', 'location' => 'uri', 'locationName' => 'y', ], 'z' => [ 'shape' => 'Integer', 'location' => 'uri', 'locationName' => 'z', ], ], ], 'GetTileOutput' => [ 'type' => 'structure', 'members' => [ 'BinaryFile' => [ 'shape' => 'BinaryFile', ], ], 'payload' => 'BinaryFile', ], 'GetVectorEnrichmentJobInput' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'VectorEnrichmentJobArn', 'location' => 'uri', 'locationName' => 'Arn', ], ], ], 'GetVectorEnrichmentJobOutput' => [ 'type' => 'structure', 'required' => [ 'Arn', 'CreationTime', 'DurationInSeconds', 'ExecutionRoleArn', 'InputConfig', 'JobConfig', 'Name', 'Status', 'Type', ], 'members' => [ 'Arn' => [ 'shape' => 'String', ], 'CreationTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'DurationInSeconds' => [ 'shape' => 'Integer', ], 'ErrorDetails' => [ 'shape' => 'VectorEnrichmentJobErrorDetails', ], 'ExecutionRoleArn' => [ 'shape' => 'ExecutionRoleArn', ], 'ExportErrorDetails' => [ 'shape' => 'VectorEnrichmentJobExportErrorDetails', ], 'ExportStatus' => [ 'shape' => 'VectorEnrichmentJobExportStatus', ], 'InputConfig' => [ 'shape' => 'VectorEnrichmentJobInputConfig', ], 'JobConfig' => [ 'shape' => 'VectorEnrichmentJobConfig', ], 'KmsKeyId' => [ 'shape' => 'KmsKey', ], 'Name' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'VectorEnrichmentJobStatus', ], 'Tags' => [ 'shape' => 'Tags', ], 'Type' => [ 'shape' => 'VectorEnrichmentJobType', ], ], ], 'GroupBy' => [ 'type' => 'string', 'enum' => [ 'ALL', 'YEARLY', ], ], 'ImageSourceBandList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'InputConfigInput' => [ 'type' => 'structure', 'members' => [ 'PreviousEarthObservationJobArn' => [ 'shape' => 'EarthObservationJobArn', ], 'RasterDataCollectionQuery' => [ 'shape' => 'RasterDataCollectionQueryInput', ], ], ], 'InputConfigOutput' => [ 'type' => 'structure', 'members' => [ 'PreviousEarthObservationJobArn' => [ 'shape' => 'EarthObservationJobArn', ], 'RasterDataCollectionQuery' => [ 'shape' => 'RasterDataCollectionQueryOutput', ], ], ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ResourceId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'ItemSource' => [ 'type' => 'structure', 'required' => [ 'DateTime', 'Geometry', 'Id', ], 'members' => [ 'Assets' => [ 'shape' => 'AssetsMap', ], 'DateTime' => [ 'shape' => 'Timestamp', ], 'Geometry' => [ 'shape' => 'Geometry', ], 'Id' => [ 'shape' => 'String', ], 'Properties' => [ 'shape' => 'Properties', ], ], ], 'ItemSourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ItemSource', ], ], 'JobConfigInput' => [ 'type' => 'structure', 'members' => [ 'BandMathConfig' => [ 'shape' => 'BandMathConfigInput', ], 'CloudMaskingConfig' => [ 'shape' => 'CloudMaskingConfigInput', ], 'CloudRemovalConfig' => [ 'shape' => 'CloudRemovalConfigInput', ], 'GeoMosaicConfig' => [ 'shape' => 'GeoMosaicConfigInput', ], 'LandCoverSegmentationConfig' => [ 'shape' => 'LandCoverSegmentationConfigInput', ], 'ResamplingConfig' => [ 'shape' => 'ResamplingConfigInput', ], 'StackConfig' => [ 'shape' => 'StackConfigInput', ], 'TemporalStatisticsConfig' => [ 'shape' => 'TemporalStatisticsConfigInput', ], 'ZonalStatisticsConfig' => [ 'shape' => 'ZonalStatisticsConfigInput', ], ], 'union' => true, ], 'KmsKey' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, ], 'LandCoverSegmentationConfigInput' => [ 'type' => 'structure', 'members' => [], ], 'LandsatCloudCoverLandInput' => [ 'type' => 'structure', 'required' => [ 'LowerBound', 'UpperBound', ], 'members' => [ 'LowerBound' => [ 'shape' => 'Float', ], 'UpperBound' => [ 'shape' => 'Float', ], ], ], 'LinearRing' => [ 'type' => 'list', 'member' => [ 'shape' => 'Position', ], 'min' => 4, ], 'LinearRings' => [ 'type' => 'list', 'member' => [ 'shape' => 'LinearRing', ], 'min' => 1, ], 'LinearRingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LinearRings', ], ], 'ListEarthObservationJobInput' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'ListEarthObservationJobInputMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'SortBy' => [ 'shape' => 'String', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], 'StatusEquals' => [ 'shape' => 'EarthObservationJobStatus', ], ], ], 'ListEarthObservationJobInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 20, 'min' => 1, ], 'ListEarthObservationJobOutput' => [ 'type' => 'structure', 'required' => [ 'EarthObservationJobSummaries', ], 'members' => [ 'EarthObservationJobSummaries' => [ 'shape' => 'EarthObservationJobList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListEarthObservationJobOutputConfig' => [ 'type' => 'structure', 'required' => [ 'Arn', 'CreationTime', 'DurationInSeconds', 'Name', 'OperationType', 'Status', ], 'members' => [ 'Arn' => [ 'shape' => 'String', ], 'CreationTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'DurationInSeconds' => [ 'shape' => 'Integer', ], 'Name' => [ 'shape' => 'String', ], 'OperationType' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'EarthObservationJobStatus', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'ListRasterDataCollectionsInput' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'ListRasterDataCollectionsInputMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'ListRasterDataCollectionsInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 20, 'min' => 1, ], 'ListRasterDataCollectionsOutput' => [ 'type' => 'structure', 'required' => [ 'RasterDataCollectionSummaries', ], 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'RasterDataCollectionSummaries' => [ 'shape' => 'DataCollectionsList', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'Tags', ], ], ], 'ListVectorEnrichmentJobInput' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'ListVectorEnrichmentJobInputMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'SortBy' => [ 'shape' => 'String', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], 'StatusEquals' => [ 'shape' => 'String', ], ], ], 'ListVectorEnrichmentJobInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 20, 'min' => 1, ], 'ListVectorEnrichmentJobOutput' => [ 'type' => 'structure', 'required' => [ 'VectorEnrichmentJobSummaries', ], 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'VectorEnrichmentJobSummaries' => [ 'shape' => 'VectorEnrichmentJobList', ], ], ], 'ListVectorEnrichmentJobOutputConfig' => [ 'type' => 'structure', 'required' => [ 'Arn', 'CreationTime', 'DurationInSeconds', 'Name', 'Status', 'Type', ], 'members' => [ 'Arn' => [ 'shape' => 'VectorEnrichmentJobArn', ], 'CreationTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'DurationInSeconds' => [ 'shape' => 'Integer', ], 'Name' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'VectorEnrichmentJobStatus', ], 'Tags' => [ 'shape' => 'Tags', ], 'Type' => [ 'shape' => 'VectorEnrichmentJobType', ], ], ], 'LogicalOperator' => [ 'type' => 'string', 'enum' => [ 'AND', ], ], 'MapMatchingConfig' => [ 'type' => 'structure', 'required' => [ 'IdAttributeName', 'TimestampAttributeName', 'XAttributeName', 'YAttributeName', ], 'members' => [ 'IdAttributeName' => [ 'shape' => 'String', ], 'TimestampAttributeName' => [ 'shape' => 'String', ], 'XAttributeName' => [ 'shape' => 'String', ], 'YAttributeName' => [ 'shape' => 'String', ], ], ], 'MultiPolygonGeometryInput' => [ 'type' => 'structure', 'required' => [ 'Coordinates', ], 'members' => [ 'Coordinates' => [ 'shape' => 'LinearRingsList', ], ], ], 'NextToken' => [ 'type' => 'string', 'max' => 8192, 'min' => 0, 'sensitive' => true, ], 'Operation' => [ 'type' => 'structure', 'required' => [ 'Equation', 'Name', ], 'members' => [ 'Equation' => [ 'shape' => 'String', ], 'Name' => [ 'shape' => 'String', ], 'OutputType' => [ 'shape' => 'OutputType', ], ], ], 'OperationsListInput' => [ 'type' => 'list', 'member' => [ 'shape' => 'Operation', ], 'min' => 1, ], 'OutputBand' => [ 'type' => 'structure', 'required' => [ 'BandName', 'OutputDataType', ], 'members' => [ 'BandName' => [ 'shape' => 'String', ], 'OutputDataType' => [ 'shape' => 'OutputType', ], ], ], 'OutputConfigInput' => [ 'type' => 'structure', 'required' => [ 'S3Data', ], 'members' => [ 'S3Data' => [ 'shape' => 'ExportS3DataInput', ], ], ], 'OutputResolutionResamplingInput' => [ 'type' => 'structure', 'required' => [ 'UserDefined', ], 'members' => [ 'UserDefined' => [ 'shape' => 'UserDefined', ], ], ], 'OutputResolutionStackInput' => [ 'type' => 'structure', 'members' => [ 'Predefined' => [ 'shape' => 'PredefinedResolution', ], 'UserDefined' => [ 'shape' => 'UserDefined', ], ], ], 'OutputType' => [ 'type' => 'string', 'enum' => [ 'INT32', 'FLOAT32', 'INT16', 'FLOAT64', 'UINT16', ], ], 'PlatformInput' => [ 'type' => 'structure', 'required' => [ 'Value', ], 'members' => [ 'ComparisonOperator' => [ 'shape' => 'ComparisonOperator', ], 'Value' => [ 'shape' => 'String', ], ], ], 'PolygonGeometryInput' => [ 'type' => 'structure', 'required' => [ 'Coordinates', ], 'members' => [ 'Coordinates' => [ 'shape' => 'LinearRings', ], ], ], 'Position' => [ 'type' => 'list', 'member' => [ 'shape' => 'Double', ], 'max' => 2, 'min' => 2, 'sensitive' => true, ], 'PredefinedResolution' => [ 'type' => 'string', 'enum' => [ 'HIGHEST', 'LOWEST', 'AVERAGE', ], ], 'Properties' => [ 'type' => 'structure', 'members' => [ 'EoCloudCover' => [ 'shape' => 'Float', ], 'LandsatCloudCoverLand' => [ 'shape' => 'Float', ], 'Platform' => [ 'shape' => 'String', ], 'ViewOffNadir' => [ 'shape' => 'Float', ], 'ViewSunAzimuth' => [ 'shape' => 'Float', ], 'ViewSunElevation' => [ 'shape' => 'Float', ], ], ], 'Property' => [ 'type' => 'structure', 'members' => [ 'EoCloudCover' => [ 'shape' => 'EoCloudCoverInput', ], 'LandsatCloudCoverLand' => [ 'shape' => 'LandsatCloudCoverLandInput', ], 'Platform' => [ 'shape' => 'PlatformInput', ], 'ViewOffNadir' => [ 'shape' => 'ViewOffNadirInput', ], 'ViewSunAzimuth' => [ 'shape' => 'ViewSunAzimuthInput', ], 'ViewSunElevation' => [ 'shape' => 'ViewSunElevationInput', ], ], 'union' => true, ], 'PropertyFilter' => [ 'type' => 'structure', 'required' => [ 'Property', ], 'members' => [ 'Property' => [ 'shape' => 'Property', ], ], ], 'PropertyFilters' => [ 'type' => 'structure', 'members' => [ 'LogicalOperator' => [ 'shape' => 'LogicalOperator', ], 'Properties' => [ 'shape' => 'PropertyFiltersList', ], ], ], 'PropertyFiltersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PropertyFilter', ], ], 'RasterDataCollectionMetadata' => [ 'type' => 'structure', 'required' => [ 'Arn', 'Description', 'Name', 'SupportedFilters', 'Type', ], 'members' => [ 'Arn' => [ 'shape' => 'DataCollectionArn', ], 'Description' => [ 'shape' => 'String', ], 'DescriptionPageUrl' => [ 'shape' => 'String', ], 'Name' => [ 'shape' => 'String', ], 'SupportedFilters' => [ 'shape' => 'FilterList', ], 'Tags' => [ 'shape' => 'Tags', ], 'Type' => [ 'shape' => 'DataCollectionType', ], ], ], 'RasterDataCollectionQueryInput' => [ 'type' => 'structure', 'required' => [ 'RasterDataCollectionArn', 'TimeRangeFilter', ], 'members' => [ 'AreaOfInterest' => [ 'shape' => 'AreaOfInterest', ], 'PropertyFilters' => [ 'shape' => 'PropertyFilters', ], 'RasterDataCollectionArn' => [ 'shape' => 'DataCollectionArn', ], 'TimeRangeFilter' => [ 'shape' => 'TimeRangeFilterInput', ], ], ], 'RasterDataCollectionQueryOutput' => [ 'type' => 'structure', 'required' => [ 'RasterDataCollectionArn', 'RasterDataCollectionName', 'TimeRangeFilter', ], 'members' => [ 'AreaOfInterest' => [ 'shape' => 'AreaOfInterest', ], 'PropertyFilters' => [ 'shape' => 'PropertyFilters', ], 'RasterDataCollectionArn' => [ 'shape' => 'DataCollectionArn', ], 'RasterDataCollectionName' => [ 'shape' => 'String', ], 'TimeRangeFilter' => [ 'shape' => 'TimeRangeFilterOutput', ], ], ], 'RasterDataCollectionQueryWithBandFilterInput' => [ 'type' => 'structure', 'required' => [ 'TimeRangeFilter', ], 'members' => [ 'AreaOfInterest' => [ 'shape' => 'AreaOfInterest', ], 'BandFilter' => [ 'shape' => 'StringListInput', ], 'PropertyFilters' => [ 'shape' => 'PropertyFilters', ], 'TimeRangeFilter' => [ 'shape' => 'TimeRangeFilterInput', ], ], ], 'ResamplingConfigInput' => [ 'type' => 'structure', 'required' => [ 'OutputResolution', ], 'members' => [ 'AlgorithmName' => [ 'shape' => 'AlgorithmNameResampling', ], 'OutputResolution' => [ 'shape' => 'OutputResolutionResamplingInput', ], 'TargetBands' => [ 'shape' => 'StringListInput', ], ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ResourceId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ReverseGeocodingConfig' => [ 'type' => 'structure', 'required' => [ 'XAttributeName', 'YAttributeName', ], 'members' => [ 'XAttributeName' => [ 'shape' => 'String', ], 'YAttributeName' => [ 'shape' => 'String', ], ], ], 'S3Uri' => [ 'type' => 'string', 'pattern' => '^s3://([^/]+)/?(.*)$', ], 'SearchRasterDataCollectionInput' => [ 'type' => 'structure', 'required' => [ 'Arn', 'RasterDataCollectionQuery', ], 'members' => [ 'Arn' => [ 'shape' => 'DataCollectionArn', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'RasterDataCollectionQuery' => [ 'shape' => 'RasterDataCollectionQueryWithBandFilterInput', ], ], ], 'SearchRasterDataCollectionOutput' => [ 'type' => 'structure', 'required' => [ 'ApproximateResultCount', ], 'members' => [ 'ApproximateResultCount' => [ 'shape' => 'Integer', ], 'Items' => [ 'shape' => 'ItemSourceList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ResourceId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'ASCENDING', 'DESCENDING', ], ], 'StackConfigInput' => [ 'type' => 'structure', 'members' => [ 'OutputResolution' => [ 'shape' => 'OutputResolutionStackInput', ], 'TargetBands' => [ 'shape' => 'StringListInput', ], ], ], 'StartEarthObservationJobInput' => [ 'type' => 'structure', 'required' => [ 'ExecutionRoleArn', 'InputConfig', 'JobConfig', 'Name', ], 'members' => [ 'ClientToken' => [ 'shape' => 'StartEarthObservationJobInputClientTokenString', 'idempotencyToken' => true, ], 'ExecutionRoleArn' => [ 'shape' => 'ExecutionRoleArn', ], 'InputConfig' => [ 'shape' => 'InputConfigInput', ], 'JobConfig' => [ 'shape' => 'JobConfigInput', ], 'KmsKeyId' => [ 'shape' => 'KmsKey', ], 'Name' => [ 'shape' => 'StartEarthObservationJobInputNameString', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'StartEarthObservationJobInputClientTokenString' => [ 'type' => 'string', 'max' => 64, 'min' => 36, ], 'StartEarthObservationJobInputNameString' => [ 'type' => 'string', 'max' => 200, 'min' => 0, ], 'StartEarthObservationJobOutput' => [ 'type' => 'structure', 'required' => [ 'Arn', 'CreationTime', 'DurationInSeconds', 'ExecutionRoleArn', 'JobConfig', 'Name', 'Status', ], 'members' => [ 'Arn' => [ 'shape' => 'String', ], 'CreationTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'DurationInSeconds' => [ 'shape' => 'Integer', ], 'ExecutionRoleArn' => [ 'shape' => 'ExecutionRoleArn', ], 'InputConfig' => [ 'shape' => 'InputConfigOutput', ], 'JobConfig' => [ 'shape' => 'JobConfigInput', ], 'KmsKeyId' => [ 'shape' => 'KmsKey', ], 'Name' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'EarthObservationJobStatus', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'StartVectorEnrichmentJobInput' => [ 'type' => 'structure', 'required' => [ 'ExecutionRoleArn', 'InputConfig', 'JobConfig', 'Name', ], 'members' => [ 'ClientToken' => [ 'shape' => 'StartVectorEnrichmentJobInputClientTokenString', 'idempotencyToken' => true, ], 'ExecutionRoleArn' => [ 'shape' => 'ExecutionRoleArn', ], 'InputConfig' => [ 'shape' => 'VectorEnrichmentJobInputConfig', ], 'JobConfig' => [ 'shape' => 'VectorEnrichmentJobConfig', ], 'KmsKeyId' => [ 'shape' => 'KmsKey', ], 'Name' => [ 'shape' => 'StartVectorEnrichmentJobInputNameString', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'StartVectorEnrichmentJobInputClientTokenString' => [ 'type' => 'string', 'max' => 64, 'min' => 36, ], 'StartVectorEnrichmentJobInputNameString' => [ 'type' => 'string', 'max' => 200, 'min' => 0, ], 'StartVectorEnrichmentJobOutput' => [ 'type' => 'structure', 'required' => [ 'Arn', 'CreationTime', 'DurationInSeconds', 'ExecutionRoleArn', 'InputConfig', 'JobConfig', 'Name', 'Status', 'Type', ], 'members' => [ 'Arn' => [ 'shape' => 'VectorEnrichmentJobArn', ], 'CreationTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'DurationInSeconds' => [ 'shape' => 'Integer', ], 'ExecutionRoleArn' => [ 'shape' => 'ExecutionRoleArn', ], 'InputConfig' => [ 'shape' => 'VectorEnrichmentJobInputConfig', ], 'JobConfig' => [ 'shape' => 'VectorEnrichmentJobConfig', ], 'KmsKeyId' => [ 'shape' => 'KmsKey', ], 'Name' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'VectorEnrichmentJobStatus', ], 'Tags' => [ 'shape' => 'Tags', ], 'Type' => [ 'shape' => 'VectorEnrichmentJobType', ], ], ], 'StopEarthObservationJobInput' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'EarthObservationJobArn', ], ], ], 'StopEarthObservationJobOutput' => [ 'type' => 'structure', 'members' => [], ], 'StopVectorEnrichmentJobInput' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'VectorEnrichmentJobArn', ], ], ], 'StopVectorEnrichmentJobOutput' => [ 'type' => 'structure', 'members' => [], ], 'String' => [ 'type' => 'string', ], 'StringListInput' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'min' => 1, ], 'SyntheticTimestamp_date_time' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 50, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'Tags' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'TargetOptions' => [ 'type' => 'string', 'enum' => [ 'INPUT', 'OUTPUT', ], ], 'TemporalStatistics' => [ 'type' => 'string', 'enum' => [ 'MEAN', 'MEDIAN', 'STANDARD_DEVIATION', ], ], 'TemporalStatisticsConfigInput' => [ 'type' => 'structure', 'required' => [ 'Statistics', ], 'members' => [ 'GroupBy' => [ 'shape' => 'GroupBy', ], 'Statistics' => [ 'shape' => 'TemporalStatisticsListInput', ], 'TargetBands' => [ 'shape' => 'StringListInput', ], ], ], 'TemporalStatisticsListInput' => [ 'type' => 'list', 'member' => [ 'shape' => 'TemporalStatistics', ], 'min' => 1, ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ResourceId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'TimeRangeFilterInput' => [ 'type' => 'structure', 'required' => [ 'EndTime', 'StartTime', ], 'members' => [ 'EndTime' => [ 'shape' => 'Timestamp', ], 'StartTime' => [ 'shape' => 'Timestamp', ], ], 'sensitive' => true, ], 'TimeRangeFilterOutput' => [ 'type' => 'structure', 'required' => [ 'EndTime', 'StartTime', ], 'members' => [ 'EndTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'StartTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], 'sensitive' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'Unit' => [ 'type' => 'string', 'enum' => [ 'METERS', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UserDefined' => [ 'type' => 'structure', 'required' => [ 'Unit', 'Value', ], 'members' => [ 'Unit' => [ 'shape' => 'Unit', ], 'Value' => [ 'shape' => 'Float', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ResourceId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'VectorEnrichmentJobArn' => [ 'type' => 'string', 'pattern' => '^arn:aws[a-z-]{0,12}:sagemaker-geospatial:[a-z0-9-]{1,25}:[0-9]{12}:vector-enrichment-job/[a-z0-9]{12,}$', ], 'VectorEnrichmentJobConfig' => [ 'type' => 'structure', 'members' => [ 'MapMatchingConfig' => [ 'shape' => 'MapMatchingConfig', ], 'ReverseGeocodingConfig' => [ 'shape' => 'ReverseGeocodingConfig', ], ], 'union' => true, ], 'VectorEnrichmentJobDataSourceConfigInput' => [ 'type' => 'structure', 'members' => [ 'S3Data' => [ 'shape' => 'VectorEnrichmentJobS3Data', ], ], 'union' => true, ], 'VectorEnrichmentJobDocumentType' => [ 'type' => 'string', 'enum' => [ 'CSV', ], ], 'VectorEnrichmentJobErrorDetails' => [ 'type' => 'structure', 'members' => [ 'ErrorMessage' => [ 'shape' => 'String', ], 'ErrorType' => [ 'shape' => 'VectorEnrichmentJobErrorType', ], ], ], 'VectorEnrichmentJobErrorType' => [ 'type' => 'string', 'enum' => [ 'CLIENT_ERROR', 'SERVER_ERROR', ], ], 'VectorEnrichmentJobExportErrorDetails' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'Type' => [ 'shape' => 'VectorEnrichmentJobExportErrorType', ], ], ], 'VectorEnrichmentJobExportErrorType' => [ 'type' => 'string', 'enum' => [ 'CLIENT_ERROR', 'SERVER_ERROR', ], ], 'VectorEnrichmentJobExportStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'SUCCEEDED', 'FAILED', ], ], 'VectorEnrichmentJobInputConfig' => [ 'type' => 'structure', 'required' => [ 'DataSourceConfig', 'DocumentType', ], 'members' => [ 'DataSourceConfig' => [ 'shape' => 'VectorEnrichmentJobDataSourceConfigInput', ], 'DocumentType' => [ 'shape' => 'VectorEnrichmentJobDocumentType', ], ], ], 'VectorEnrichmentJobList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListVectorEnrichmentJobOutputConfig', ], ], 'VectorEnrichmentJobS3Data' => [ 'type' => 'structure', 'required' => [ 'S3Uri', ], 'members' => [ 'KmsKeyId' => [ 'shape' => 'KmsKey', ], 'S3Uri' => [ 'shape' => 'S3Uri', ], ], ], 'VectorEnrichmentJobStatus' => [ 'type' => 'string', 'enum' => [ 'INITIALIZING', 'IN_PROGRESS', 'STOPPING', 'STOPPED', 'COMPLETED', 'FAILED', 'DELETING', 'DELETED', ], ], 'VectorEnrichmentJobType' => [ 'type' => 'string', 'enum' => [ 'REVERSE_GEOCODING', 'MAP_MATCHING', ], ], 'ViewOffNadirInput' => [ 'type' => 'structure', 'required' => [ 'LowerBound', 'UpperBound', ], 'members' => [ 'LowerBound' => [ 'shape' => 'Float', ], 'UpperBound' => [ 'shape' => 'Float', ], ], ], 'ViewSunAzimuthInput' => [ 'type' => 'structure', 'required' => [ 'LowerBound', 'UpperBound', ], 'members' => [ 'LowerBound' => [ 'shape' => 'Float', ], 'UpperBound' => [ 'shape' => 'Float', ], ], ], 'ViewSunElevationInput' => [ 'type' => 'structure', 'required' => [ 'LowerBound', 'UpperBound', ], 'members' => [ 'LowerBound' => [ 'shape' => 'Float', ], 'UpperBound' => [ 'shape' => 'Float', ], ], ], 'ZonalStatistics' => [ 'type' => 'string', 'enum' => [ 'MEAN', 'MEDIAN', 'STANDARD_DEVIATION', 'MAX', 'MIN', 'SUM', ], ], 'ZonalStatisticsConfigInput' => [ 'type' => 'structure', 'required' => [ 'Statistics', 'ZoneS3Path', ], 'members' => [ 'Statistics' => [ 'shape' => 'ZonalStatisticsListInput', ], 'TargetBands' => [ 'shape' => 'StringListInput', ], 'ZoneS3Path' => [ 'shape' => 'S3Uri', ], 'ZoneS3PathKmsKeyId' => [ 'shape' => 'KmsKey', ], ], ], 'ZonalStatisticsListInput' => [ 'type' => 'list', 'member' => [ 'shape' => 'ZonalStatistics', ], 'min' => 1, ], ],];
