<?php
// This file was auto-generated from sdk-root/src/data/securitylake/2018-05-10/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-05-10', 'endpointPrefix' => 'securitylake', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'Amazon Security Lake', 'serviceId' => 'SecurityLake', 'signatureVersion' => 'v4', 'signingName' => 'securitylake', 'uid' => 'securitylake-2018-05-10', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'CreateAwsLogSource' => [ 'name' => 'CreateAwsLogSource', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/datalake/logsources/aws', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateAwsLogSourceRequest', ], 'output' => [ 'shape' => 'CreateAwsLogSourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateCustomLogSource' => [ 'name' => 'CreateCustomLogSource', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/datalake/logsources/custom', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateCustomLogSourceRequest', ], 'output' => [ 'shape' => 'CreateCustomLogSourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'CreateDataLake' => [ 'name' => 'CreateDataLake', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/datalake', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateDataLakeRequest', ], 'output' => [ 'shape' => 'CreateDataLakeResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateDataLakeExceptionSubscription' => [ 'name' => 'CreateDataLakeExceptionSubscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/datalake/exceptions/subscription', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateDataLakeExceptionSubscriptionRequest', ], 'output' => [ 'shape' => 'CreateDataLakeExceptionSubscriptionResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateDataLakeOrganizationConfiguration' => [ 'name' => 'CreateDataLakeOrganizationConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/datalake/organization/configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateDataLakeOrganizationConfigurationRequest', ], 'output' => [ 'shape' => 'CreateDataLakeOrganizationConfigurationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateSubscriber' => [ 'name' => 'CreateSubscriber', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/subscribers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateSubscriberRequest', ], 'output' => [ 'shape' => 'CreateSubscriberResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateSubscriberNotification' => [ 'name' => 'CreateSubscriberNotification', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/subscribers/{subscriberId}/notification', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateSubscriberNotificationRequest', ], 'output' => [ 'shape' => 'CreateSubscriberNotificationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteAwsLogSource' => [ 'name' => 'DeleteAwsLogSource', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/datalake/logsources/aws/delete', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteAwsLogSourceRequest', ], 'output' => [ 'shape' => 'DeleteAwsLogSourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteCustomLogSource' => [ 'name' => 'DeleteCustomLogSource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/datalake/logsources/custom/{sourceName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteCustomLogSourceRequest', ], 'output' => [ 'shape' => 'DeleteCustomLogSourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteDataLake' => [ 'name' => 'DeleteDataLake', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/datalake/delete', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteDataLakeRequest', ], 'output' => [ 'shape' => 'DeleteDataLakeResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteDataLakeExceptionSubscription' => [ 'name' => 'DeleteDataLakeExceptionSubscription', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/datalake/exceptions/subscription', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteDataLakeExceptionSubscriptionRequest', ], 'output' => [ 'shape' => 'DeleteDataLakeExceptionSubscriptionResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteDataLakeOrganizationConfiguration' => [ 'name' => 'DeleteDataLakeOrganizationConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/datalake/organization/configuration/delete', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteDataLakeOrganizationConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteDataLakeOrganizationConfigurationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteSubscriber' => [ 'name' => 'DeleteSubscriber', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/subscribers/{subscriberId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteSubscriberRequest', ], 'output' => [ 'shape' => 'DeleteSubscriberResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteSubscriberNotification' => [ 'name' => 'DeleteSubscriberNotification', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/subscribers/{subscriberId}/notification', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteSubscriberNotificationRequest', ], 'output' => [ 'shape' => 'DeleteSubscriberNotificationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeregisterDataLakeDelegatedAdministrator' => [ 'name' => 'DeregisterDataLakeDelegatedAdministrator', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/datalake/delegate', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeregisterDataLakeDelegatedAdministratorRequest', ], 'output' => [ 'shape' => 'DeregisterDataLakeDelegatedAdministratorResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'GetDataLakeExceptionSubscription' => [ 'name' => 'GetDataLakeExceptionSubscription', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/datalake/exceptions/subscription', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDataLakeExceptionSubscriptionRequest', ], 'output' => [ 'shape' => 'GetDataLakeExceptionSubscriptionResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetDataLakeOrganizationConfiguration' => [ 'name' => 'GetDataLakeOrganizationConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/datalake/organization/configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDataLakeOrganizationConfigurationRequest', ], 'output' => [ 'shape' => 'GetDataLakeOrganizationConfigurationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetDataLakeSources' => [ 'name' => 'GetDataLakeSources', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/datalake/sources', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDataLakeSourcesRequest', ], 'output' => [ 'shape' => 'GetDataLakeSourcesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetSubscriber' => [ 'name' => 'GetSubscriber', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/subscribers/{subscriberId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSubscriberRequest', ], 'output' => [ 'shape' => 'GetSubscriberResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListDataLakeExceptions' => [ 'name' => 'ListDataLakeExceptions', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/datalake/exceptions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDataLakeExceptionsRequest', ], 'output' => [ 'shape' => 'ListDataLakeExceptionsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListDataLakes' => [ 'name' => 'ListDataLakes', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/datalakes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDataLakesRequest', ], 'output' => [ 'shape' => 'ListDataLakesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListLogSources' => [ 'name' => 'ListLogSources', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/datalake/logsources/list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListLogSourcesRequest', ], 'output' => [ 'shape' => 'ListLogSourcesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListSubscribers' => [ 'name' => 'ListSubscribers', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/subscribers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSubscribersRequest', ], 'output' => [ 'shape' => 'ListSubscribersResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'RegisterDataLakeDelegatedAdministrator' => [ 'name' => 'RegisterDataLakeDelegatedAdministrator', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/datalake/delegate', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RegisterDataLakeDelegatedAdministratorRequest', ], 'output' => [ 'shape' => 'RegisterDataLakeDelegatedAdministratorResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'UpdateDataLake' => [ 'name' => 'UpdateDataLake', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/datalake', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateDataLakeRequest', ], 'output' => [ 'shape' => 'UpdateDataLakeResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'UpdateDataLakeExceptionSubscription' => [ 'name' => 'UpdateDataLakeExceptionSubscription', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/datalake/exceptions/subscription', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateDataLakeExceptionSubscriptionRequest', ], 'output' => [ 'shape' => 'UpdateDataLakeExceptionSubscriptionResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'UpdateSubscriber' => [ 'name' => 'UpdateSubscriber', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/subscribers/{subscriberId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateSubscriberRequest', ], 'output' => [ 'shape' => 'UpdateSubscriberResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'UpdateSubscriberNotification' => [ 'name' => 'UpdateSubscriberNotification', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/subscribers/{subscriberId}/notification', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateSubscriberNotificationRequest', ], 'output' => [ 'shape' => 'UpdateSubscriberNotificationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'errorCode' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AccessType' => [ 'type' => 'string', 'enum' => [ 'LAKEFORMATION', 'S3', ], ], 'AccessTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccessType', ], ], 'AccountList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsAccountId', ], ], 'AmazonResourceName' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, 'pattern' => '^arn:(aws|aws-us-gov|aws-cn):securitylake:[A-za-z0-9_/.\\-]{0,63}:[A-za-z0-9_/.\\-]{0,63}:[A-Za-z0-9][A-za-z0-9_/.\\-]{0,127}$', ], 'AwsAccountId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '^[0-9]{12}$', ], 'AwsIdentity' => [ 'type' => 'structure', 'required' => [ 'externalId', 'principal', ], 'members' => [ 'externalId' => [ 'shape' => 'ExternalId', ], 'principal' => [ 'shape' => 'AwsPrincipal', ], ], ], 'AwsLogSourceConfiguration' => [ 'type' => 'structure', 'required' => [ 'regions', 'sourceName', ], 'members' => [ 'accounts' => [ 'shape' => 'AccountList', ], 'regions' => [ 'shape' => 'RegionList', ], 'sourceName' => [ 'shape' => 'AwsLogSourceName', ], 'sourceVersion' => [ 'shape' => 'AwsLogSourceVersion', ], ], ], 'AwsLogSourceConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsLogSourceConfiguration', ], 'max' => 50, 'min' => 1, ], 'AwsLogSourceName' => [ 'type' => 'string', 'enum' => [ 'ROUTE53', 'VPC_FLOW', 'SH_FINDINGS', 'CLOUD_TRAIL_MGMT', 'LAMBDA_EXECUTION', 'S3_DATA', 'EKS_AUDIT', 'WAF', ], ], 'AwsLogSourceResource' => [ 'type' => 'structure', 'members' => [ 'sourceName' => [ 'shape' => 'AwsLogSourceName', ], 'sourceVersion' => [ 'shape' => 'AwsLogSourceVersion', ], ], ], 'AwsLogSourceResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsLogSourceResource', ], 'min' => 1, ], 'AwsLogSourceVersion' => [ 'type' => 'string', 'pattern' => '^(latest|[0-9]\\.[0-9])$', ], 'AwsPrincipal' => [ 'type' => 'string', 'pattern' => '^([0-9]{12}|[a-z0-9\\.\\-]*\\.(amazonaws|amazon)\\.com)$', ], 'BadRequestException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceName' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateAwsLogSourceRequest' => [ 'type' => 'structure', 'required' => [ 'sources', ], 'members' => [ 'sources' => [ 'shape' => 'AwsLogSourceConfigurationList', ], ], ], 'CreateAwsLogSourceResponse' => [ 'type' => 'structure', 'members' => [ 'failed' => [ 'shape' => 'AccountList', ], ], ], 'CreateCustomLogSourceRequest' => [ 'type' => 'structure', 'required' => [ 'configuration', 'sourceName', ], 'members' => [ 'configuration' => [ 'shape' => 'CustomLogSourceConfiguration', ], 'eventClasses' => [ 'shape' => 'OcsfEventClassList', ], 'sourceName' => [ 'shape' => 'CustomLogSourceName', ], 'sourceVersion' => [ 'shape' => 'CustomLogSourceVersion', ], ], ], 'CreateCustomLogSourceResponse' => [ 'type' => 'structure', 'members' => [ 'source' => [ 'shape' => 'CustomLogSourceResource', ], ], ], 'CreateDataLakeExceptionSubscriptionRequest' => [ 'type' => 'structure', 'required' => [ 'notificationEndpoint', 'subscriptionProtocol', ], 'members' => [ 'exceptionTimeToLive' => [ 'shape' => 'CreateDataLakeExceptionSubscriptionRequestExceptionTimeToLiveLong', ], 'notificationEndpoint' => [ 'shape' => 'SafeString', ], 'subscriptionProtocol' => [ 'shape' => 'SubscriptionProtocol', ], ], ], 'CreateDataLakeExceptionSubscriptionRequestExceptionTimeToLiveLong' => [ 'type' => 'long', 'box' => true, 'min' => 1, ], 'CreateDataLakeExceptionSubscriptionResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateDataLakeOrganizationConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'autoEnableNewAccount' => [ 'shape' => 'DataLakeAutoEnableNewAccountConfigurationList', ], ], ], 'CreateDataLakeOrganizationConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateDataLakeRequest' => [ 'type' => 'structure', 'required' => [ 'configurations', 'metaStoreManagerRoleArn', ], 'members' => [ 'configurations' => [ 'shape' => 'DataLakeConfigurationList', ], 'metaStoreManagerRoleArn' => [ 'shape' => 'RoleArn', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateDataLakeResponse' => [ 'type' => 'structure', 'members' => [ 'dataLakes' => [ 'shape' => 'DataLakeResourceList', ], ], ], 'CreateSubscriberNotificationRequest' => [ 'type' => 'structure', 'required' => [ 'configuration', 'subscriberId', ], 'members' => [ 'configuration' => [ 'shape' => 'NotificationConfiguration', ], 'subscriberId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'subscriberId', ], ], ], 'CreateSubscriberNotificationResponse' => [ 'type' => 'structure', 'members' => [ 'subscriberEndpoint' => [ 'shape' => 'SafeString', ], ], ], 'CreateSubscriberRequest' => [ 'type' => 'structure', 'required' => [ 'sources', 'subscriberIdentity', 'subscriberName', ], 'members' => [ 'accessTypes' => [ 'shape' => 'AccessTypeList', ], 'sources' => [ 'shape' => 'LogSourceResourceList', ], 'subscriberDescription' => [ 'shape' => 'DescriptionString', ], 'subscriberIdentity' => [ 'shape' => 'AwsIdentity', ], 'subscriberName' => [ 'shape' => 'CreateSubscriberRequestSubscriberNameString', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateSubscriberRequestSubscriberNameString' => [ 'type' => 'string', 'max' => 64, 'min' => 0, ], 'CreateSubscriberResponse' => [ 'type' => 'structure', 'members' => [ 'subscriber' => [ 'shape' => 'SubscriberResource', ], ], ], 'CustomLogSourceAttributes' => [ 'type' => 'structure', 'members' => [ 'crawlerArn' => [ 'shape' => 'AmazonResourceName', ], 'databaseArn' => [ 'shape' => 'AmazonResourceName', ], 'tableArn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'CustomLogSourceConfiguration' => [ 'type' => 'structure', 'required' => [ 'crawlerConfiguration', 'providerIdentity', ], 'members' => [ 'crawlerConfiguration' => [ 'shape' => 'CustomLogSourceCrawlerConfiguration', ], 'providerIdentity' => [ 'shape' => 'AwsIdentity', ], ], ], 'CustomLogSourceCrawlerConfiguration' => [ 'type' => 'structure', 'required' => [ 'roleArn', ], 'members' => [ 'roleArn' => [ 'shape' => 'RoleArn', ], ], ], 'CustomLogSourceName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[\\w\\-\\_\\:\\.]*$', ], 'CustomLogSourceProvider' => [ 'type' => 'structure', 'members' => [ 'location' => [ 'shape' => 'S3URI', ], 'roleArn' => [ 'shape' => 'RoleArn', ], ], ], 'CustomLogSourceResource' => [ 'type' => 'structure', 'members' => [ 'attributes' => [ 'shape' => 'CustomLogSourceAttributes', ], 'provider' => [ 'shape' => 'CustomLogSourceProvider', ], 'sourceName' => [ 'shape' => 'CustomLogSourceName', ], 'sourceVersion' => [ 'shape' => 'CustomLogSourceVersion', ], ], ], 'CustomLogSourceVersion' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '^[A-Za-z0-9\\-\\.\\_]*$', ], 'DataLakeAutoEnableNewAccountConfiguration' => [ 'type' => 'structure', 'required' => [ 'region', 'sources', ], 'members' => [ 'region' => [ 'shape' => 'Region', ], 'sources' => [ 'shape' => 'AwsLogSourceResourceList', ], ], ], 'DataLakeAutoEnableNewAccountConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataLakeAutoEnableNewAccountConfiguration', ], 'min' => 1, ], 'DataLakeConfiguration' => [ 'type' => 'structure', 'required' => [ 'region', ], 'members' => [ 'encryptionConfiguration' => [ 'shape' => 'DataLakeEncryptionConfiguration', ], 'lifecycleConfiguration' => [ 'shape' => 'DataLakeLifecycleConfiguration', ], 'region' => [ 'shape' => 'Region', ], 'replicationConfiguration' => [ 'shape' => 'DataLakeReplicationConfiguration', ], ], ], 'DataLakeConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataLakeConfiguration', ], 'min' => 1, ], 'DataLakeEncryptionConfiguration' => [ 'type' => 'structure', 'members' => [ 'kmsKeyId' => [ 'shape' => 'String', ], ], ], 'DataLakeException' => [ 'type' => 'structure', 'members' => [ 'exception' => [ 'shape' => 'SafeString', ], 'region' => [ 'shape' => 'Region', ], 'remediation' => [ 'shape' => 'SafeString', ], 'timestamp' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'DataLakeExceptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataLakeException', ], ], 'DataLakeLifecycleConfiguration' => [ 'type' => 'structure', 'members' => [ 'expiration' => [ 'shape' => 'DataLakeLifecycleExpiration', ], 'transitions' => [ 'shape' => 'DataLakeLifecycleTransitionList', ], ], ], 'DataLakeLifecycleExpiration' => [ 'type' => 'structure', 'members' => [ 'days' => [ 'shape' => 'DataLakeLifecycleExpirationDaysInteger', ], ], ], 'DataLakeLifecycleExpirationDaysInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'DataLakeLifecycleTransition' => [ 'type' => 'structure', 'members' => [ 'days' => [ 'shape' => 'DataLakeLifecycleTransitionDaysInteger', ], 'storageClass' => [ 'shape' => 'DataLakeStorageClass', ], ], ], 'DataLakeLifecycleTransitionDaysInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'DataLakeLifecycleTransitionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataLakeLifecycleTransition', ], ], 'DataLakeReplicationConfiguration' => [ 'type' => 'structure', 'members' => [ 'regions' => [ 'shape' => 'RegionList', ], 'roleArn' => [ 'shape' => 'RoleArn', ], ], ], 'DataLakeResource' => [ 'type' => 'structure', 'required' => [ 'dataLakeArn', 'region', ], 'members' => [ 'createStatus' => [ 'shape' => 'DataLakeStatus', ], 'dataLakeArn' => [ 'shape' => 'AmazonResourceName', ], 'encryptionConfiguration' => [ 'shape' => 'DataLakeEncryptionConfiguration', ], 'lifecycleConfiguration' => [ 'shape' => 'DataLakeLifecycleConfiguration', ], 'region' => [ 'shape' => 'Region', ], 'replicationConfiguration' => [ 'shape' => 'DataLakeReplicationConfiguration', ], 's3BucketArn' => [ 'shape' => 'S3BucketArn', ], 'updateStatus' => [ 'shape' => 'DataLakeUpdateStatus', ], ], ], 'DataLakeResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataLakeResource', ], ], 'DataLakeSource' => [ 'type' => 'structure', 'members' => [ 'account' => [ 'shape' => 'String', ], 'eventClasses' => [ 'shape' => 'OcsfEventClassList', ], 'sourceName' => [ 'shape' => 'String', ], 'sourceStatuses' => [ 'shape' => 'DataLakeSourceStatusList', ], ], ], 'DataLakeSourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataLakeSource', ], ], 'DataLakeSourceStatus' => [ 'type' => 'structure', 'members' => [ 'resource' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'SourceCollectionStatus', ], ], ], 'DataLakeSourceStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataLakeSourceStatus', ], ], 'DataLakeStatus' => [ 'type' => 'string', 'enum' => [ 'INITIALIZED', 'PENDING', 'COMPLETED', 'FAILED', ], ], 'DataLakeStorageClass' => [ 'type' => 'string', ], 'DataLakeUpdateException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'String', ], 'reason' => [ 'shape' => 'String', ], ], ], 'DataLakeUpdateStatus' => [ 'type' => 'structure', 'members' => [ 'exception' => [ 'shape' => 'DataLakeUpdateException', ], 'requestId' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'DataLakeStatus', ], ], ], 'DeleteAwsLogSourceRequest' => [ 'type' => 'structure', 'required' => [ 'sources', ], 'members' => [ 'sources' => [ 'shape' => 'AwsLogSourceConfigurationList', ], ], ], 'DeleteAwsLogSourceResponse' => [ 'type' => 'structure', 'members' => [ 'failed' => [ 'shape' => 'AccountList', ], ], ], 'DeleteCustomLogSourceRequest' => [ 'type' => 'structure', 'required' => [ 'sourceName', ], 'members' => [ 'sourceName' => [ 'shape' => 'CustomLogSourceName', 'location' => 'uri', 'locationName' => 'sourceName', ], 'sourceVersion' => [ 'shape' => 'CustomLogSourceVersion', 'location' => 'querystring', 'locationName' => 'sourceVersion', ], ], ], 'DeleteCustomLogSourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDataLakeExceptionSubscriptionRequest' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDataLakeExceptionSubscriptionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDataLakeOrganizationConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'autoEnableNewAccount' => [ 'shape' => 'DataLakeAutoEnableNewAccountConfigurationList', ], ], ], 'DeleteDataLakeOrganizationConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDataLakeRequest' => [ 'type' => 'structure', 'required' => [ 'regions', ], 'members' => [ 'regions' => [ 'shape' => 'RegionList', ], ], ], 'DeleteDataLakeResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSubscriberNotificationRequest' => [ 'type' => 'structure', 'required' => [ 'subscriberId', ], 'members' => [ 'subscriberId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'subscriberId', ], ], ], 'DeleteSubscriberNotificationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSubscriberRequest' => [ 'type' => 'structure', 'required' => [ 'subscriberId', ], 'members' => [ 'subscriberId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'subscriberId', ], ], ], 'DeleteSubscriberResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeregisterDataLakeDelegatedAdministratorRequest' => [ 'type' => 'structure', 'members' => [], ], 'DeregisterDataLakeDelegatedAdministratorResponse' => [ 'type' => 'structure', 'members' => [], ], 'DescriptionString' => [ 'type' => 'string', 'pattern' => '^[\\\\\\w\\s\\-_:/,.@=+]*$', ], 'ExternalId' => [ 'type' => 'string', 'max' => 1224, 'min' => 2, 'pattern' => '^[\\w+=,.@:\\/-]*$', ], 'GetDataLakeExceptionSubscriptionRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetDataLakeExceptionSubscriptionResponse' => [ 'type' => 'structure', 'members' => [ 'exceptionTimeToLive' => [ 'shape' => 'Long', ], 'notificationEndpoint' => [ 'shape' => 'SafeString', ], 'subscriptionProtocol' => [ 'shape' => 'SubscriptionProtocol', ], ], ], 'GetDataLakeOrganizationConfigurationRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetDataLakeOrganizationConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'autoEnableNewAccount' => [ 'shape' => 'DataLakeAutoEnableNewAccountConfigurationList', ], ], ], 'GetDataLakeSourcesRequest' => [ 'type' => 'structure', 'members' => [ 'accounts' => [ 'shape' => 'AccountList', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetDataLakeSourcesResponse' => [ 'type' => 'structure', 'members' => [ 'dataLakeArn' => [ 'shape' => 'AmazonResourceName', ], 'dataLakeSources' => [ 'shape' => 'DataLakeSourceList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetSubscriberRequest' => [ 'type' => 'structure', 'required' => [ 'subscriberId', ], 'members' => [ 'subscriberId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'subscriberId', ], ], ], 'GetSubscriberResponse' => [ 'type' => 'structure', 'members' => [ 'subscriber' => [ 'shape' => 'SubscriberResource', ], ], ], 'HttpMethod' => [ 'type' => 'string', 'enum' => [ 'POST', 'PUT', ], ], 'HttpsNotificationConfiguration' => [ 'type' => 'structure', 'required' => [ 'endpoint', 'targetRoleArn', ], 'members' => [ 'authorizationApiKeyName' => [ 'shape' => 'String', ], 'authorizationApiKeyValue' => [ 'shape' => 'String', ], 'endpoint' => [ 'shape' => 'HttpsNotificationConfigurationEndpointString', ], 'httpMethod' => [ 'shape' => 'HttpMethod', ], 'targetRoleArn' => [ 'shape' => 'RoleArn', ], ], ], 'HttpsNotificationConfigurationEndpointString' => [ 'type' => 'string', 'pattern' => '^https?://.+$', ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'ListDataLakeExceptionsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'regions' => [ 'shape' => 'RegionList', ], ], ], 'ListDataLakeExceptionsResponse' => [ 'type' => 'structure', 'members' => [ 'exceptions' => [ 'shape' => 'DataLakeExceptionList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDataLakesRequest' => [ 'type' => 'structure', 'members' => [ 'regions' => [ 'shape' => 'RegionList', 'location' => 'querystring', 'locationName' => 'regions', ], ], ], 'ListDataLakesResponse' => [ 'type' => 'structure', 'members' => [ 'dataLakes' => [ 'shape' => 'DataLakeResourceList', ], ], ], 'ListLogSourcesRequest' => [ 'type' => 'structure', 'members' => [ 'accounts' => [ 'shape' => 'AccountList', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'regions' => [ 'shape' => 'RegionList', ], 'sources' => [ 'shape' => 'LogSourceResourceList', ], ], ], 'ListLogSourcesResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'sources' => [ 'shape' => 'LogSourceList', ], ], ], 'ListSubscribersRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListSubscribersResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'subscribers' => [ 'shape' => 'SubscriberResourceList', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagList', ], ], ], 'LogSource' => [ 'type' => 'structure', 'members' => [ 'account' => [ 'shape' => 'AwsAccountId', ], 'region' => [ 'shape' => 'Region', ], 'sources' => [ 'shape' => 'LogSourceResourceList', ], ], ], 'LogSourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LogSource', ], ], 'LogSourceResource' => [ 'type' => 'structure', 'members' => [ 'awsLogSource' => [ 'shape' => 'AwsLogSourceResource', ], 'customLogSource' => [ 'shape' => 'CustomLogSourceResource', ], ], 'union' => true, ], 'LogSourceResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LogSourceResource', ], ], 'Long' => [ 'type' => 'long', 'box' => true, ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'NextToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, ], 'NotificationConfiguration' => [ 'type' => 'structure', 'members' => [ 'httpsNotificationConfiguration' => [ 'shape' => 'HttpsNotificationConfiguration', ], 'sqsNotificationConfiguration' => [ 'shape' => 'SqsNotificationConfiguration', ], ], 'union' => true, ], 'OcsfEventClass' => [ 'type' => 'string', 'pattern' => '^[A-Z\\_0-9]*$', ], 'OcsfEventClassList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OcsfEventClass', ], ], 'Region' => [ 'type' => 'string', 'pattern' => '^(us(-gov)?|af|ap|ca|eu|me|sa)-(central|north|(north(?:east|west))|south|south(?:east|west)|east|west)-\\d+$', ], 'RegionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Region', ], ], 'RegisterDataLakeDelegatedAdministratorRequest' => [ 'type' => 'structure', 'required' => [ 'accountId', ], 'members' => [ 'accountId' => [ 'shape' => 'SafeString', ], ], ], 'RegisterDataLakeDelegatedAdministratorResponse' => [ 'type' => 'structure', 'members' => [], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceName' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResourceShareArn' => [ 'type' => 'string', ], 'ResourceShareName' => [ 'type' => 'string', 'pattern' => '^LakeFormation(?:-V[0-9]+)-([a-zA-Z0-9]+)-([\\\\\\w\\-_:/.@=+]*)$', ], 'RoleArn' => [ 'type' => 'string', 'pattern' => '^arn:(aws[a-zA-Z-]*)?:iam::\\d{12}:role/?[a-zA-Z_0-9+=,.@\\-_/]+$', ], 'S3BucketArn' => [ 'type' => 'string', ], 'S3URI' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '^s3[an]?://[a-z0-9][\\.\\-a-z0-9]{1,61}[a-z0-9](/[^/].*)+$', ], 'SafeString' => [ 'type' => 'string', 'pattern' => '^[\\\\\\w\\-_:/.@=+]*$', ], 'SourceCollectionStatus' => [ 'type' => 'string', 'enum' => [ 'COLLECTING', 'MISCONFIGURED', 'NOT_COLLECTING', ], ], 'SqsNotificationConfiguration' => [ 'type' => 'structure', 'members' => [], ], 'String' => [ 'type' => 'string', ], 'SubscriberResource' => [ 'type' => 'structure', 'required' => [ 'sources', 'subscriberArn', 'subscriberId', 'subscriberIdentity', 'subscriberName', ], 'members' => [ 'accessTypes' => [ 'shape' => 'AccessTypeList', ], 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'resourceShareArn' => [ 'shape' => 'ResourceShareArn', ], 'resourceShareName' => [ 'shape' => 'ResourceShareName', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 's3BucketArn' => [ 'shape' => 'S3BucketArn', ], 'sources' => [ 'shape' => 'LogSourceResourceList', ], 'subscriberArn' => [ 'shape' => 'AmazonResourceName', ], 'subscriberDescription' => [ 'shape' => 'SafeString', ], 'subscriberEndpoint' => [ 'shape' => 'SafeString', ], 'subscriberId' => [ 'shape' => 'UUID', ], 'subscriberIdentity' => [ 'shape' => 'AwsIdentity', ], 'subscriberName' => [ 'shape' => 'SafeString', ], 'subscriberStatus' => [ 'shape' => 'SubscriberStatus', ], 'updatedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'SubscriberResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubscriberResource', ], ], 'SubscriberStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'DEACTIVATED', 'PENDING', 'READY', ], ], 'SubscriptionProtocol' => [ 'type' => 'string', 'pattern' => '^[a-z\\-]*$', ], 'SyntheticTimestamp_date_time' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'key', 'value', ], 'members' => [ 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 0, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 50, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], 'serviceCode' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'UUID' => [ 'type' => 'string', 'pattern' => '^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDataLakeExceptionSubscriptionRequest' => [ 'type' => 'structure', 'required' => [ 'notificationEndpoint', 'subscriptionProtocol', ], 'members' => [ 'exceptionTimeToLive' => [ 'shape' => 'UpdateDataLakeExceptionSubscriptionRequestExceptionTimeToLiveLong', ], 'notificationEndpoint' => [ 'shape' => 'SafeString', ], 'subscriptionProtocol' => [ 'shape' => 'SubscriptionProtocol', ], ], ], 'UpdateDataLakeExceptionSubscriptionRequestExceptionTimeToLiveLong' => [ 'type' => 'long', 'box' => true, 'min' => 1, ], 'UpdateDataLakeExceptionSubscriptionResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDataLakeRequest' => [ 'type' => 'structure', 'required' => [ 'configurations', ], 'members' => [ 'configurations' => [ 'shape' => 'DataLakeConfigurationList', ], 'metaStoreManagerRoleArn' => [ 'shape' => 'RoleArn', ], ], ], 'UpdateDataLakeResponse' => [ 'type' => 'structure', 'members' => [ 'dataLakes' => [ 'shape' => 'DataLakeResourceList', ], ], ], 'UpdateSubscriberNotificationRequest' => [ 'type' => 'structure', 'required' => [ 'configuration', 'subscriberId', ], 'members' => [ 'configuration' => [ 'shape' => 'NotificationConfiguration', ], 'subscriberId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'subscriberId', ], ], ], 'UpdateSubscriberNotificationResponse' => [ 'type' => 'structure', 'members' => [ 'subscriberEndpoint' => [ 'shape' => 'SafeString', ], ], ], 'UpdateSubscriberRequest' => [ 'type' => 'structure', 'required' => [ 'subscriberId', ], 'members' => [ 'sources' => [ 'shape' => 'LogSourceResourceList', ], 'subscriberDescription' => [ 'shape' => 'DescriptionString', ], 'subscriberId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'subscriberId', ], 'subscriberIdentity' => [ 'shape' => 'AwsIdentity', ], 'subscriberName' => [ 'shape' => 'UpdateSubscriberRequestSubscriberNameString', ], ], ], 'UpdateSubscriberRequestSubscriberNameString' => [ 'type' => 'string', 'max' => 64, 'min' => 0, 'pattern' => '^[\\\\\\w\\-_:/.@=+]*$', ], 'UpdateSubscriberResponse' => [ 'type' => 'structure', 'members' => [ 'subscriber' => [ 'shape' => 'SubscriberResource', ], ], ], ],];
