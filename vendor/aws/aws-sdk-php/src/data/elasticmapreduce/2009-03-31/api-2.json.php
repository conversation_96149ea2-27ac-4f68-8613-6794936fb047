<?php
// This file was auto-generated from sdk-root/src/data/elasticmapreduce/2009-03-31/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2009-03-31', 'endpointPrefix' => 'elasticmapreduce', 'jsonVersion' => '1.1', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceAbbreviation' => 'Amazon EMR', 'serviceFullName' => 'Amazon EMR', 'serviceId' => 'EMR', 'signatureVersion' => 'v4', 'targetPrefix' => 'ElasticMapReduce', 'uid' => 'elasticmapreduce-2009-03-31', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'AddInstanceFleet' => [ 'name' => 'AddInstanceFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddInstanceFleetInput', ], 'output' => [ 'shape' => 'AddInstanceFleetOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'AddInstanceGroups' => [ 'name' => 'AddInstanceGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddInstanceGroupsInput', ], 'output' => [ 'shape' => 'AddInstanceGroupsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], ], ], 'AddJobFlowSteps' => [ 'name' => 'AddJobFlowSteps', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddJobFlowStepsInput', ], 'output' => [ 'shape' => 'AddJobFlowStepsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], ], ], 'AddTags' => [ 'name' => 'AddTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddTagsInput', ], 'output' => [ 'shape' => 'AddTagsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'CancelSteps' => [ 'name' => 'CancelSteps', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CancelStepsInput', ], 'output' => [ 'shape' => 'CancelStepsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'CreateSecurityConfiguration' => [ 'name' => 'CreateSecurityConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateSecurityConfigurationInput', ], 'output' => [ 'shape' => 'CreateSecurityConfigurationOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'CreateStudio' => [ 'name' => 'CreateStudio', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateStudioInput', ], 'output' => [ 'shape' => 'CreateStudioOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'CreateStudioSessionMapping' => [ 'name' => 'CreateStudioSessionMapping', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateStudioSessionMappingInput', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'DeleteSecurityConfiguration' => [ 'name' => 'DeleteSecurityConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteSecurityConfigurationInput', ], 'output' => [ 'shape' => 'DeleteSecurityConfigurationOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'DeleteStudio' => [ 'name' => 'DeleteStudio', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteStudioInput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'DeleteStudioSessionMapping' => [ 'name' => 'DeleteStudioSessionMapping', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteStudioSessionMappingInput', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'DescribeCluster' => [ 'name' => 'DescribeCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeClusterInput', ], 'output' => [ 'shape' => 'DescribeClusterOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'DescribeJobFlows' => [ 'name' => 'DescribeJobFlows', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeJobFlowsInput', ], 'output' => [ 'shape' => 'DescribeJobFlowsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], ], 'deprecated' => true, ], 'DescribeNotebookExecution' => [ 'name' => 'DescribeNotebookExecution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeNotebookExecutionInput', ], 'output' => [ 'shape' => 'DescribeNotebookExecutionOutput', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'DescribeReleaseLabel' => [ 'name' => 'DescribeReleaseLabel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeReleaseLabelInput', ], 'output' => [ 'shape' => 'DescribeReleaseLabelOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'DescribeSecurityConfiguration' => [ 'name' => 'DescribeSecurityConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeSecurityConfigurationInput', ], 'output' => [ 'shape' => 'DescribeSecurityConfigurationOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'DescribeStep' => [ 'name' => 'DescribeStep', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeStepInput', ], 'output' => [ 'shape' => 'DescribeStepOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'DescribeStudio' => [ 'name' => 'DescribeStudio', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeStudioInput', ], 'output' => [ 'shape' => 'DescribeStudioOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'GetAutoTerminationPolicy' => [ 'name' => 'GetAutoTerminationPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetAutoTerminationPolicyInput', ], 'output' => [ 'shape' => 'GetAutoTerminationPolicyOutput', ], ], 'GetBlockPublicAccessConfiguration' => [ 'name' => 'GetBlockPublicAccessConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetBlockPublicAccessConfigurationInput', ], 'output' => [ 'shape' => 'GetBlockPublicAccessConfigurationOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'GetClusterSessionCredentials' => [ 'name' => 'GetClusterSessionCredentials', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetClusterSessionCredentialsInput', ], 'output' => [ 'shape' => 'GetClusterSessionCredentialsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'GetManagedScalingPolicy' => [ 'name' => 'GetManagedScalingPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetManagedScalingPolicyInput', ], 'output' => [ 'shape' => 'GetManagedScalingPolicyOutput', ], ], 'GetStudioSessionMapping' => [ 'name' => 'GetStudioSessionMapping', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetStudioSessionMappingInput', ], 'output' => [ 'shape' => 'GetStudioSessionMappingOutput', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'ListBootstrapActions' => [ 'name' => 'ListBootstrapActions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListBootstrapActionsInput', ], 'output' => [ 'shape' => 'ListBootstrapActionsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'ListClusters' => [ 'name' => 'ListClusters', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListClustersInput', ], 'output' => [ 'shape' => 'ListClustersOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'ListInstanceFleets' => [ 'name' => 'ListInstanceFleets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListInstanceFleetsInput', ], 'output' => [ 'shape' => 'ListInstanceFleetsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'ListInstanceGroups' => [ 'name' => 'ListInstanceGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListInstanceGroupsInput', ], 'output' => [ 'shape' => 'ListInstanceGroupsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'ListInstances' => [ 'name' => 'ListInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListInstancesInput', ], 'output' => [ 'shape' => 'ListInstancesOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'ListNotebookExecutions' => [ 'name' => 'ListNotebookExecutions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListNotebookExecutionsInput', ], 'output' => [ 'shape' => 'ListNotebookExecutionsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'ListReleaseLabels' => [ 'name' => 'ListReleaseLabels', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListReleaseLabelsInput', ], 'output' => [ 'shape' => 'ListReleaseLabelsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'ListSecurityConfigurations' => [ 'name' => 'ListSecurityConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSecurityConfigurationsInput', ], 'output' => [ 'shape' => 'ListSecurityConfigurationsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'ListSteps' => [ 'name' => 'ListSteps', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListStepsInput', ], 'output' => [ 'shape' => 'ListStepsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'ListStudioSessionMappings' => [ 'name' => 'ListStudioSessionMappings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListStudioSessionMappingsInput', ], 'output' => [ 'shape' => 'ListStudioSessionMappingsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'ListStudios' => [ 'name' => 'ListStudios', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListStudiosInput', ], 'output' => [ 'shape' => 'ListStudiosOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'ListSupportedInstanceTypes' => [ 'name' => 'ListSupportedInstanceTypes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSupportedInstanceTypesInput', ], 'output' => [ 'shape' => 'ListSupportedInstanceTypesOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'ModifyCluster' => [ 'name' => 'ModifyCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyClusterInput', ], 'output' => [ 'shape' => 'ModifyClusterOutput', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'ModifyInstanceFleet' => [ 'name' => 'ModifyInstanceFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyInstanceFleetInput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'ModifyInstanceGroups' => [ 'name' => 'ModifyInstanceGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyInstanceGroupsInput', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], ], ], 'PutAutoScalingPolicy' => [ 'name' => 'PutAutoScalingPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutAutoScalingPolicyInput', ], 'output' => [ 'shape' => 'PutAutoScalingPolicyOutput', ], ], 'PutAutoTerminationPolicy' => [ 'name' => 'PutAutoTerminationPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutAutoTerminationPolicyInput', ], 'output' => [ 'shape' => 'PutAutoTerminationPolicyOutput', ], ], 'PutBlockPublicAccessConfiguration' => [ 'name' => 'PutBlockPublicAccessConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutBlockPublicAccessConfigurationInput', ], 'output' => [ 'shape' => 'PutBlockPublicAccessConfigurationOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'PutManagedScalingPolicy' => [ 'name' => 'PutManagedScalingPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutManagedScalingPolicyInput', ], 'output' => [ 'shape' => 'PutManagedScalingPolicyOutput', ], ], 'RemoveAutoScalingPolicy' => [ 'name' => 'RemoveAutoScalingPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemoveAutoScalingPolicyInput', ], 'output' => [ 'shape' => 'RemoveAutoScalingPolicyOutput', ], ], 'RemoveAutoTerminationPolicy' => [ 'name' => 'RemoveAutoTerminationPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemoveAutoTerminationPolicyInput', ], 'output' => [ 'shape' => 'RemoveAutoTerminationPolicyOutput', ], ], 'RemoveManagedScalingPolicy' => [ 'name' => 'RemoveManagedScalingPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemoveManagedScalingPolicyInput', ], 'output' => [ 'shape' => 'RemoveManagedScalingPolicyOutput', ], ], 'RemoveTags' => [ 'name' => 'RemoveTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemoveTagsInput', ], 'output' => [ 'shape' => 'RemoveTagsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'RunJobFlow' => [ 'name' => 'RunJobFlow', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RunJobFlowInput', ], 'output' => [ 'shape' => 'RunJobFlowOutput', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], ], ], 'SetKeepJobFlowAliveWhenNoSteps' => [ 'name' => 'SetKeepJobFlowAliveWhenNoSteps', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetKeepJobFlowAliveWhenNoStepsInput', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], ], ], 'SetTerminationProtection' => [ 'name' => 'SetTerminationProtection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetTerminationProtectionInput', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], ], ], 'SetUnhealthyNodeReplacement' => [ 'name' => 'SetUnhealthyNodeReplacement', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetUnhealthyNodeReplacementInput', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], ], ], 'SetVisibleToAllUsers' => [ 'name' => 'SetVisibleToAllUsers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetVisibleToAllUsersInput', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], ], ], 'StartNotebookExecution' => [ 'name' => 'StartNotebookExecution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartNotebookExecutionInput', ], 'output' => [ 'shape' => 'StartNotebookExecutionOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'StopNotebookExecution' => [ 'name' => 'StopNotebookExecution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopNotebookExecutionInput', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'TerminateJobFlows' => [ 'name' => 'TerminateJobFlows', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TerminateJobFlowsInput', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], ], ], 'UpdateStudio' => [ 'name' => 'UpdateStudio', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateStudioInput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'UpdateStudioSessionMapping' => [ 'name' => 'UpdateStudioSessionMapping', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateStudioSessionMappingInput', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidRequestException', ], ], ], ], 'shapes' => [ 'ActionOnFailure' => [ 'type' => 'string', 'enum' => [ 'TERMINATE_JOB_FLOW', 'TERMINATE_CLUSTER', 'CANCEL_AND_WAIT', 'CONTINUE', ], ], 'AddInstanceFleetInput' => [ 'type' => 'structure', 'required' => [ 'ClusterId', 'InstanceFleet', ], 'members' => [ 'ClusterId' => [ 'shape' => 'XmlStringMaxLen256', ], 'InstanceFleet' => [ 'shape' => 'InstanceFleetConfig', ], ], ], 'AddInstanceFleetOutput' => [ 'type' => 'structure', 'members' => [ 'ClusterId' => [ 'shape' => 'XmlStringMaxLen256', ], 'InstanceFleetId' => [ 'shape' => 'InstanceFleetId', ], 'ClusterArn' => [ 'shape' => 'ArnType', ], ], ], 'AddInstanceGroupsInput' => [ 'type' => 'structure', 'required' => [ 'InstanceGroups', 'JobFlowId', ], 'members' => [ 'InstanceGroups' => [ 'shape' => 'InstanceGroupConfigList', ], 'JobFlowId' => [ 'shape' => 'XmlStringMaxLen256', ], ], ], 'AddInstanceGroupsOutput' => [ 'type' => 'structure', 'members' => [ 'JobFlowId' => [ 'shape' => 'XmlStringMaxLen256', ], 'InstanceGroupIds' => [ 'shape' => 'InstanceGroupIdsList', ], 'ClusterArn' => [ 'shape' => 'ArnType', ], ], ], 'AddJobFlowStepsInput' => [ 'type' => 'structure', 'required' => [ 'JobFlowId', 'Steps', ], 'members' => [ 'JobFlowId' => [ 'shape' => 'XmlStringMaxLen256', ], 'Steps' => [ 'shape' => 'StepConfigList', ], 'ExecutionRoleArn' => [ 'shape' => 'ArnType', ], ], ], 'AddJobFlowStepsOutput' => [ 'type' => 'structure', 'members' => [ 'StepIds' => [ 'shape' => 'StepIdsList', ], ], ], 'AddTagsInput' => [ 'type' => 'structure', 'required' => [ 'ResourceId', 'Tags', ], 'members' => [ 'ResourceId' => [ 'shape' => 'ResourceId', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'AddTagsOutput' => [ 'type' => 'structure', 'members' => [], ], 'AdjustmentType' => [ 'type' => 'string', 'enum' => [ 'CHANGE_IN_CAPACITY', 'PERCENT_CHANGE_IN_CAPACITY', 'EXACT_CAPACITY', ], ], 'Application' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Version' => [ 'shape' => 'String', ], 'Args' => [ 'shape' => 'StringList', ], 'AdditionalInfo' => [ 'shape' => 'StringMap', ], ], ], 'ApplicationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Application', ], ], 'ArnType' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, ], 'AuthMode' => [ 'type' => 'string', 'enum' => [ 'SSO', 'IAM', ], ], 'AutoScalingPolicy' => [ 'type' => 'structure', 'required' => [ 'Constraints', 'Rules', ], 'members' => [ 'Constraints' => [ 'shape' => 'ScalingConstraints', ], 'Rules' => [ 'shape' => 'ScalingRuleList', ], ], ], 'AutoScalingPolicyDescription' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'AutoScalingPolicyStatus', ], 'Constraints' => [ 'shape' => 'ScalingConstraints', ], 'Rules' => [ 'shape' => 'ScalingRuleList', ], ], ], 'AutoScalingPolicyState' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'ATTACHING', 'ATTACHED', 'DETACHING', 'DETACHED', 'FAILED', ], ], 'AutoScalingPolicyStateChangeReason' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'AutoScalingPolicyStateChangeReasonCode', ], 'Message' => [ 'shape' => 'String', ], ], ], 'AutoScalingPolicyStateChangeReasonCode' => [ 'type' => 'string', 'enum' => [ 'USER_REQUEST', 'PROVISION_FAILURE', 'CLEANUP_FAILURE', ], ], 'AutoScalingPolicyStatus' => [ 'type' => 'structure', 'members' => [ 'State' => [ 'shape' => 'AutoScalingPolicyState', ], 'StateChangeReason' => [ 'shape' => 'AutoScalingPolicyStateChangeReason', ], ], ], 'AutoTerminationPolicy' => [ 'type' => 'structure', 'members' => [ 'IdleTimeout' => [ 'shape' => 'Long', ], ], ], 'BlockPublicAccessConfiguration' => [ 'type' => 'structure', 'required' => [ 'BlockPublicSecurityGroupRules', ], 'members' => [ 'BlockPublicSecurityGroupRules' => [ 'shape' => 'Boolean', ], 'PermittedPublicSecurityGroupRuleRanges' => [ 'shape' => 'PortRanges', ], ], ], 'BlockPublicAccessConfigurationMetadata' => [ 'type' => 'structure', 'required' => [ 'CreationDateTime', 'CreatedByArn', ], 'members' => [ 'CreationDateTime' => [ 'shape' => 'Date', ], 'CreatedByArn' => [ 'shape' => 'ArnType', ], ], ], 'Boolean' => [ 'type' => 'boolean', ], 'BooleanObject' => [ 'type' => 'boolean', ], 'BootstrapActionConfig' => [ 'type' => 'structure', 'required' => [ 'Name', 'ScriptBootstrapAction', ], 'members' => [ 'Name' => [ 'shape' => 'XmlStringMaxLen256', ], 'ScriptBootstrapAction' => [ 'shape' => 'ScriptBootstrapActionConfig', ], ], ], 'BootstrapActionConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BootstrapActionConfig', ], ], 'BootstrapActionDetail' => [ 'type' => 'structure', 'members' => [ 'BootstrapActionConfig' => [ 'shape' => 'BootstrapActionConfig', ], ], ], 'BootstrapActionDetailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BootstrapActionDetail', ], ], 'CancelStepsInfo' => [ 'type' => 'structure', 'members' => [ 'StepId' => [ 'shape' => 'StepId', ], 'Status' => [ 'shape' => 'CancelStepsRequestStatus', ], 'Reason' => [ 'shape' => 'String', ], ], ], 'CancelStepsInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CancelStepsInfo', ], ], 'CancelStepsInput' => [ 'type' => 'structure', 'required' => [ 'ClusterId', 'StepIds', ], 'members' => [ 'ClusterId' => [ 'shape' => 'XmlStringMaxLen256', ], 'StepIds' => [ 'shape' => 'StepIdsList', ], 'StepCancellationOption' => [ 'shape' => 'StepCancellationOption', ], ], ], 'CancelStepsOutput' => [ 'type' => 'structure', 'members' => [ 'CancelStepsInfoList' => [ 'shape' => 'CancelStepsInfoList', ], ], ], 'CancelStepsRequestStatus' => [ 'type' => 'string', 'enum' => [ 'SUBMITTED', 'FAILED', ], ], 'CloudWatchAlarmDefinition' => [ 'type' => 'structure', 'required' => [ 'ComparisonOperator', 'MetricName', 'Period', 'Threshold', ], 'members' => [ 'ComparisonOperator' => [ 'shape' => 'ComparisonOperator', ], 'EvaluationPeriods' => [ 'shape' => 'Integer', ], 'MetricName' => [ 'shape' => 'String', ], 'Namespace' => [ 'shape' => 'String', ], 'Period' => [ 'shape' => 'Integer', ], 'Statistic' => [ 'shape' => 'Statistic', ], 'Threshold' => [ 'shape' => 'NonNegativeDouble', ], 'Unit' => [ 'shape' => 'Unit', ], 'Dimensions' => [ 'shape' => 'MetricDimensionList', ], ], ], 'Cluster' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ClusterId', ], 'Name' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'ClusterStatus', ], 'Ec2InstanceAttributes' => [ 'shape' => 'Ec2InstanceAttributes', ], 'InstanceCollectionType' => [ 'shape' => 'InstanceCollectionType', ], 'LogUri' => [ 'shape' => 'String', ], 'LogEncryptionKmsKeyId' => [ 'shape' => 'String', ], 'RequestedAmiVersion' => [ 'shape' => 'String', ], 'RunningAmiVersion' => [ 'shape' => 'String', ], 'ReleaseLabel' => [ 'shape' => 'String', ], 'AutoTerminate' => [ 'shape' => 'Boolean', ], 'TerminationProtected' => [ 'shape' => 'Boolean', ], 'UnhealthyNodeReplacement' => [ 'shape' => 'BooleanObject', ], 'VisibleToAllUsers' => [ 'shape' => 'Boolean', ], 'Applications' => [ 'shape' => 'ApplicationList', ], 'Tags' => [ 'shape' => 'TagList', ], 'ServiceRole' => [ 'shape' => 'String', ], 'NormalizedInstanceHours' => [ 'shape' => 'Integer', ], 'MasterPublicDnsName' => [ 'shape' => 'String', ], 'Configurations' => [ 'shape' => 'ConfigurationList', ], 'SecurityConfiguration' => [ 'shape' => 'XmlString', ], 'AutoScalingRole' => [ 'shape' => 'XmlString', ], 'ScaleDownBehavior' => [ 'shape' => 'ScaleDownBehavior', ], 'CustomAmiId' => [ 'shape' => 'XmlStringMaxLen256', ], 'EbsRootVolumeSize' => [ 'shape' => 'Integer', ], 'RepoUpgradeOnBoot' => [ 'shape' => 'RepoUpgradeOnBoot', ], 'KerberosAttributes' => [ 'shape' => 'KerberosAttributes', ], 'ClusterArn' => [ 'shape' => 'ArnType', ], 'OutpostArn' => [ 'shape' => 'OptionalArnType', ], 'StepConcurrencyLevel' => [ 'shape' => 'Integer', ], 'PlacementGroups' => [ 'shape' => 'PlacementGroupConfigList', ], 'OSReleaseLabel' => [ 'shape' => 'String', ], 'EbsRootVolumeIops' => [ 'shape' => 'Integer', ], 'EbsRootVolumeThroughput' => [ 'shape' => 'Integer', ], ], ], 'ClusterId' => [ 'type' => 'string', ], 'ClusterState' => [ 'type' => 'string', 'enum' => [ 'STARTING', 'BOOTSTRAPPING', 'RUNNING', 'WAITING', 'TERMINATING', 'TERMINATED', 'TERMINATED_WITH_ERRORS', ], ], 'ClusterStateChangeReason' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ClusterStateChangeReasonCode', ], 'Message' => [ 'shape' => 'String', ], ], ], 'ClusterStateChangeReasonCode' => [ 'type' => 'string', 'enum' => [ 'INTERNAL_ERROR', 'VALIDATION_ERROR', 'INSTANCE_FAILURE', 'INSTANCE_FLEET_TIMEOUT', 'BOOTSTRAP_FAILURE', 'USER_REQUEST', 'STEP_FAILURE', 'ALL_STEPS_COMPLETED', ], ], 'ClusterStateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ClusterState', ], ], 'ClusterStatus' => [ 'type' => 'structure', 'members' => [ 'State' => [ 'shape' => 'ClusterState', ], 'StateChangeReason' => [ 'shape' => 'ClusterStateChangeReason', ], 'Timeline' => [ 'shape' => 'ClusterTimeline', ], 'ErrorDetails' => [ 'shape' => 'ErrorDetailList', ], ], ], 'ClusterSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ClusterId', ], 'Name' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'ClusterStatus', ], 'NormalizedInstanceHours' => [ 'shape' => 'Integer', ], 'ClusterArn' => [ 'shape' => 'ArnType', ], 'OutpostArn' => [ 'shape' => 'OptionalArnType', ], ], ], 'ClusterSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ClusterSummary', ], ], 'ClusterTimeline' => [ 'type' => 'structure', 'members' => [ 'CreationDateTime' => [ 'shape' => 'Date', ], 'ReadyDateTime' => [ 'shape' => 'Date', ], 'EndDateTime' => [ 'shape' => 'Date', ], ], ], 'Command' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'ScriptPath' => [ 'shape' => 'String', ], 'Args' => [ 'shape' => 'StringList', ], ], ], 'CommandList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Command', ], ], 'ComparisonOperator' => [ 'type' => 'string', 'enum' => [ 'GREATER_THAN_OR_EQUAL', 'GREATER_THAN', 'LESS_THAN', 'LESS_THAN_OR_EQUAL', ], ], 'ComputeLimits' => [ 'type' => 'structure', 'required' => [ 'UnitType', 'MinimumCapacityUnits', 'MaximumCapacityUnits', ], 'members' => [ 'UnitType' => [ 'shape' => 'ComputeLimitsUnitType', ], 'MinimumCapacityUnits' => [ 'shape' => 'Integer', ], 'MaximumCapacityUnits' => [ 'shape' => 'Integer', ], 'MaximumOnDemandCapacityUnits' => [ 'shape' => 'Integer', ], 'MaximumCoreCapacityUnits' => [ 'shape' => 'Integer', ], ], ], 'ComputeLimitsUnitType' => [ 'type' => 'string', 'enum' => [ 'InstanceFleetUnits', 'Instances', 'VCPU', ], ], 'Configuration' => [ 'type' => 'structure', 'members' => [ 'Classification' => [ 'shape' => 'String', ], 'Configurations' => [ 'shape' => 'ConfigurationList', ], 'Properties' => [ 'shape' => 'StringMap', ], ], ], 'ConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Configuration', ], ], 'CreateSecurityConfigurationInput' => [ 'type' => 'structure', 'required' => [ 'Name', 'SecurityConfiguration', ], 'members' => [ 'Name' => [ 'shape' => 'XmlString', ], 'SecurityConfiguration' => [ 'shape' => 'String', ], ], ], 'CreateSecurityConfigurationOutput' => [ 'type' => 'structure', 'required' => [ 'Name', 'CreationDateTime', ], 'members' => [ 'Name' => [ 'shape' => 'XmlString', ], 'CreationDateTime' => [ 'shape' => 'Date', ], ], ], 'CreateStudioInput' => [ 'type' => 'structure', 'required' => [ 'Name', 'AuthMode', 'VpcId', 'SubnetIds', 'ServiceRole', 'WorkspaceSecurityGroupId', 'EngineSecurityGroupId', 'DefaultS3Location', ], 'members' => [ 'Name' => [ 'shape' => 'XmlStringMaxLen256', ], 'Description' => [ 'shape' => 'XmlStringMaxLen256', ], 'AuthMode' => [ 'shape' => 'AuthMode', ], 'VpcId' => [ 'shape' => 'XmlStringMaxLen256', ], 'SubnetIds' => [ 'shape' => 'SubnetIdList', ], 'ServiceRole' => [ 'shape' => 'XmlString', ], 'UserRole' => [ 'shape' => 'XmlString', ], 'WorkspaceSecurityGroupId' => [ 'shape' => 'XmlStringMaxLen256', ], 'EngineSecurityGroupId' => [ 'shape' => 'XmlStringMaxLen256', ], 'DefaultS3Location' => [ 'shape' => 'XmlString', ], 'IdpAuthUrl' => [ 'shape' => 'XmlString', ], 'IdpRelayStateParameterName' => [ 'shape' => 'XmlStringMaxLen256', ], 'Tags' => [ 'shape' => 'TagList', ], 'TrustedIdentityPropagationEnabled' => [ 'shape' => 'BooleanObject', ], 'IdcUserAssignment' => [ 'shape' => 'IdcUserAssignment', ], 'IdcInstanceArn' => [ 'shape' => 'ArnType', ], 'EncryptionKeyArn' => [ 'shape' => 'XmlString', ], ], ], 'CreateStudioOutput' => [ 'type' => 'structure', 'members' => [ 'StudioId' => [ 'shape' => 'XmlStringMaxLen256', ], 'Url' => [ 'shape' => 'XmlString', ], ], ], 'CreateStudioSessionMappingInput' => [ 'type' => 'structure', 'required' => [ 'StudioId', 'IdentityType', 'SessionPolicyArn', ], 'members' => [ 'StudioId' => [ 'shape' => 'XmlStringMaxLen256', ], 'IdentityId' => [ 'shape' => 'XmlStringMaxLen256', ], 'IdentityName' => [ 'shape' => 'XmlStringMaxLen256', ], 'IdentityType' => [ 'shape' => 'IdentityType', ], 'SessionPolicyArn' => [ 'shape' => 'XmlStringMaxLen256', ], ], ], 'Credentials' => [ 'type' => 'structure', 'members' => [ 'UsernamePassword' => [ 'shape' => 'UsernamePassword', ], ], 'union' => true, ], 'Date' => [ 'type' => 'timestamp', ], 'DeleteSecurityConfigurationInput' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'XmlString', ], ], ], 'DeleteSecurityConfigurationOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteStudioInput' => [ 'type' => 'structure', 'required' => [ 'StudioId', ], 'members' => [ 'StudioId' => [ 'shape' => 'XmlStringMaxLen256', ], ], ], 'DeleteStudioSessionMappingInput' => [ 'type' => 'structure', 'required' => [ 'StudioId', 'IdentityType', ], 'members' => [ 'StudioId' => [ 'shape' => 'XmlStringMaxLen256', ], 'IdentityId' => [ 'shape' => 'XmlStringMaxLen256', ], 'IdentityName' => [ 'shape' => 'XmlStringMaxLen256', ], 'IdentityType' => [ 'shape' => 'IdentityType', ], ], ], 'DescribeClusterInput' => [ 'type' => 'structure', 'required' => [ 'ClusterId', ], 'members' => [ 'ClusterId' => [ 'shape' => 'ClusterId', ], ], ], 'DescribeClusterOutput' => [ 'type' => 'structure', 'members' => [ 'Cluster' => [ 'shape' => 'Cluster', ], ], ], 'DescribeJobFlowsInput' => [ 'type' => 'structure', 'members' => [ 'CreatedAfter' => [ 'shape' => 'Date', ], 'CreatedBefore' => [ 'shape' => 'Date', ], 'JobFlowIds' => [ 'shape' => 'XmlStringList', ], 'JobFlowStates' => [ 'shape' => 'JobFlowExecutionStateList', ], ], ], 'DescribeJobFlowsOutput' => [ 'type' => 'structure', 'members' => [ 'JobFlows' => [ 'shape' => 'JobFlowDetailList', ], ], ], 'DescribeNotebookExecutionInput' => [ 'type' => 'structure', 'required' => [ 'NotebookExecutionId', ], 'members' => [ 'NotebookExecutionId' => [ 'shape' => 'XmlStringMaxLen256', ], ], ], 'DescribeNotebookExecutionOutput' => [ 'type' => 'structure', 'members' => [ 'NotebookExecution' => [ 'shape' => 'NotebookExecution', ], ], ], 'DescribeReleaseLabelInput' => [ 'type' => 'structure', 'members' => [ 'ReleaseLabel' => [ 'shape' => 'String', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'MaxResultsNumber', ], ], ], 'DescribeReleaseLabelOutput' => [ 'type' => 'structure', 'members' => [ 'ReleaseLabel' => [ 'shape' => 'String', ], 'Applications' => [ 'shape' => 'SimplifiedApplicationList', ], 'NextToken' => [ 'shape' => 'String', ], 'AvailableOSReleases' => [ 'shape' => 'OSReleaseList', ], ], ], 'DescribeSecurityConfigurationInput' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'XmlString', ], ], ], 'DescribeSecurityConfigurationOutput' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'XmlString', ], 'SecurityConfiguration' => [ 'shape' => 'String', ], 'CreationDateTime' => [ 'shape' => 'Date', ], ], ], 'DescribeStepInput' => [ 'type' => 'structure', 'required' => [ 'ClusterId', 'StepId', ], 'members' => [ 'ClusterId' => [ 'shape' => 'ClusterId', ], 'StepId' => [ 'shape' => 'StepId', ], ], ], 'DescribeStepOutput' => [ 'type' => 'structure', 'members' => [ 'Step' => [ 'shape' => 'Step', ], ], ], 'DescribeStudioInput' => [ 'type' => 'structure', 'required' => [ 'StudioId', ], 'members' => [ 'StudioId' => [ 'shape' => 'XmlStringMaxLen256', ], ], ], 'DescribeStudioOutput' => [ 'type' => 'structure', 'members' => [ 'Studio' => [ 'shape' => 'Studio', ], ], ], 'EC2InstanceIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceId', ], ], 'EC2InstanceIdsToTerminateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceId', ], ], 'EbsBlockDevice' => [ 'type' => 'structure', 'members' => [ 'VolumeSpecification' => [ 'shape' => 'VolumeSpecification', ], 'Device' => [ 'shape' => 'String', ], ], ], 'EbsBlockDeviceConfig' => [ 'type' => 'structure', 'required' => [ 'VolumeSpecification', ], 'members' => [ 'VolumeSpecification' => [ 'shape' => 'VolumeSpecification', ], 'VolumesPerInstance' => [ 'shape' => 'Integer', ], ], ], 'EbsBlockDeviceConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EbsBlockDeviceConfig', ], ], 'EbsBlockDeviceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EbsBlockDevice', ], ], 'EbsConfiguration' => [ 'type' => 'structure', 'members' => [ 'EbsBlockDeviceConfigs' => [ 'shape' => 'EbsBlockDeviceConfigList', ], 'EbsOptimized' => [ 'shape' => 'BooleanObject', ], ], ], 'EbsVolume' => [ 'type' => 'structure', 'members' => [ 'Device' => [ 'shape' => 'String', ], 'VolumeId' => [ 'shape' => 'String', ], ], ], 'EbsVolumeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EbsVolume', ], ], 'Ec2InstanceAttributes' => [ 'type' => 'structure', 'members' => [ 'Ec2KeyName' => [ 'shape' => 'String', ], 'Ec2SubnetId' => [ 'shape' => 'String', ], 'RequestedEc2SubnetIds' => [ 'shape' => 'XmlStringMaxLen256List', ], 'Ec2AvailabilityZone' => [ 'shape' => 'String', ], 'RequestedEc2AvailabilityZones' => [ 'shape' => 'XmlStringMaxLen256List', ], 'IamInstanceProfile' => [ 'shape' => 'String', ], 'EmrManagedMasterSecurityGroup' => [ 'shape' => 'String', ], 'EmrManagedSlaveSecurityGroup' => [ 'shape' => 'String', ], 'ServiceAccessSecurityGroup' => [ 'shape' => 'String', ], 'AdditionalMasterSecurityGroups' => [ 'shape' => 'StringList', ], 'AdditionalSlaveSecurityGroups' => [ 'shape' => 'StringList', ], ], ], 'EnvironmentVariablesMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'XmlStringMaxLen256', ], 'value' => [ 'shape' => 'XmlString', ], ], 'ErrorCode' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'ErrorData' => [ 'type' => 'list', 'member' => [ 'shape' => 'StringMap', ], ], 'ErrorDetail' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => 'String', ], 'ErrorData' => [ 'shape' => 'ErrorData', ], 'ErrorMessage' => [ 'shape' => 'String', ], ], ], 'ErrorDetailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ErrorDetail', ], ], 'ErrorMessage' => [ 'type' => 'string', ], 'ExecutionEngineConfig' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'XmlStringMaxLen256', ], 'Type' => [ 'shape' => 'ExecutionEngineType', ], 'MasterInstanceSecurityGroupId' => [ 'shape' => 'XmlStringMaxLen256', ], 'ExecutionRoleArn' => [ 'shape' => 'IAMRoleArn', ], ], ], 'ExecutionEngineType' => [ 'type' => 'string', 'enum' => [ 'EMR', ], ], 'FailureDetails' => [ 'type' => 'structure', 'members' => [ 'Reason' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], 'LogFile' => [ 'shape' => 'String', ], ], ], 'Float' => [ 'type' => 'float', ], 'GetAutoTerminationPolicyInput' => [ 'type' => 'structure', 'required' => [ 'ClusterId', ], 'members' => [ 'ClusterId' => [ 'shape' => 'ClusterId', ], ], ], 'GetAutoTerminationPolicyOutput' => [ 'type' => 'structure', 'members' => [ 'AutoTerminationPolicy' => [ 'shape' => 'AutoTerminationPolicy', ], ], ], 'GetBlockPublicAccessConfigurationInput' => [ 'type' => 'structure', 'members' => [], ], 'GetBlockPublicAccessConfigurationOutput' => [ 'type' => 'structure', 'required' => [ 'BlockPublicAccessConfiguration', 'BlockPublicAccessConfigurationMetadata', ], 'members' => [ 'BlockPublicAccessConfiguration' => [ 'shape' => 'BlockPublicAccessConfiguration', ], 'BlockPublicAccessConfigurationMetadata' => [ 'shape' => 'BlockPublicAccessConfigurationMetadata', ], ], ], 'GetClusterSessionCredentialsInput' => [ 'type' => 'structure', 'required' => [ 'ClusterId', ], 'members' => [ 'ClusterId' => [ 'shape' => 'XmlStringMaxLen256', ], 'ExecutionRoleArn' => [ 'shape' => 'ArnType', ], ], ], 'GetClusterSessionCredentialsOutput' => [ 'type' => 'structure', 'members' => [ 'Credentials' => [ 'shape' => 'Credentials', ], 'ExpiresAt' => [ 'shape' => 'Date', ], ], ], 'GetManagedScalingPolicyInput' => [ 'type' => 'structure', 'required' => [ 'ClusterId', ], 'members' => [ 'ClusterId' => [ 'shape' => 'ClusterId', ], ], ], 'GetManagedScalingPolicyOutput' => [ 'type' => 'structure', 'members' => [ 'ManagedScalingPolicy' => [ 'shape' => 'ManagedScalingPolicy', ], ], ], 'GetStudioSessionMappingInput' => [ 'type' => 'structure', 'required' => [ 'StudioId', 'IdentityType', ], 'members' => [ 'StudioId' => [ 'shape' => 'XmlStringMaxLen256', ], 'IdentityId' => [ 'shape' => 'XmlStringMaxLen256', ], 'IdentityName' => [ 'shape' => 'XmlStringMaxLen256', ], 'IdentityType' => [ 'shape' => 'IdentityType', ], ], ], 'GetStudioSessionMappingOutput' => [ 'type' => 'structure', 'members' => [ 'SessionMapping' => [ 'shape' => 'SessionMappingDetail', ], ], ], 'HadoopJarStepConfig' => [ 'type' => 'structure', 'required' => [ 'Jar', ], 'members' => [ 'Properties' => [ 'shape' => 'KeyValueList', ], 'Jar' => [ 'shape' => 'XmlString', ], 'MainClass' => [ 'shape' => 'XmlString', ], 'Args' => [ 'shape' => 'XmlStringList', ], ], ], 'HadoopStepConfig' => [ 'type' => 'structure', 'members' => [ 'Jar' => [ 'shape' => 'String', ], 'Properties' => [ 'shape' => 'StringMap', ], 'MainClass' => [ 'shape' => 'String', ], 'Args' => [ 'shape' => 'StringList', ], ], ], 'IAMRoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:(aws[a-zA-Z0-9-]*):iam::(\\d{12})?:(role((\\u002F)|(\\u002F[\\u0021-\\u007F]+\\u002F))[\\w+=,.@-]+)$', ], 'IdcUserAssignment' => [ 'type' => 'string', 'enum' => [ 'REQUIRED', 'OPTIONAL', ], ], 'IdentityType' => [ 'type' => 'string', 'enum' => [ 'USER', 'GROUP', ], ], 'Instance' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'InstanceId', ], 'Ec2InstanceId' => [ 'shape' => 'InstanceId', ], 'PublicDnsName' => [ 'shape' => 'String', ], 'PublicIpAddress' => [ 'shape' => 'String', ], 'PrivateDnsName' => [ 'shape' => 'String', ], 'PrivateIpAddress' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'InstanceStatus', ], 'InstanceGroupId' => [ 'shape' => 'String', ], 'InstanceFleetId' => [ 'shape' => 'InstanceFleetId', ], 'Market' => [ 'shape' => 'MarketType', ], 'InstanceType' => [ 'shape' => 'InstanceType', ], 'EbsVolumes' => [ 'shape' => 'EbsVolumeList', ], ], ], 'InstanceCollectionType' => [ 'type' => 'string', 'enum' => [ 'INSTANCE_FLEET', 'INSTANCE_GROUP', ], ], 'InstanceFleet' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'InstanceFleetId', ], 'Name' => [ 'shape' => 'XmlStringMaxLen256', ], 'Status' => [ 'shape' => 'InstanceFleetStatus', ], 'InstanceFleetType' => [ 'shape' => 'InstanceFleetType', ], 'TargetOnDemandCapacity' => [ 'shape' => 'WholeNumber', ], 'TargetSpotCapacity' => [ 'shape' => 'WholeNumber', ], 'ProvisionedOnDemandCapacity' => [ 'shape' => 'WholeNumber', ], 'ProvisionedSpotCapacity' => [ 'shape' => 'WholeNumber', ], 'InstanceTypeSpecifications' => [ 'shape' => 'InstanceTypeSpecificationList', ], 'LaunchSpecifications' => [ 'shape' => 'InstanceFleetProvisioningSpecifications', ], 'ResizeSpecifications' => [ 'shape' => 'InstanceFleetResizingSpecifications', ], ], ], 'InstanceFleetConfig' => [ 'type' => 'structure', 'required' => [ 'InstanceFleetType', ], 'members' => [ 'Name' => [ 'shape' => 'XmlStringMaxLen256', ], 'InstanceFleetType' => [ 'shape' => 'InstanceFleetType', ], 'TargetOnDemandCapacity' => [ 'shape' => 'WholeNumber', ], 'TargetSpotCapacity' => [ 'shape' => 'WholeNumber', ], 'InstanceTypeConfigs' => [ 'shape' => 'InstanceTypeConfigList', ], 'LaunchSpecifications' => [ 'shape' => 'InstanceFleetProvisioningSpecifications', ], 'ResizeSpecifications' => [ 'shape' => 'InstanceFleetResizingSpecifications', ], ], ], 'InstanceFleetConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceFleetConfig', ], ], 'InstanceFleetId' => [ 'type' => 'string', ], 'InstanceFleetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceFleet', ], ], 'InstanceFleetModifyConfig' => [ 'type' => 'structure', 'required' => [ 'InstanceFleetId', ], 'members' => [ 'InstanceFleetId' => [ 'shape' => 'InstanceFleetId', ], 'TargetOnDemandCapacity' => [ 'shape' => 'WholeNumber', ], 'TargetSpotCapacity' => [ 'shape' => 'WholeNumber', ], 'ResizeSpecifications' => [ 'shape' => 'InstanceFleetResizingSpecifications', ], 'InstanceTypeConfigs' => [ 'shape' => 'InstanceTypeConfigList', ], ], ], 'InstanceFleetProvisioningSpecifications' => [ 'type' => 'structure', 'members' => [ 'SpotSpecification' => [ 'shape' => 'SpotProvisioningSpecification', ], 'OnDemandSpecification' => [ 'shape' => 'OnDemandProvisioningSpecification', ], ], ], 'InstanceFleetResizingSpecifications' => [ 'type' => 'structure', 'members' => [ 'SpotResizeSpecification' => [ 'shape' => 'SpotResizingSpecification', ], 'OnDemandResizeSpecification' => [ 'shape' => 'OnDemandResizingSpecification', ], ], ], 'InstanceFleetState' => [ 'type' => 'string', 'enum' => [ 'PROVISIONING', 'BOOTSTRAPPING', 'RUNNING', 'RESIZING', 'SUSPENDED', 'TERMINATING', 'TERMINATED', ], ], 'InstanceFleetStateChangeReason' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'InstanceFleetStateChangeReasonCode', ], 'Message' => [ 'shape' => 'String', ], ], ], 'InstanceFleetStateChangeReasonCode' => [ 'type' => 'string', 'enum' => [ 'INTERNAL_ERROR', 'VALIDATION_ERROR', 'INSTANCE_FAILURE', 'CLUSTER_TERMINATED', ], ], 'InstanceFleetStatus' => [ 'type' => 'structure', 'members' => [ 'State' => [ 'shape' => 'InstanceFleetState', ], 'StateChangeReason' => [ 'shape' => 'InstanceFleetStateChangeReason', ], 'Timeline' => [ 'shape' => 'InstanceFleetTimeline', ], ], ], 'InstanceFleetTimeline' => [ 'type' => 'structure', 'members' => [ 'CreationDateTime' => [ 'shape' => 'Date', ], 'ReadyDateTime' => [ 'shape' => 'Date', ], 'EndDateTime' => [ 'shape' => 'Date', ], ], ], 'InstanceFleetType' => [ 'type' => 'string', 'enum' => [ 'MASTER', 'CORE', 'TASK', ], ], 'InstanceGroup' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'InstanceGroupId', ], 'Name' => [ 'shape' => 'String', ], 'Market' => [ 'shape' => 'MarketType', ], 'InstanceGroupType' => [ 'shape' => 'InstanceGroupType', ], 'BidPrice' => [ 'shape' => 'String', ], 'InstanceType' => [ 'shape' => 'InstanceType', ], 'RequestedInstanceCount' => [ 'shape' => 'Integer', ], 'RunningInstanceCount' => [ 'shape' => 'Integer', ], 'Status' => [ 'shape' => 'InstanceGroupStatus', ], 'Configurations' => [ 'shape' => 'ConfigurationList', ], 'ConfigurationsVersion' => [ 'shape' => 'Long', ], 'LastSuccessfullyAppliedConfigurations' => [ 'shape' => 'ConfigurationList', ], 'LastSuccessfullyAppliedConfigurationsVersion' => [ 'shape' => 'Long', ], 'EbsBlockDevices' => [ 'shape' => 'EbsBlockDeviceList', ], 'EbsOptimized' => [ 'shape' => 'BooleanObject', ], 'ShrinkPolicy' => [ 'shape' => 'ShrinkPolicy', ], 'AutoScalingPolicy' => [ 'shape' => 'AutoScalingPolicyDescription', ], 'CustomAmiId' => [ 'shape' => 'XmlStringMaxLen256', ], ], ], 'InstanceGroupConfig' => [ 'type' => 'structure', 'required' => [ 'InstanceRole', 'InstanceType', 'InstanceCount', ], 'members' => [ 'Name' => [ 'shape' => 'XmlStringMaxLen256', ], 'Market' => [ 'shape' => 'MarketType', ], 'InstanceRole' => [ 'shape' => 'InstanceRoleType', ], 'BidPrice' => [ 'shape' => 'XmlStringMaxLen256', ], 'InstanceType' => [ 'shape' => 'InstanceType', ], 'InstanceCount' => [ 'shape' => 'Integer', ], 'Configurations' => [ 'shape' => 'ConfigurationList', ], 'EbsConfiguration' => [ 'shape' => 'EbsConfiguration', ], 'AutoScalingPolicy' => [ 'shape' => 'AutoScalingPolicy', ], 'CustomAmiId' => [ 'shape' => 'XmlStringMaxLen256', ], ], ], 'InstanceGroupConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceGroupConfig', ], ], 'InstanceGroupDetail' => [ 'type' => 'structure', 'required' => [ 'Market', 'InstanceRole', 'InstanceType', 'InstanceRequestCount', 'InstanceRunningCount', 'State', 'CreationDateTime', ], 'members' => [ 'InstanceGroupId' => [ 'shape' => 'XmlStringMaxLen256', ], 'Name' => [ 'shape' => 'XmlStringMaxLen256', ], 'Market' => [ 'shape' => 'MarketType', ], 'InstanceRole' => [ 'shape' => 'InstanceRoleType', ], 'BidPrice' => [ 'shape' => 'XmlStringMaxLen256', ], 'InstanceType' => [ 'shape' => 'InstanceType', ], 'InstanceRequestCount' => [ 'shape' => 'Integer', ], 'InstanceRunningCount' => [ 'shape' => 'Integer', ], 'State' => [ 'shape' => 'InstanceGroupState', ], 'LastStateChangeReason' => [ 'shape' => 'XmlString', ], 'CreationDateTime' => [ 'shape' => 'Date', ], 'StartDateTime' => [ 'shape' => 'Date', ], 'ReadyDateTime' => [ 'shape' => 'Date', ], 'EndDateTime' => [ 'shape' => 'Date', ], 'CustomAmiId' => [ 'shape' => 'XmlStringMaxLen256', ], ], ], 'InstanceGroupDetailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceGroupDetail', ], ], 'InstanceGroupId' => [ 'type' => 'string', ], 'InstanceGroupIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'XmlStringMaxLen256', ], ], 'InstanceGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceGroup', ], ], 'InstanceGroupModifyConfig' => [ 'type' => 'structure', 'required' => [ 'InstanceGroupId', ], 'members' => [ 'InstanceGroupId' => [ 'shape' => 'XmlStringMaxLen256', ], 'InstanceCount' => [ 'shape' => 'Integer', ], 'EC2InstanceIdsToTerminate' => [ 'shape' => 'EC2InstanceIdsToTerminateList', ], 'ShrinkPolicy' => [ 'shape' => 'ShrinkPolicy', ], 'ReconfigurationType' => [ 'shape' => 'ReconfigurationType', ], 'Configurations' => [ 'shape' => 'ConfigurationList', ], ], ], 'InstanceGroupModifyConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceGroupModifyConfig', ], ], 'InstanceGroupState' => [ 'type' => 'string', 'enum' => [ 'PROVISIONING', 'BOOTSTRAPPING', 'RUNNING', 'RECONFIGURING', 'RESIZING', 'SUSPENDED', 'TERMINATING', 'TERMINATED', 'ARRESTED', 'SHUTTING_DOWN', 'ENDED', ], ], 'InstanceGroupStateChangeReason' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'InstanceGroupStateChangeReasonCode', ], 'Message' => [ 'shape' => 'String', ], ], ], 'InstanceGroupStateChangeReasonCode' => [ 'type' => 'string', 'enum' => [ 'INTERNAL_ERROR', 'VALIDATION_ERROR', 'INSTANCE_FAILURE', 'CLUSTER_TERMINATED', ], ], 'InstanceGroupStatus' => [ 'type' => 'structure', 'members' => [ 'State' => [ 'shape' => 'InstanceGroupState', ], 'StateChangeReason' => [ 'shape' => 'InstanceGroupStateChangeReason', ], 'Timeline' => [ 'shape' => 'InstanceGroupTimeline', ], ], ], 'InstanceGroupTimeline' => [ 'type' => 'structure', 'members' => [ 'CreationDateTime' => [ 'shape' => 'Date', ], 'ReadyDateTime' => [ 'shape' => 'Date', ], 'EndDateTime' => [ 'shape' => 'Date', ], ], ], 'InstanceGroupType' => [ 'type' => 'string', 'enum' => [ 'MASTER', 'CORE', 'TASK', ], ], 'InstanceGroupTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceGroupType', ], ], 'InstanceId' => [ 'type' => 'string', ], 'InstanceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Instance', ], ], 'InstanceResizePolicy' => [ 'type' => 'structure', 'members' => [ 'InstancesToTerminate' => [ 'shape' => 'EC2InstanceIdsList', ], 'InstancesToProtect' => [ 'shape' => 'EC2InstanceIdsList', ], 'InstanceTerminationTimeout' => [ 'shape' => 'Integer', ], ], ], 'InstanceRoleType' => [ 'type' => 'string', 'enum' => [ 'MASTER', 'CORE', 'TASK', ], ], 'InstanceState' => [ 'type' => 'string', 'enum' => [ 'AWAITING_FULFILLMENT', 'PROVISIONING', 'BOOTSTRAPPING', 'RUNNING', 'TERMINATED', ], ], 'InstanceStateChangeReason' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'InstanceStateChangeReasonCode', ], 'Message' => [ 'shape' => 'String', ], ], ], 'InstanceStateChangeReasonCode' => [ 'type' => 'string', 'enum' => [ 'INTERNAL_ERROR', 'VALIDATION_ERROR', 'INSTANCE_FAILURE', 'BOOTSTRAP_FAILURE', 'CLUSTER_TERMINATED', ], ], 'InstanceStateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceState', ], ], 'InstanceStatus' => [ 'type' => 'structure', 'members' => [ 'State' => [ 'shape' => 'InstanceState', ], 'StateChangeReason' => [ 'shape' => 'InstanceStateChangeReason', ], 'Timeline' => [ 'shape' => 'InstanceTimeline', ], ], ], 'InstanceTimeline' => [ 'type' => 'structure', 'members' => [ 'CreationDateTime' => [ 'shape' => 'Date', ], 'ReadyDateTime' => [ 'shape' => 'Date', ], 'EndDateTime' => [ 'shape' => 'Date', ], ], ], 'InstanceType' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'InstanceTypeConfig' => [ 'type' => 'structure', 'required' => [ 'InstanceType', ], 'members' => [ 'InstanceType' => [ 'shape' => 'InstanceType', ], 'WeightedCapacity' => [ 'shape' => 'WholeNumber', ], 'BidPrice' => [ 'shape' => 'XmlStringMaxLen256', ], 'BidPriceAsPercentageOfOnDemandPrice' => [ 'shape' => 'NonNegativeDouble', ], 'EbsConfiguration' => [ 'shape' => 'EbsConfiguration', ], 'Configurations' => [ 'shape' => 'ConfigurationList', ], 'CustomAmiId' => [ 'shape' => 'XmlStringMaxLen256', ], 'Priority' => [ 'shape' => 'NonNegativeDouble', ], ], ], 'InstanceTypeConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceTypeConfig', ], ], 'InstanceTypeSpecification' => [ 'type' => 'structure', 'members' => [ 'InstanceType' => [ 'shape' => 'InstanceType', ], 'WeightedCapacity' => [ 'shape' => 'WholeNumber', ], 'BidPrice' => [ 'shape' => 'XmlStringMaxLen256', ], 'BidPriceAsPercentageOfOnDemandPrice' => [ 'shape' => 'NonNegativeDouble', ], 'Configurations' => [ 'shape' => 'ConfigurationList', ], 'EbsBlockDevices' => [ 'shape' => 'EbsBlockDeviceList', ], 'EbsOptimized' => [ 'shape' => 'BooleanObject', ], 'CustomAmiId' => [ 'shape' => 'XmlStringMaxLen256', ], 'Priority' => [ 'shape' => 'NonNegativeDouble', ], ], ], 'InstanceTypeSpecificationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceTypeSpecification', ], ], 'Integer' => [ 'type' => 'integer', ], 'InternalServerError' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, 'fault' => true, ], 'InvalidRequestException' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'JobFlowDetail' => [ 'type' => 'structure', 'required' => [ 'JobFlowId', 'Name', 'ExecutionStatusDetail', 'Instances', ], 'members' => [ 'JobFlowId' => [ 'shape' => 'XmlStringMaxLen256', ], 'Name' => [ 'shape' => 'XmlStringMaxLen256', ], 'LogUri' => [ 'shape' => 'XmlString', ], 'LogEncryptionKmsKeyId' => [ 'shape' => 'XmlString', ], 'AmiVersion' => [ 'shape' => 'XmlStringMaxLen256', ], 'ExecutionStatusDetail' => [ 'shape' => 'JobFlowExecutionStatusDetail', ], 'Instances' => [ 'shape' => 'JobFlowInstancesDetail', ], 'Steps' => [ 'shape' => 'StepDetailList', ], 'BootstrapActions' => [ 'shape' => 'BootstrapActionDetailList', ], 'SupportedProducts' => [ 'shape' => 'SupportedProductsList', ], 'VisibleToAllUsers' => [ 'shape' => 'Boolean', ], 'JobFlowRole' => [ 'shape' => 'XmlString', ], 'ServiceRole' => [ 'shape' => 'XmlString', ], 'AutoScalingRole' => [ 'shape' => 'XmlString', ], 'ScaleDownBehavior' => [ 'shape' => 'ScaleDownBehavior', ], ], ], 'JobFlowDetailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobFlowDetail', ], ], 'JobFlowExecutionState' => [ 'type' => 'string', 'enum' => [ 'STARTING', 'BOOTSTRAPPING', 'RUNNING', 'WAITING', 'SHUTTING_DOWN', 'TERMINATED', 'COMPLETED', 'FAILED', ], ], 'JobFlowExecutionStateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobFlowExecutionState', ], ], 'JobFlowExecutionStatusDetail' => [ 'type' => 'structure', 'required' => [ 'State', 'CreationDateTime', ], 'members' => [ 'State' => [ 'shape' => 'JobFlowExecutionState', ], 'CreationDateTime' => [ 'shape' => 'Date', ], 'StartDateTime' => [ 'shape' => 'Date', ], 'ReadyDateTime' => [ 'shape' => 'Date', ], 'EndDateTime' => [ 'shape' => 'Date', ], 'LastStateChangeReason' => [ 'shape' => 'XmlString', ], ], ], 'JobFlowInstancesConfig' => [ 'type' => 'structure', 'members' => [ 'MasterInstanceType' => [ 'shape' => 'InstanceType', ], 'SlaveInstanceType' => [ 'shape' => 'InstanceType', ], 'InstanceCount' => [ 'shape' => 'Integer', ], 'InstanceGroups' => [ 'shape' => 'InstanceGroupConfigList', ], 'InstanceFleets' => [ 'shape' => 'InstanceFleetConfigList', ], 'Ec2KeyName' => [ 'shape' => 'XmlStringMaxLen256', ], 'Placement' => [ 'shape' => 'PlacementType', ], 'KeepJobFlowAliveWhenNoSteps' => [ 'shape' => 'Boolean', ], 'TerminationProtected' => [ 'shape' => 'Boolean', ], 'UnhealthyNodeReplacement' => [ 'shape' => 'BooleanObject', ], 'HadoopVersion' => [ 'shape' => 'XmlStringMaxLen256', ], 'Ec2SubnetId' => [ 'shape' => 'XmlStringMaxLen256', ], 'Ec2SubnetIds' => [ 'shape' => 'XmlStringMaxLen256List', ], 'EmrManagedMasterSecurityGroup' => [ 'shape' => 'XmlStringMaxLen256', ], 'EmrManagedSlaveSecurityGroup' => [ 'shape' => 'XmlStringMaxLen256', ], 'ServiceAccessSecurityGroup' => [ 'shape' => 'XmlStringMaxLen256', ], 'AdditionalMasterSecurityGroups' => [ 'shape' => 'SecurityGroupsList', ], 'AdditionalSlaveSecurityGroups' => [ 'shape' => 'SecurityGroupsList', ], ], ], 'JobFlowInstancesDetail' => [ 'type' => 'structure', 'required' => [ 'MasterInstanceType', 'SlaveInstanceType', 'InstanceCount', ], 'members' => [ 'MasterInstanceType' => [ 'shape' => 'InstanceType', ], 'MasterPublicDnsName' => [ 'shape' => 'XmlString', ], 'MasterInstanceId' => [ 'shape' => 'XmlString', ], 'SlaveInstanceType' => [ 'shape' => 'InstanceType', ], 'InstanceCount' => [ 'shape' => 'Integer', ], 'InstanceGroups' => [ 'shape' => 'InstanceGroupDetailList', ], 'NormalizedInstanceHours' => [ 'shape' => 'Integer', ], 'Ec2KeyName' => [ 'shape' => 'XmlStringMaxLen256', ], 'Ec2SubnetId' => [ 'shape' => 'XmlStringMaxLen256', ], 'Placement' => [ 'shape' => 'PlacementType', ], 'KeepJobFlowAliveWhenNoSteps' => [ 'shape' => 'Boolean', ], 'TerminationProtected' => [ 'shape' => 'Boolean', ], 'UnhealthyNodeReplacement' => [ 'shape' => 'BooleanObject', ], 'HadoopVersion' => [ 'shape' => 'XmlStringMaxLen256', ], ], ], 'KerberosAttributes' => [ 'type' => 'structure', 'required' => [ 'Realm', 'KdcAdminPassword', ], 'members' => [ 'Realm' => [ 'shape' => 'XmlStringMaxLen256', ], 'KdcAdminPassword' => [ 'shape' => 'XmlStringMaxLen256', ], 'CrossRealmTrustPrincipalPassword' => [ 'shape' => 'XmlStringMaxLen256', ], 'ADDomainJoinUser' => [ 'shape' => 'XmlStringMaxLen256', ], 'ADDomainJoinPassword' => [ 'shape' => 'XmlStringMaxLen256', ], ], ], 'KeyValue' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'XmlString', ], 'Value' => [ 'shape' => 'XmlString', ], ], ], 'KeyValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KeyValue', ], ], 'ListBootstrapActionsInput' => [ 'type' => 'structure', 'required' => [ 'ClusterId', ], 'members' => [ 'ClusterId' => [ 'shape' => 'ClusterId', ], 'Marker' => [ 'shape' => 'Marker', ], ], ], 'ListBootstrapActionsOutput' => [ 'type' => 'structure', 'members' => [ 'BootstrapActions' => [ 'shape' => 'CommandList', ], 'Marker' => [ 'shape' => 'Marker', ], ], ], 'ListClustersInput' => [ 'type' => 'structure', 'members' => [ 'CreatedAfter' => [ 'shape' => 'Date', ], 'CreatedBefore' => [ 'shape' => 'Date', ], 'ClusterStates' => [ 'shape' => 'ClusterStateList', ], 'Marker' => [ 'shape' => 'Marker', ], ], ], 'ListClustersOutput' => [ 'type' => 'structure', 'members' => [ 'Clusters' => [ 'shape' => 'ClusterSummaryList', ], 'Marker' => [ 'shape' => 'Marker', ], ], ], 'ListInstanceFleetsInput' => [ 'type' => 'structure', 'required' => [ 'ClusterId', ], 'members' => [ 'ClusterId' => [ 'shape' => 'ClusterId', ], 'Marker' => [ 'shape' => 'Marker', ], ], ], 'ListInstanceFleetsOutput' => [ 'type' => 'structure', 'members' => [ 'InstanceFleets' => [ 'shape' => 'InstanceFleetList', ], 'Marker' => [ 'shape' => 'Marker', ], ], ], 'ListInstanceGroupsInput' => [ 'type' => 'structure', 'required' => [ 'ClusterId', ], 'members' => [ 'ClusterId' => [ 'shape' => 'ClusterId', ], 'Marker' => [ 'shape' => 'Marker', ], ], ], 'ListInstanceGroupsOutput' => [ 'type' => 'structure', 'members' => [ 'InstanceGroups' => [ 'shape' => 'InstanceGroupList', ], 'Marker' => [ 'shape' => 'Marker', ], ], ], 'ListInstancesInput' => [ 'type' => 'structure', 'required' => [ 'ClusterId', ], 'members' => [ 'ClusterId' => [ 'shape' => 'ClusterId', ], 'InstanceGroupId' => [ 'shape' => 'InstanceGroupId', ], 'InstanceGroupTypes' => [ 'shape' => 'InstanceGroupTypeList', ], 'InstanceFleetId' => [ 'shape' => 'InstanceFleetId', ], 'InstanceFleetType' => [ 'shape' => 'InstanceFleetType', ], 'InstanceStates' => [ 'shape' => 'InstanceStateList', ], 'Marker' => [ 'shape' => 'Marker', ], ], ], 'ListInstancesOutput' => [ 'type' => 'structure', 'members' => [ 'Instances' => [ 'shape' => 'InstanceList', ], 'Marker' => [ 'shape' => 'Marker', ], ], ], 'ListNotebookExecutionsInput' => [ 'type' => 'structure', 'members' => [ 'EditorId' => [ 'shape' => 'XmlStringMaxLen256', ], 'Status' => [ 'shape' => 'NotebookExecutionStatus', ], 'From' => [ 'shape' => 'Date', ], 'To' => [ 'shape' => 'Date', ], 'Marker' => [ 'shape' => 'Marker', ], 'ExecutionEngineId' => [ 'shape' => 'XmlString', ], ], ], 'ListNotebookExecutionsOutput' => [ 'type' => 'structure', 'members' => [ 'NotebookExecutions' => [ 'shape' => 'NotebookExecutionSummaryList', ], 'Marker' => [ 'shape' => 'Marker', ], ], ], 'ListReleaseLabelsInput' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'ReleaseLabelFilter', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'MaxResultsNumber', ], ], ], 'ListReleaseLabelsOutput' => [ 'type' => 'structure', 'members' => [ 'ReleaseLabels' => [ 'shape' => 'StringList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListSecurityConfigurationsInput' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'Marker', ], ], ], 'ListSecurityConfigurationsOutput' => [ 'type' => 'structure', 'members' => [ 'SecurityConfigurations' => [ 'shape' => 'SecurityConfigurationList', ], 'Marker' => [ 'shape' => 'Marker', ], ], ], 'ListStepsInput' => [ 'type' => 'structure', 'required' => [ 'ClusterId', ], 'members' => [ 'ClusterId' => [ 'shape' => 'ClusterId', ], 'StepStates' => [ 'shape' => 'StepStateList', ], 'StepIds' => [ 'shape' => 'XmlStringList', ], 'Marker' => [ 'shape' => 'Marker', ], ], ], 'ListStepsOutput' => [ 'type' => 'structure', 'members' => [ 'Steps' => [ 'shape' => 'StepSummaryList', ], 'Marker' => [ 'shape' => 'Marker', ], ], ], 'ListStudioSessionMappingsInput' => [ 'type' => 'structure', 'members' => [ 'StudioId' => [ 'shape' => 'XmlStringMaxLen256', ], 'IdentityType' => [ 'shape' => 'IdentityType', ], 'Marker' => [ 'shape' => 'Marker', ], ], ], 'ListStudioSessionMappingsOutput' => [ 'type' => 'structure', 'members' => [ 'SessionMappings' => [ 'shape' => 'SessionMappingSummaryList', ], 'Marker' => [ 'shape' => 'Marker', ], ], ], 'ListStudiosInput' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'Marker', ], ], ], 'ListStudiosOutput' => [ 'type' => 'structure', 'members' => [ 'Studios' => [ 'shape' => 'StudioSummaryList', ], 'Marker' => [ 'shape' => 'Marker', ], ], ], 'ListSupportedInstanceTypesInput' => [ 'type' => 'structure', 'required' => [ 'ReleaseLabel', ], 'members' => [ 'ReleaseLabel' => [ 'shape' => 'String', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'ListSupportedInstanceTypesOutput' => [ 'type' => 'structure', 'members' => [ 'SupportedInstanceTypes' => [ 'shape' => 'SupportedInstanceTypesList', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'Long' => [ 'type' => 'long', ], 'ManagedScalingPolicy' => [ 'type' => 'structure', 'members' => [ 'ComputeLimits' => [ 'shape' => 'ComputeLimits', ], ], ], 'Marker' => [ 'type' => 'string', ], 'MarketType' => [ 'type' => 'string', 'enum' => [ 'ON_DEMAND', 'SPOT', ], ], 'MaxResultsNumber' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'MetricDimension' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'String', ], ], ], 'MetricDimensionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricDimension', ], ], 'ModifyClusterInput' => [ 'type' => 'structure', 'required' => [ 'ClusterId', ], 'members' => [ 'ClusterId' => [ 'shape' => 'String', ], 'StepConcurrencyLevel' => [ 'shape' => 'Integer', ], ], ], 'ModifyClusterOutput' => [ 'type' => 'structure', 'members' => [ 'StepConcurrencyLevel' => [ 'shape' => 'Integer', ], ], ], 'ModifyInstanceFleetInput' => [ 'type' => 'structure', 'required' => [ 'ClusterId', 'InstanceFleet', ], 'members' => [ 'ClusterId' => [ 'shape' => 'ClusterId', ], 'InstanceFleet' => [ 'shape' => 'InstanceFleetModifyConfig', ], ], ], 'ModifyInstanceGroupsInput' => [ 'type' => 'structure', 'members' => [ 'ClusterId' => [ 'shape' => 'ClusterId', ], 'InstanceGroups' => [ 'shape' => 'InstanceGroupModifyConfigList', ], ], ], 'NewSupportedProductsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SupportedProductConfig', ], ], 'NonNegativeDouble' => [ 'type' => 'double', 'min' => 0.0, ], 'NotebookExecution' => [ 'type' => 'structure', 'members' => [ 'NotebookExecutionId' => [ 'shape' => 'XmlStringMaxLen256', ], 'EditorId' => [ 'shape' => 'XmlStringMaxLen256', ], 'ExecutionEngine' => [ 'shape' => 'ExecutionEngineConfig', ], 'NotebookExecutionName' => [ 'shape' => 'XmlStringMaxLen256', ], 'NotebookParams' => [ 'shape' => 'XmlString', ], 'Status' => [ 'shape' => 'NotebookExecutionStatus', ], 'StartTime' => [ 'shape' => 'Date', ], 'EndTime' => [ 'shape' => 'Date', ], 'Arn' => [ 'shape' => 'XmlStringMaxLen256', ], 'OutputNotebookURI' => [ 'shape' => 'XmlString', ], 'LastStateChangeReason' => [ 'shape' => 'XmlString', ], 'NotebookInstanceSecurityGroupId' => [ 'shape' => 'XmlStringMaxLen256', ], 'Tags' => [ 'shape' => 'TagList', ], 'NotebookS3Location' => [ 'shape' => 'NotebookS3LocationForOutput', ], 'OutputNotebookS3Location' => [ 'shape' => 'OutputNotebookS3LocationForOutput', ], 'OutputNotebookFormat' => [ 'shape' => 'OutputNotebookFormat', ], 'EnvironmentVariables' => [ 'shape' => 'EnvironmentVariablesMap', ], ], ], 'NotebookExecutionStatus' => [ 'type' => 'string', 'enum' => [ 'START_PENDING', 'STARTING', 'RUNNING', 'FINISHING', 'FINISHED', 'FAILING', 'FAILED', 'STOP_PENDING', 'STOPPING', 'STOPPED', ], ], 'NotebookExecutionSummary' => [ 'type' => 'structure', 'members' => [ 'NotebookExecutionId' => [ 'shape' => 'XmlStringMaxLen256', ], 'EditorId' => [ 'shape' => 'XmlStringMaxLen256', ], 'NotebookExecutionName' => [ 'shape' => 'XmlStringMaxLen256', ], 'Status' => [ 'shape' => 'NotebookExecutionStatus', ], 'StartTime' => [ 'shape' => 'Date', ], 'EndTime' => [ 'shape' => 'Date', ], 'NotebookS3Location' => [ 'shape' => 'NotebookS3LocationForOutput', ], 'ExecutionEngineId' => [ 'shape' => 'XmlString', ], ], ], 'NotebookExecutionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NotebookExecutionSummary', ], ], 'NotebookS3LocationForOutput' => [ 'type' => 'structure', 'members' => [ 'Bucket' => [ 'shape' => 'XmlStringMaxLen256', ], 'Key' => [ 'shape' => 'UriString', ], ], ], 'NotebookS3LocationFromInput' => [ 'type' => 'structure', 'members' => [ 'Bucket' => [ 'shape' => 'XmlStringMaxLen256', ], 'Key' => [ 'shape' => 'UriString', ], ], ], 'OSRelease' => [ 'type' => 'structure', 'members' => [ 'Label' => [ 'shape' => 'String', ], ], ], 'OSReleaseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OSRelease', ], ], 'OnDemandCapacityReservationOptions' => [ 'type' => 'structure', 'members' => [ 'UsageStrategy' => [ 'shape' => 'OnDemandCapacityReservationUsageStrategy', ], 'CapacityReservationPreference' => [ 'shape' => 'OnDemandCapacityReservationPreference', ], 'CapacityReservationResourceGroupArn' => [ 'shape' => 'XmlStringMaxLen256', ], ], ], 'OnDemandCapacityReservationPreference' => [ 'type' => 'string', 'enum' => [ 'open', 'none', ], ], 'OnDemandCapacityReservationUsageStrategy' => [ 'type' => 'string', 'enum' => [ 'use-capacity-reservations-first', ], ], 'OnDemandProvisioningAllocationStrategy' => [ 'type' => 'string', 'enum' => [ 'lowest-price', 'prioritized', ], ], 'OnDemandProvisioningSpecification' => [ 'type' => 'structure', 'required' => [ 'AllocationStrategy', ], 'members' => [ 'AllocationStrategy' => [ 'shape' => 'OnDemandProvisioningAllocationStrategy', ], 'CapacityReservationOptions' => [ 'shape' => 'OnDemandCapacityReservationOptions', ], ], ], 'OnDemandResizingSpecification' => [ 'type' => 'structure', 'members' => [ 'TimeoutDurationMinutes' => [ 'shape' => 'WholeNumber', ], 'AllocationStrategy' => [ 'shape' => 'OnDemandProvisioningAllocationStrategy', ], 'CapacityReservationOptions' => [ 'shape' => 'OnDemandCapacityReservationOptions', ], ], ], 'OptionalArnType' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, ], 'OutputNotebookFormat' => [ 'type' => 'string', 'enum' => [ 'HTML', ], ], 'OutputNotebookS3LocationForOutput' => [ 'type' => 'structure', 'members' => [ 'Bucket' => [ 'shape' => 'XmlStringMaxLen256', ], 'Key' => [ 'shape' => 'UriString', ], ], ], 'OutputNotebookS3LocationFromInput' => [ 'type' => 'structure', 'members' => [ 'Bucket' => [ 'shape' => 'XmlStringMaxLen256', ], 'Key' => [ 'shape' => 'UriString', ], ], ], 'PlacementGroupConfig' => [ 'type' => 'structure', 'required' => [ 'InstanceRole', ], 'members' => [ 'InstanceRole' => [ 'shape' => 'InstanceRoleType', ], 'PlacementStrategy' => [ 'shape' => 'PlacementGroupStrategy', ], ], ], 'PlacementGroupConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PlacementGroupConfig', ], ], 'PlacementGroupStrategy' => [ 'type' => 'string', 'enum' => [ 'SPREAD', 'PARTITION', 'CLUSTER', 'NONE', ], ], 'PlacementType' => [ 'type' => 'structure', 'members' => [ 'AvailabilityZone' => [ 'shape' => 'XmlString', ], 'AvailabilityZones' => [ 'shape' => 'XmlStringMaxLen256List', ], ], ], 'Port' => [ 'type' => 'integer', 'max' => 65535, 'min' => -1, ], 'PortRange' => [ 'type' => 'structure', 'required' => [ 'MinRange', ], 'members' => [ 'MinRange' => [ 'shape' => 'Port', ], 'MaxRange' => [ 'shape' => 'Port', ], ], ], 'PortRanges' => [ 'type' => 'list', 'member' => [ 'shape' => 'PortRange', ], ], 'PutAutoScalingPolicyInput' => [ 'type' => 'structure', 'required' => [ 'ClusterId', 'InstanceGroupId', 'AutoScalingPolicy', ], 'members' => [ 'ClusterId' => [ 'shape' => 'ClusterId', ], 'InstanceGroupId' => [ 'shape' => 'InstanceGroupId', ], 'AutoScalingPolicy' => [ 'shape' => 'AutoScalingPolicy', ], ], ], 'PutAutoScalingPolicyOutput' => [ 'type' => 'structure', 'members' => [ 'ClusterId' => [ 'shape' => 'ClusterId', ], 'InstanceGroupId' => [ 'shape' => 'InstanceGroupId', ], 'AutoScalingPolicy' => [ 'shape' => 'AutoScalingPolicyDescription', ], 'ClusterArn' => [ 'shape' => 'ArnType', ], ], ], 'PutAutoTerminationPolicyInput' => [ 'type' => 'structure', 'required' => [ 'ClusterId', ], 'members' => [ 'ClusterId' => [ 'shape' => 'ClusterId', ], 'AutoTerminationPolicy' => [ 'shape' => 'AutoTerminationPolicy', ], ], ], 'PutAutoTerminationPolicyOutput' => [ 'type' => 'structure', 'members' => [], ], 'PutBlockPublicAccessConfigurationInput' => [ 'type' => 'structure', 'required' => [ 'BlockPublicAccessConfiguration', ], 'members' => [ 'BlockPublicAccessConfiguration' => [ 'shape' => 'BlockPublicAccessConfiguration', ], ], ], 'PutBlockPublicAccessConfigurationOutput' => [ 'type' => 'structure', 'members' => [], ], 'PutManagedScalingPolicyInput' => [ 'type' => 'structure', 'required' => [ 'ClusterId', 'ManagedScalingPolicy', ], 'members' => [ 'ClusterId' => [ 'shape' => 'ClusterId', ], 'ManagedScalingPolicy' => [ 'shape' => 'ManagedScalingPolicy', ], ], ], 'PutManagedScalingPolicyOutput' => [ 'type' => 'structure', 'members' => [], ], 'ReconfigurationType' => [ 'type' => 'string', 'enum' => [ 'OVERWRITE', 'MERGE', ], ], 'ReleaseLabelFilter' => [ 'type' => 'structure', 'members' => [ 'Prefix' => [ 'shape' => 'String', ], 'Application' => [ 'shape' => 'String', ], ], ], 'RemoveAutoScalingPolicyInput' => [ 'type' => 'structure', 'required' => [ 'ClusterId', 'InstanceGroupId', ], 'members' => [ 'ClusterId' => [ 'shape' => 'ClusterId', ], 'InstanceGroupId' => [ 'shape' => 'InstanceGroupId', ], ], ], 'RemoveAutoScalingPolicyOutput' => [ 'type' => 'structure', 'members' => [], ], 'RemoveAutoTerminationPolicyInput' => [ 'type' => 'structure', 'required' => [ 'ClusterId', ], 'members' => [ 'ClusterId' => [ 'shape' => 'ClusterId', ], ], ], 'RemoveAutoTerminationPolicyOutput' => [ 'type' => 'structure', 'members' => [], ], 'RemoveManagedScalingPolicyInput' => [ 'type' => 'structure', 'required' => [ 'ClusterId', ], 'members' => [ 'ClusterId' => [ 'shape' => 'ClusterId', ], ], ], 'RemoveManagedScalingPolicyOutput' => [ 'type' => 'structure', 'members' => [], ], 'RemoveTagsInput' => [ 'type' => 'structure', 'required' => [ 'ResourceId', 'TagKeys', ], 'members' => [ 'ResourceId' => [ 'shape' => 'ResourceId', ], 'TagKeys' => [ 'shape' => 'StringList', ], ], ], 'RemoveTagsOutput' => [ 'type' => 'structure', 'members' => [], ], 'RepoUpgradeOnBoot' => [ 'type' => 'string', 'enum' => [ 'SECURITY', 'NONE', ], ], 'ResourceId' => [ 'type' => 'string', ], 'RunJobFlowInput' => [ 'type' => 'structure', 'required' => [ 'Name', 'Instances', ], 'members' => [ 'Name' => [ 'shape' => 'XmlStringMaxLen256', ], 'LogUri' => [ 'shape' => 'XmlString', ], 'LogEncryptionKmsKeyId' => [ 'shape' => 'XmlString', ], 'AdditionalInfo' => [ 'shape' => 'XmlString', ], 'AmiVersion' => [ 'shape' => 'XmlStringMaxLen256', ], 'ReleaseLabel' => [ 'shape' => 'XmlStringMaxLen256', ], 'Instances' => [ 'shape' => 'JobFlowInstancesConfig', ], 'Steps' => [ 'shape' => 'StepConfigList', ], 'BootstrapActions' => [ 'shape' => 'BootstrapActionConfigList', ], 'SupportedProducts' => [ 'shape' => 'SupportedProductsList', ], 'NewSupportedProducts' => [ 'shape' => 'NewSupportedProductsList', ], 'Applications' => [ 'shape' => 'ApplicationList', ], 'Configurations' => [ 'shape' => 'ConfigurationList', ], 'VisibleToAllUsers' => [ 'shape' => 'Boolean', ], 'JobFlowRole' => [ 'shape' => 'XmlString', ], 'ServiceRole' => [ 'shape' => 'XmlString', ], 'Tags' => [ 'shape' => 'TagList', ], 'SecurityConfiguration' => [ 'shape' => 'XmlString', ], 'AutoScalingRole' => [ 'shape' => 'XmlString', ], 'ScaleDownBehavior' => [ 'shape' => 'ScaleDownBehavior', ], 'CustomAmiId' => [ 'shape' => 'XmlStringMaxLen256', ], 'EbsRootVolumeSize' => [ 'shape' => 'Integer', ], 'RepoUpgradeOnBoot' => [ 'shape' => 'RepoUpgradeOnBoot', ], 'KerberosAttributes' => [ 'shape' => 'KerberosAttributes', ], 'StepConcurrencyLevel' => [ 'shape' => 'Integer', ], 'ManagedScalingPolicy' => [ 'shape' => 'ManagedScalingPolicy', ], 'PlacementGroupConfigs' => [ 'shape' => 'PlacementGroupConfigList', ], 'AutoTerminationPolicy' => [ 'shape' => 'AutoTerminationPolicy', ], 'OSReleaseLabel' => [ 'shape' => 'XmlStringMaxLen256', ], 'EbsRootVolumeIops' => [ 'shape' => 'Integer', ], 'EbsRootVolumeThroughput' => [ 'shape' => 'Integer', ], ], ], 'RunJobFlowOutput' => [ 'type' => 'structure', 'members' => [ 'JobFlowId' => [ 'shape' => 'XmlStringMaxLen256', ], 'ClusterArn' => [ 'shape' => 'ArnType', ], ], ], 'ScaleDownBehavior' => [ 'type' => 'string', 'enum' => [ 'TERMINATE_AT_INSTANCE_HOUR', 'TERMINATE_AT_TASK_COMPLETION', ], ], 'ScalingAction' => [ 'type' => 'structure', 'required' => [ 'SimpleScalingPolicyConfiguration', ], 'members' => [ 'Market' => [ 'shape' => 'MarketType', ], 'SimpleScalingPolicyConfiguration' => [ 'shape' => 'SimpleScalingPolicyConfiguration', ], ], ], 'ScalingConstraints' => [ 'type' => 'structure', 'required' => [ 'MinCapacity', 'MaxCapacity', ], 'members' => [ 'MinCapacity' => [ 'shape' => 'Integer', ], 'MaxCapacity' => [ 'shape' => 'Integer', ], ], ], 'ScalingRule' => [ 'type' => 'structure', 'required' => [ 'Name', 'Action', 'Trigger', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'Action' => [ 'shape' => 'ScalingAction', ], 'Trigger' => [ 'shape' => 'ScalingTrigger', ], ], ], 'ScalingRuleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScalingRule', ], ], 'ScalingTrigger' => [ 'type' => 'structure', 'required' => [ 'CloudWatchAlarmDefinition', ], 'members' => [ 'CloudWatchAlarmDefinition' => [ 'shape' => 'CloudWatchAlarmDefinition', ], ], ], 'ScriptBootstrapActionConfig' => [ 'type' => 'structure', 'required' => [ 'Path', ], 'members' => [ 'Path' => [ 'shape' => 'XmlString', ], 'Args' => [ 'shape' => 'XmlStringList', ], ], ], 'SecurityConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityConfigurationSummary', ], ], 'SecurityConfigurationSummary' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'XmlString', ], 'CreationDateTime' => [ 'shape' => 'Date', ], ], ], 'SecurityGroupsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'XmlStringMaxLen256', ], ], 'SessionMappingDetail' => [ 'type' => 'structure', 'members' => [ 'StudioId' => [ 'shape' => 'XmlStringMaxLen256', ], 'IdentityId' => [ 'shape' => 'XmlStringMaxLen256', ], 'IdentityName' => [ 'shape' => 'XmlStringMaxLen256', ], 'IdentityType' => [ 'shape' => 'IdentityType', ], 'SessionPolicyArn' => [ 'shape' => 'XmlStringMaxLen256', ], 'CreationTime' => [ 'shape' => 'Date', ], 'LastModifiedTime' => [ 'shape' => 'Date', ], ], ], 'SessionMappingSummary' => [ 'type' => 'structure', 'members' => [ 'StudioId' => [ 'shape' => 'XmlStringMaxLen256', ], 'IdentityId' => [ 'shape' => 'XmlStringMaxLen256', ], 'IdentityName' => [ 'shape' => 'XmlStringMaxLen256', ], 'IdentityType' => [ 'shape' => 'IdentityType', ], 'SessionPolicyArn' => [ 'shape' => 'XmlStringMaxLen256', ], 'CreationTime' => [ 'shape' => 'Date', ], ], ], 'SessionMappingSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SessionMappingSummary', ], ], 'SetKeepJobFlowAliveWhenNoStepsInput' => [ 'type' => 'structure', 'required' => [ 'JobFlowIds', 'KeepJobFlowAliveWhenNoSteps', ], 'members' => [ 'JobFlowIds' => [ 'shape' => 'XmlStringList', ], 'KeepJobFlowAliveWhenNoSteps' => [ 'shape' => 'Boolean', ], ], ], 'SetTerminationProtectionInput' => [ 'type' => 'structure', 'required' => [ 'JobFlowIds', 'TerminationProtected', ], 'members' => [ 'JobFlowIds' => [ 'shape' => 'XmlStringList', ], 'TerminationProtected' => [ 'shape' => 'Boolean', ], ], ], 'SetUnhealthyNodeReplacementInput' => [ 'type' => 'structure', 'required' => [ 'JobFlowIds', 'UnhealthyNodeReplacement', ], 'members' => [ 'JobFlowIds' => [ 'shape' => 'XmlStringList', ], 'UnhealthyNodeReplacement' => [ 'shape' => 'BooleanObject', ], ], ], 'SetVisibleToAllUsersInput' => [ 'type' => 'structure', 'required' => [ 'JobFlowIds', 'VisibleToAllUsers', ], 'members' => [ 'JobFlowIds' => [ 'shape' => 'XmlStringList', ], 'VisibleToAllUsers' => [ 'shape' => 'Boolean', ], ], ], 'ShrinkPolicy' => [ 'type' => 'structure', 'members' => [ 'DecommissionTimeout' => [ 'shape' => 'Integer', ], 'InstanceResizePolicy' => [ 'shape' => 'InstanceResizePolicy', ], ], ], 'SimpleScalingPolicyConfiguration' => [ 'type' => 'structure', 'required' => [ 'ScalingAdjustment', ], 'members' => [ 'AdjustmentType' => [ 'shape' => 'AdjustmentType', ], 'ScalingAdjustment' => [ 'shape' => 'Integer', ], 'CoolDown' => [ 'shape' => 'Integer', ], ], ], 'SimplifiedApplication' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Version' => [ 'shape' => 'String', ], ], ], 'SimplifiedApplicationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SimplifiedApplication', ], ], 'SpotProvisioningAllocationStrategy' => [ 'type' => 'string', 'enum' => [ 'capacity-optimized', 'price-capacity-optimized', 'lowest-price', 'diversified', 'capacity-optimized-prioritized', ], ], 'SpotProvisioningSpecification' => [ 'type' => 'structure', 'required' => [ 'TimeoutDurationMinutes', 'TimeoutAction', ], 'members' => [ 'TimeoutDurationMinutes' => [ 'shape' => 'WholeNumber', ], 'TimeoutAction' => [ 'shape' => 'SpotProvisioningTimeoutAction', ], 'BlockDurationMinutes' => [ 'shape' => 'WholeNumber', ], 'AllocationStrategy' => [ 'shape' => 'SpotProvisioningAllocationStrategy', ], ], ], 'SpotProvisioningTimeoutAction' => [ 'type' => 'string', 'enum' => [ 'SWITCH_TO_ON_DEMAND', 'TERMINATE_CLUSTER', ], ], 'SpotResizingSpecification' => [ 'type' => 'structure', 'members' => [ 'TimeoutDurationMinutes' => [ 'shape' => 'WholeNumber', ], 'AllocationStrategy' => [ 'shape' => 'SpotProvisioningAllocationStrategy', ], ], ], 'StartNotebookExecutionInput' => [ 'type' => 'structure', 'required' => [ 'ExecutionEngine', 'ServiceRole', ], 'members' => [ 'EditorId' => [ 'shape' => 'XmlStringMaxLen256', ], 'RelativePath' => [ 'shape' => 'XmlString', ], 'NotebookExecutionName' => [ 'shape' => 'XmlStringMaxLen256', ], 'NotebookParams' => [ 'shape' => 'XmlString', ], 'ExecutionEngine' => [ 'shape' => 'ExecutionEngineConfig', ], 'ServiceRole' => [ 'shape' => 'XmlString', ], 'NotebookInstanceSecurityGroupId' => [ 'shape' => 'XmlStringMaxLen256', ], 'Tags' => [ 'shape' => 'TagList', ], 'NotebookS3Location' => [ 'shape' => 'NotebookS3LocationFromInput', ], 'OutputNotebookS3Location' => [ 'shape' => 'OutputNotebookS3LocationFromInput', ], 'OutputNotebookFormat' => [ 'shape' => 'OutputNotebookFormat', ], 'EnvironmentVariables' => [ 'shape' => 'EnvironmentVariablesMap', ], ], ], 'StartNotebookExecutionOutput' => [ 'type' => 'structure', 'members' => [ 'NotebookExecutionId' => [ 'shape' => 'XmlStringMaxLen256', ], ], ], 'Statistic' => [ 'type' => 'string', 'enum' => [ 'SAMPLE_COUNT', 'AVERAGE', 'SUM', 'MINIMUM', 'MAXIMUM', ], ], 'Step' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'StepId', ], 'Name' => [ 'shape' => 'String', ], 'Config' => [ 'shape' => 'HadoopStepConfig', ], 'ActionOnFailure' => [ 'shape' => 'ActionOnFailure', ], 'Status' => [ 'shape' => 'StepStatus', ], 'ExecutionRoleArn' => [ 'shape' => 'OptionalArnType', ], ], ], 'StepCancellationOption' => [ 'type' => 'string', 'enum' => [ 'SEND_INTERRUPT', 'TERMINATE_PROCESS', ], ], 'StepConfig' => [ 'type' => 'structure', 'required' => [ 'Name', 'HadoopJarStep', ], 'members' => [ 'Name' => [ 'shape' => 'XmlStringMaxLen256', ], 'ActionOnFailure' => [ 'shape' => 'ActionOnFailure', ], 'HadoopJarStep' => [ 'shape' => 'HadoopJarStepConfig', ], ], ], 'StepConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StepConfig', ], ], 'StepDetail' => [ 'type' => 'structure', 'required' => [ 'StepConfig', 'ExecutionStatusDetail', ], 'members' => [ 'StepConfig' => [ 'shape' => 'StepConfig', ], 'ExecutionStatusDetail' => [ 'shape' => 'StepExecutionStatusDetail', ], ], ], 'StepDetailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StepDetail', ], ], 'StepExecutionState' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'RUNNING', 'CONTINUE', 'COMPLETED', 'CANCELLED', 'FAILED', 'INTERRUPTED', ], ], 'StepExecutionStatusDetail' => [ 'type' => 'structure', 'required' => [ 'State', 'CreationDateTime', ], 'members' => [ 'State' => [ 'shape' => 'StepExecutionState', ], 'CreationDateTime' => [ 'shape' => 'Date', ], 'StartDateTime' => [ 'shape' => 'Date', ], 'EndDateTime' => [ 'shape' => 'Date', ], 'LastStateChangeReason' => [ 'shape' => 'XmlString', ], ], ], 'StepId' => [ 'type' => 'string', ], 'StepIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'XmlStringMaxLen256', ], ], 'StepState' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'CANCEL_PENDING', 'RUNNING', 'COMPLETED', 'CANCELLED', 'FAILED', 'INTERRUPTED', ], ], 'StepStateChangeReason' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'StepStateChangeReasonCode', ], 'Message' => [ 'shape' => 'String', ], ], ], 'StepStateChangeReasonCode' => [ 'type' => 'string', 'enum' => [ 'NONE', ], ], 'StepStateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StepState', ], ], 'StepStatus' => [ 'type' => 'structure', 'members' => [ 'State' => [ 'shape' => 'StepState', ], 'StateChangeReason' => [ 'shape' => 'StepStateChangeReason', ], 'FailureDetails' => [ 'shape' => 'FailureDetails', ], 'Timeline' => [ 'shape' => 'StepTimeline', ], ], ], 'StepSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'StepId', ], 'Name' => [ 'shape' => 'String', ], 'Config' => [ 'shape' => 'HadoopStepConfig', ], 'ActionOnFailure' => [ 'shape' => 'ActionOnFailure', ], 'Status' => [ 'shape' => 'StepStatus', ], ], ], 'StepSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StepSummary', ], ], 'StepTimeline' => [ 'type' => 'structure', 'members' => [ 'CreationDateTime' => [ 'shape' => 'Date', ], 'StartDateTime' => [ 'shape' => 'Date', ], 'EndDateTime' => [ 'shape' => 'Date', ], ], ], 'StopNotebookExecutionInput' => [ 'type' => 'structure', 'required' => [ 'NotebookExecutionId', ], 'members' => [ 'NotebookExecutionId' => [ 'shape' => 'XmlStringMaxLen256', ], ], ], 'String' => [ 'type' => 'string', ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'StringMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'Studio' => [ 'type' => 'structure', 'members' => [ 'StudioId' => [ 'shape' => 'XmlStringMaxLen256', ], 'StudioArn' => [ 'shape' => 'XmlStringMaxLen256', ], 'Name' => [ 'shape' => 'XmlStringMaxLen256', ], 'Description' => [ 'shape' => 'XmlStringMaxLen256', ], 'AuthMode' => [ 'shape' => 'AuthMode', ], 'VpcId' => [ 'shape' => 'XmlStringMaxLen256', ], 'SubnetIds' => [ 'shape' => 'SubnetIdList', ], 'ServiceRole' => [ 'shape' => 'XmlString', ], 'UserRole' => [ 'shape' => 'XmlString', ], 'WorkspaceSecurityGroupId' => [ 'shape' => 'XmlStringMaxLen256', ], 'EngineSecurityGroupId' => [ 'shape' => 'XmlStringMaxLen256', ], 'Url' => [ 'shape' => 'XmlString', ], 'CreationTime' => [ 'shape' => 'Date', ], 'DefaultS3Location' => [ 'shape' => 'XmlString', ], 'IdpAuthUrl' => [ 'shape' => 'XmlString', ], 'IdpRelayStateParameterName' => [ 'shape' => 'XmlStringMaxLen256', ], 'Tags' => [ 'shape' => 'TagList', ], 'IdcInstanceArn' => [ 'shape' => 'ArnType', ], 'TrustedIdentityPropagationEnabled' => [ 'shape' => 'BooleanObject', ], 'IdcUserAssignment' => [ 'shape' => 'IdcUserAssignment', ], 'EncryptionKeyArn' => [ 'shape' => 'XmlString', ], ], ], 'StudioSummary' => [ 'type' => 'structure', 'members' => [ 'StudioId' => [ 'shape' => 'XmlStringMaxLen256', ], 'Name' => [ 'shape' => 'XmlStringMaxLen256', ], 'VpcId' => [ 'shape' => 'XmlStringMaxLen256', ], 'Description' => [ 'shape' => 'XmlStringMaxLen256', ], 'Url' => [ 'shape' => 'XmlStringMaxLen256', ], 'AuthMode' => [ 'shape' => 'AuthMode', ], 'CreationTime' => [ 'shape' => 'Date', ], ], ], 'StudioSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StudioSummary', ], ], 'SubnetIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'SupportedInstanceType' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'MemoryGB' => [ 'shape' => 'Float', ], 'StorageGB' => [ 'shape' => 'Integer', ], 'VCPU' => [ 'shape' => 'Integer', ], 'Is64BitsOnly' => [ 'shape' => 'Boolean', ], 'InstanceFamilyId' => [ 'shape' => 'String', ], 'EbsOptimizedAvailable' => [ 'shape' => 'Boolean', ], 'EbsOptimizedByDefault' => [ 'shape' => 'Boolean', ], 'NumberOfDisks' => [ 'shape' => 'Integer', ], 'EbsStorageOnly' => [ 'shape' => 'Boolean', ], 'Architecture' => [ 'shape' => 'String', ], ], ], 'SupportedInstanceTypesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SupportedInstanceType', ], ], 'SupportedProductConfig' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'XmlStringMaxLen256', ], 'Args' => [ 'shape' => 'XmlStringList', ], ], ], 'SupportedProductsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'XmlStringMaxLen256', ], ], 'Tag' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'String', ], ], ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'TerminateJobFlowsInput' => [ 'type' => 'structure', 'required' => [ 'JobFlowIds', ], 'members' => [ 'JobFlowIds' => [ 'shape' => 'XmlStringList', ], ], ], 'ThroughputVal' => [ 'type' => 'integer', 'min' => 0, ], 'Unit' => [ 'type' => 'string', 'enum' => [ 'NONE', 'SECONDS', 'MICRO_SECONDS', 'MILLI_SECONDS', 'BYTES', 'KILO_BYTES', 'MEGA_BYTES', 'GIGA_BYTES', 'TERA_BYTES', 'BITS', 'KILO_BITS', 'MEGA_BITS', 'GIGA_BITS', 'TERA_BITS', 'PERCENT', 'COUNT', 'BYTES_PER_SECOND', 'KILO_BYTES_PER_SECOND', 'MEGA_BYTES_PER_SECOND', 'GIGA_BYTES_PER_SECOND', 'TERA_BYTES_PER_SECOND', 'BITS_PER_SECOND', 'KILO_BITS_PER_SECOND', 'MEGA_BITS_PER_SECOND', 'GIGA_BITS_PER_SECOND', 'TERA_BITS_PER_SECOND', 'COUNT_PER_SECOND', ], ], 'UpdateStudioInput' => [ 'type' => 'structure', 'required' => [ 'StudioId', ], 'members' => [ 'StudioId' => [ 'shape' => 'XmlStringMaxLen256', ], 'Name' => [ 'shape' => 'XmlStringMaxLen256', ], 'Description' => [ 'shape' => 'XmlStringMaxLen256', ], 'SubnetIds' => [ 'shape' => 'SubnetIdList', ], 'DefaultS3Location' => [ 'shape' => 'XmlString', ], 'EncryptionKeyArn' => [ 'shape' => 'XmlString', ], ], ], 'UpdateStudioSessionMappingInput' => [ 'type' => 'structure', 'required' => [ 'StudioId', 'IdentityType', 'SessionPolicyArn', ], 'members' => [ 'StudioId' => [ 'shape' => 'XmlStringMaxLen256', ], 'IdentityId' => [ 'shape' => 'XmlStringMaxLen256', ], 'IdentityName' => [ 'shape' => 'XmlStringMaxLen256', ], 'IdentityType' => [ 'shape' => 'IdentityType', ], 'SessionPolicyArn' => [ 'shape' => 'XmlStringMaxLen256', ], ], ], 'UriString' => [ 'type' => 'string', 'max' => 10280, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDBFF-\\uDC00\\uDFFF\\r\\n\\t]*', ], 'UsernamePassword' => [ 'type' => 'structure', 'members' => [ 'Username' => [ 'shape' => 'XmlStringMaxLen256', ], 'Password' => [ 'shape' => 'XmlStringMaxLen256', ], ], 'sensitive' => true, ], 'VolumeSpecification' => [ 'type' => 'structure', 'required' => [ 'VolumeType', 'SizeInGB', ], 'members' => [ 'VolumeType' => [ 'shape' => 'String', ], 'Iops' => [ 'shape' => 'Integer', ], 'SizeInGB' => [ 'shape' => 'Integer', ], 'Throughput' => [ 'shape' => 'ThroughputVal', ], ], ], 'WholeNumber' => [ 'type' => 'integer', 'min' => 0, ], 'XmlString' => [ 'type' => 'string', 'max' => 10280, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'XmlStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'XmlString', ], ], 'XmlStringMaxLen256' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'XmlStringMaxLen256List' => [ 'type' => 'list', 'member' => [ 'shape' => 'XmlStringMaxLen256', ], ], ],];
