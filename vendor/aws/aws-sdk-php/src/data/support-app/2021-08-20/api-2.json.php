<?php
// This file was auto-generated from sdk-root/src/data/support-app/2021-08-20/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2021-08-20', 'endpointPrefix' => 'supportapp', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceAbbreviation' => 'SupportApp', 'serviceFullName' => 'AWS Support App', 'serviceId' => 'Support App', 'signatureVersion' => 'v4', 'signingName' => 'supportapp', 'uid' => 'support-app-2021-08-20', ], 'operations' => [ 'CreateSlackChannelConfiguration' => [ 'name' => 'CreateSlackChannelConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/control/create-slack-channel-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateSlackChannelConfigurationRequest', ], 'output' => [ 'shape' => 'CreateSlackChannelConfigurationResult', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'DeleteAccountAlias' => [ 'name' => 'DeleteAccountAlias', 'http' => [ 'method' => 'POST', 'requestUri' => '/control/delete-account-alias', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteAccountAliasRequest', ], 'output' => [ 'shape' => 'DeleteAccountAliasResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteSlackChannelConfiguration' => [ 'name' => 'DeleteSlackChannelConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/control/delete-slack-channel-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteSlackChannelConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteSlackChannelConfigurationResult', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'DeleteSlackWorkspaceConfiguration' => [ 'name' => 'DeleteSlackWorkspaceConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/control/delete-slack-workspace-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteSlackWorkspaceConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteSlackWorkspaceConfigurationResult', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetAccountAlias' => [ 'name' => 'GetAccountAlias', 'http' => [ 'method' => 'POST', 'requestUri' => '/control/get-account-alias', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAccountAliasRequest', ], 'output' => [ 'shape' => 'GetAccountAliasResult', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], ], ], 'ListSlackChannelConfigurations' => [ 'name' => 'ListSlackChannelConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/control/list-slack-channel-configurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSlackChannelConfigurationsRequest', ], 'output' => [ 'shape' => 'ListSlackChannelConfigurationsResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListSlackWorkspaceConfigurations' => [ 'name' => 'ListSlackWorkspaceConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/control/list-slack-workspace-configurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSlackWorkspaceConfigurationsRequest', ], 'output' => [ 'shape' => 'ListSlackWorkspaceConfigurationsResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'PutAccountAlias' => [ 'name' => 'PutAccountAlias', 'http' => [ 'method' => 'POST', 'requestUri' => '/control/put-account-alias', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutAccountAliasRequest', ], 'output' => [ 'shape' => 'PutAccountAliasResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'RegisterSlackWorkspaceForOrganization' => [ 'name' => 'RegisterSlackWorkspaceForOrganization', 'http' => [ 'method' => 'POST', 'requestUri' => '/control/register-slack-workspace-for-organization', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RegisterSlackWorkspaceForOrganizationRequest', ], 'output' => [ 'shape' => 'RegisterSlackWorkspaceForOrganizationResult', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateSlackChannelConfiguration' => [ 'name' => 'UpdateSlackChannelConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/control/update-slack-channel-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateSlackChannelConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateSlackChannelConfigurationResult', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AccountType' => [ 'type' => 'string', 'enum' => [ 'management', 'member', ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateSlackChannelConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'channelId', 'channelRoleArn', 'notifyOnCaseSeverity', 'teamId', ], 'members' => [ 'channelId' => [ 'shape' => 'channelId', ], 'channelName' => [ 'shape' => 'channelName', ], 'channelRoleArn' => [ 'shape' => 'roleArn', ], 'notifyOnAddCorrespondenceToCase' => [ 'shape' => 'booleanValue', ], 'notifyOnCaseSeverity' => [ 'shape' => 'NotificationSeverityLevel', ], 'notifyOnCreateOrReopenCase' => [ 'shape' => 'booleanValue', ], 'notifyOnResolveCase' => [ 'shape' => 'booleanValue', ], 'teamId' => [ 'shape' => 'teamId', ], ], ], 'CreateSlackChannelConfigurationResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAccountAliasRequest' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAccountAliasResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSlackChannelConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'channelId', 'teamId', ], 'members' => [ 'channelId' => [ 'shape' => 'channelId', ], 'teamId' => [ 'shape' => 'teamId', ], ], ], 'DeleteSlackChannelConfigurationResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSlackWorkspaceConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'teamId', ], 'members' => [ 'teamId' => [ 'shape' => 'teamId', ], ], ], 'DeleteSlackWorkspaceConfigurationResult' => [ 'type' => 'structure', 'members' => [], ], 'GetAccountAliasRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetAccountAliasResult' => [ 'type' => 'structure', 'members' => [ 'accountAlias' => [ 'shape' => 'awsAccountAlias', ], ], ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'ListSlackChannelConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'paginationToken', ], ], ], 'ListSlackChannelConfigurationsResult' => [ 'type' => 'structure', 'required' => [ 'slackChannelConfigurations', ], 'members' => [ 'nextToken' => [ 'shape' => 'paginationToken', ], 'slackChannelConfigurations' => [ 'shape' => 'slackChannelConfigurationList', ], ], ], 'ListSlackWorkspaceConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'paginationToken', ], ], ], 'ListSlackWorkspaceConfigurationsResult' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'paginationToken', ], 'slackWorkspaceConfigurations' => [ 'shape' => 'SlackWorkspaceConfigurationList', ], ], ], 'NotificationSeverityLevel' => [ 'type' => 'string', 'enum' => [ 'none', 'all', 'high', ], ], 'PutAccountAliasRequest' => [ 'type' => 'structure', 'required' => [ 'accountAlias', ], 'members' => [ 'accountAlias' => [ 'shape' => 'awsAccountAlias', ], ], ], 'PutAccountAliasResult' => [ 'type' => 'structure', 'members' => [], ], 'RegisterSlackWorkspaceForOrganizationRequest' => [ 'type' => 'structure', 'required' => [ 'teamId', ], 'members' => [ 'teamId' => [ 'shape' => 'teamId', ], ], ], 'RegisterSlackWorkspaceForOrganizationResult' => [ 'type' => 'structure', 'members' => [ 'accountType' => [ 'shape' => 'AccountType', ], 'teamId' => [ 'shape' => 'teamId', ], 'teamName' => [ 'shape' => 'teamName', ], ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'SlackChannelConfiguration' => [ 'type' => 'structure', 'required' => [ 'channelId', 'teamId', ], 'members' => [ 'channelId' => [ 'shape' => 'channelId', ], 'channelName' => [ 'shape' => 'channelName', ], 'channelRoleArn' => [ 'shape' => 'roleArn', ], 'notifyOnAddCorrespondenceToCase' => [ 'shape' => 'booleanValue', ], 'notifyOnCaseSeverity' => [ 'shape' => 'NotificationSeverityLevel', ], 'notifyOnCreateOrReopenCase' => [ 'shape' => 'booleanValue', ], 'notifyOnResolveCase' => [ 'shape' => 'booleanValue', ], 'teamId' => [ 'shape' => 'teamId', ], ], ], 'SlackWorkspaceConfiguration' => [ 'type' => 'structure', 'required' => [ 'teamId', ], 'members' => [ 'allowOrganizationMemberAccount' => [ 'shape' => 'booleanValue', ], 'teamId' => [ 'shape' => 'teamId', ], 'teamName' => [ 'shape' => 'teamName', ], ], ], 'SlackWorkspaceConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SlackWorkspaceConfiguration', ], ], 'UpdateSlackChannelConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'channelId', 'teamId', ], 'members' => [ 'channelId' => [ 'shape' => 'channelId', ], 'channelName' => [ 'shape' => 'channelName', ], 'channelRoleArn' => [ 'shape' => 'roleArn', ], 'notifyOnAddCorrespondenceToCase' => [ 'shape' => 'booleanValue', ], 'notifyOnCaseSeverity' => [ 'shape' => 'NotificationSeverityLevel', ], 'notifyOnCreateOrReopenCase' => [ 'shape' => 'booleanValue', ], 'notifyOnResolveCase' => [ 'shape' => 'booleanValue', ], 'teamId' => [ 'shape' => 'teamId', ], ], ], 'UpdateSlackChannelConfigurationResult' => [ 'type' => 'structure', 'members' => [ 'channelId' => [ 'shape' => 'channelId', ], 'channelName' => [ 'shape' => 'channelName', ], 'channelRoleArn' => [ 'shape' => 'roleArn', ], 'notifyOnAddCorrespondenceToCase' => [ 'shape' => 'booleanValue', ], 'notifyOnCaseSeverity' => [ 'shape' => 'NotificationSeverityLevel', ], 'notifyOnCreateOrReopenCase' => [ 'shape' => 'booleanValue', ], 'notifyOnResolveCase' => [ 'shape' => 'booleanValue', ], 'teamId' => [ 'shape' => 'teamId', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'awsAccountAlias' => [ 'type' => 'string', 'max' => 30, 'min' => 1, 'pattern' => '^[\\w\\- ]+$', ], 'booleanValue' => [ 'type' => 'boolean', 'box' => true, ], 'channelId' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^\\S+$', ], 'channelName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^.+$', ], 'errorMessage' => [ 'type' => 'string', ], 'paginationToken' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^\\S+$', ], 'roleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 31, 'pattern' => '^arn:aws:iam::[0-9]{12}:role/(.+)$', ], 'slackChannelConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SlackChannelConfiguration', ], ], 'teamId' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^\\S+$', ], 'teamName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^.+$', ], ],];
