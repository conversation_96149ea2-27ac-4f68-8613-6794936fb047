<?php
// This file was auto-generated from sdk-root/src/data/appfabric/2023-05-19/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2023-05-19', 'endpointPrefix' => 'appfabric', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'AppFabric', 'serviceId' => 'AppFabric', 'signatureVersion' => 'v4', 'signingName' => 'appfabric', 'uid' => 'appfabric-2023-05-19', ], 'operations' => [ 'BatchGetUserAccessTasks' => [ 'name' => 'BatchGetUserAccessTasks', 'http' => [ 'method' => 'POST', 'requestUri' => '/useraccess/batchget', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchGetUserAccessTasksRequest', ], 'output' => [ 'shape' => 'BatchGetUserAccessTasksResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ConnectAppAuthorization' => [ 'name' => 'ConnectAppAuthorization', 'http' => [ 'method' => 'POST', 'requestUri' => '/appbundles/{appBundleIdentifier}/appauthorizations/{appAuthorizationIdentifier}/connect', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ConnectAppAuthorizationRequest', ], 'output' => [ 'shape' => 'ConnectAppAuthorizationResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateAppAuthorization' => [ 'name' => 'CreateAppAuthorization', 'http' => [ 'method' => 'POST', 'requestUri' => '/appbundles/{appBundleIdentifier}/appauthorizations', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateAppAuthorizationRequest', ], 'output' => [ 'shape' => 'CreateAppAuthorizationResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'CreateAppBundle' => [ 'name' => 'CreateAppBundle', 'http' => [ 'method' => 'POST', 'requestUri' => '/appbundles', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateAppBundleRequest', ], 'output' => [ 'shape' => 'CreateAppBundleResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'CreateIngestion' => [ 'name' => 'CreateIngestion', 'http' => [ 'method' => 'POST', 'requestUri' => '/appbundles/{appBundleIdentifier}/ingestions', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateIngestionRequest', ], 'output' => [ 'shape' => 'CreateIngestionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'CreateIngestionDestination' => [ 'name' => 'CreateIngestionDestination', 'http' => [ 'method' => 'POST', 'requestUri' => '/appbundles/{appBundleIdentifier}/ingestions/{ingestionIdentifier}/ingestiondestinations', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateIngestionDestinationRequest', ], 'output' => [ 'shape' => 'CreateIngestionDestinationResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteAppAuthorization' => [ 'name' => 'DeleteAppAuthorization', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/appbundles/{appBundleIdentifier}/appauthorizations/{appAuthorizationIdentifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteAppAuthorizationRequest', ], 'output' => [ 'shape' => 'DeleteAppAuthorizationResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteAppBundle' => [ 'name' => 'DeleteAppBundle', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/appbundles/{appBundleIdentifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteAppBundleRequest', ], 'output' => [ 'shape' => 'DeleteAppBundleResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteIngestion' => [ 'name' => 'DeleteIngestion', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/appbundles/{appBundleIdentifier}/ingestions/{ingestionIdentifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteIngestionRequest', ], 'output' => [ 'shape' => 'DeleteIngestionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteIngestionDestination' => [ 'name' => 'DeleteIngestionDestination', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/appbundles/{appBundleIdentifier}/ingestions/{ingestionIdentifier}/ingestiondestinations/{ingestionDestinationIdentifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteIngestionDestinationRequest', ], 'output' => [ 'shape' => 'DeleteIngestionDestinationResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'GetAppAuthorization' => [ 'name' => 'GetAppAuthorization', 'http' => [ 'method' => 'GET', 'requestUri' => '/appbundles/{appBundleIdentifier}/appauthorizations/{appAuthorizationIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAppAuthorizationRequest', ], 'output' => [ 'shape' => 'GetAppAuthorizationResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetAppBundle' => [ 'name' => 'GetAppBundle', 'http' => [ 'method' => 'GET', 'requestUri' => '/appbundles/{appBundleIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAppBundleRequest', ], 'output' => [ 'shape' => 'GetAppBundleResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetIngestion' => [ 'name' => 'GetIngestion', 'http' => [ 'method' => 'GET', 'requestUri' => '/appbundles/{appBundleIdentifier}/ingestions/{ingestionIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetIngestionRequest', ], 'output' => [ 'shape' => 'GetIngestionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetIngestionDestination' => [ 'name' => 'GetIngestionDestination', 'http' => [ 'method' => 'GET', 'requestUri' => '/appbundles/{appBundleIdentifier}/ingestions/{ingestionIdentifier}/ingestiondestinations/{ingestionDestinationIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetIngestionDestinationRequest', ], 'output' => [ 'shape' => 'GetIngestionDestinationResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAppAuthorizations' => [ 'name' => 'ListAppAuthorizations', 'http' => [ 'method' => 'GET', 'requestUri' => '/appbundles/{appBundleIdentifier}/appauthorizations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAppAuthorizationsRequest', ], 'output' => [ 'shape' => 'ListAppAuthorizationsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAppBundles' => [ 'name' => 'ListAppBundles', 'http' => [ 'method' => 'GET', 'requestUri' => '/appbundles', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAppBundlesRequest', ], 'output' => [ 'shape' => 'ListAppBundlesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListIngestionDestinations' => [ 'name' => 'ListIngestionDestinations', 'http' => [ 'method' => 'GET', 'requestUri' => '/appbundles/{appBundleIdentifier}/ingestions/{ingestionIdentifier}/ingestiondestinations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListIngestionDestinationsRequest', ], 'output' => [ 'shape' => 'ListIngestionDestinationsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListIngestions' => [ 'name' => 'ListIngestions', 'http' => [ 'method' => 'GET', 'requestUri' => '/appbundles/{appBundleIdentifier}/ingestions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListIngestionsRequest', ], 'output' => [ 'shape' => 'ListIngestionsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'StartIngestion' => [ 'name' => 'StartIngestion', 'http' => [ 'method' => 'POST', 'requestUri' => '/appbundles/{appBundleIdentifier}/ingestions/{ingestionIdentifier}/start', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartIngestionRequest', ], 'output' => [ 'shape' => 'StartIngestionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'StartUserAccessTasks' => [ 'name' => 'StartUserAccessTasks', 'http' => [ 'method' => 'POST', 'requestUri' => '/useraccess/start', 'responseCode' => 201, ], 'input' => [ 'shape' => 'StartUserAccessTasksRequest', ], 'output' => [ 'shape' => 'StartUserAccessTasksResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'StopIngestion' => [ 'name' => 'StopIngestion', 'http' => [ 'method' => 'POST', 'requestUri' => '/appbundles/{appBundleIdentifier}/ingestions/{ingestionIdentifier}/stop', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopIngestionRequest', ], 'output' => [ 'shape' => 'StopIngestionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'UpdateAppAuthorization' => [ 'name' => 'UpdateAppAuthorization', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/appbundles/{appBundleIdentifier}/appauthorizations/{appAuthorizationIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAppAuthorizationRequest', ], 'output' => [ 'shape' => 'UpdateAppAuthorizationResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateIngestionDestination' => [ 'name' => 'UpdateIngestionDestination', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/appbundles/{appBundleIdentifier}/ingestions/{ingestionIdentifier}/ingestiondestinations/{ingestionDestinationIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateIngestionDestinationRequest', ], 'output' => [ 'shape' => 'UpdateIngestionDestinationResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'ApiKeyCredential' => [ 'type' => 'structure', 'required' => [ 'apiKey', ], 'members' => [ 'apiKey' => [ 'shape' => 'SensitiveString2048', ], ], ], 'AppAuthorization' => [ 'type' => 'structure', 'required' => [ 'appAuthorizationArn', 'appBundleArn', 'app', 'tenant', 'authType', 'status', 'createdAt', 'updatedAt', ], 'members' => [ 'appAuthorizationArn' => [ 'shape' => 'Arn', ], 'appBundleArn' => [ 'shape' => 'Arn', ], 'app' => [ 'shape' => 'String255', ], 'tenant' => [ 'shape' => 'Tenant', ], 'authType' => [ 'shape' => 'AuthType', ], 'status' => [ 'shape' => 'AppAuthorizationStatus', ], 'createdAt' => [ 'shape' => 'DateTime', ], 'updatedAt' => [ 'shape' => 'DateTime', ], 'persona' => [ 'shape' => 'Persona', ], 'authUrl' => [ 'shape' => 'String', ], ], ], 'AppAuthorizationStatus' => [ 'type' => 'string', 'enum' => [ 'PendingConnect', 'Connected', 'ConnectionValidationFailed', 'TokenAutoRotationFailed', ], ], 'AppAuthorizationSummary' => [ 'type' => 'structure', 'required' => [ 'appAuthorizationArn', 'appBundleArn', 'app', 'tenant', 'status', 'updatedAt', ], 'members' => [ 'appAuthorizationArn' => [ 'shape' => 'Arn', ], 'appBundleArn' => [ 'shape' => 'Arn', ], 'app' => [ 'shape' => 'String255', ], 'tenant' => [ 'shape' => 'Tenant', ], 'status' => [ 'shape' => 'AppAuthorizationStatus', ], 'updatedAt' => [ 'shape' => 'DateTime', ], ], ], 'AppAuthorizationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AppAuthorizationSummary', ], ], 'AppBundle' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'customerManagedKeyArn' => [ 'shape' => 'Arn', ], ], ], 'AppBundleSummary' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', ], ], ], 'AppBundleSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AppBundleSummary', ], ], 'Arn' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, 'pattern' => 'arn:.+', ], 'AuditLogDestinationConfiguration' => [ 'type' => 'structure', 'required' => [ 'destination', ], 'members' => [ 'destination' => [ 'shape' => 'Destination', ], ], ], 'AuditLogProcessingConfiguration' => [ 'type' => 'structure', 'required' => [ 'schema', 'format', ], 'members' => [ 'schema' => [ 'shape' => 'Schema', ], 'format' => [ 'shape' => 'Format', ], ], ], 'AuthRequest' => [ 'type' => 'structure', 'required' => [ 'redirectUri', 'code', ], 'members' => [ 'redirectUri' => [ 'shape' => 'RedirectUri', ], 'code' => [ 'shape' => 'SensitiveString2048', ], ], ], 'AuthType' => [ 'type' => 'string', 'enum' => [ 'oauth2', 'apiKey', ], ], 'BatchGetUserAccessTasksRequest' => [ 'type' => 'structure', 'required' => [ 'appBundleIdentifier', 'taskIdList', ], 'members' => [ 'appBundleIdentifier' => [ 'shape' => 'Identifier', ], 'taskIdList' => [ 'shape' => 'TaskIdList', ], ], ], 'BatchGetUserAccessTasksResponse' => [ 'type' => 'structure', 'members' => [ 'userAccessResultsList' => [ 'shape' => 'UserAccessResultsList', ], ], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ConnectAppAuthorizationRequest' => [ 'type' => 'structure', 'required' => [ 'appBundleIdentifier', 'appAuthorizationIdentifier', ], 'members' => [ 'appBundleIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'appBundleIdentifier', ], 'appAuthorizationIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'appAuthorizationIdentifier', ], 'authRequest' => [ 'shape' => 'AuthRequest', ], ], ], 'ConnectAppAuthorizationResponse' => [ 'type' => 'structure', 'required' => [ 'appAuthorizationSummary', ], 'members' => [ 'appAuthorizationSummary' => [ 'shape' => 'AppAuthorizationSummary', ], ], ], 'CreateAppAuthorizationRequest' => [ 'type' => 'structure', 'required' => [ 'appBundleIdentifier', 'app', 'credential', 'tenant', 'authType', ], 'members' => [ 'appBundleIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'appBundleIdentifier', ], 'app' => [ 'shape' => 'String255', ], 'credential' => [ 'shape' => 'Credential', ], 'tenant' => [ 'shape' => 'Tenant', ], 'authType' => [ 'shape' => 'AuthType', ], 'clientToken' => [ 'shape' => 'UUID', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateAppAuthorizationResponse' => [ 'type' => 'structure', 'required' => [ 'appAuthorization', ], 'members' => [ 'appAuthorization' => [ 'shape' => 'AppAuthorization', ], ], ], 'CreateAppBundleRequest' => [ 'type' => 'structure', 'members' => [ 'clientToken' => [ 'shape' => 'UUID', 'idempotencyToken' => true, ], 'customerManagedKeyIdentifier' => [ 'shape' => 'Identifier', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateAppBundleResponse' => [ 'type' => 'structure', 'required' => [ 'appBundle', ], 'members' => [ 'appBundle' => [ 'shape' => 'AppBundle', ], ], ], 'CreateIngestionDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'appBundleIdentifier', 'ingestionIdentifier', 'processingConfiguration', 'destinationConfiguration', ], 'members' => [ 'appBundleIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'appBundleIdentifier', ], 'ingestionIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'ingestionIdentifier', ], 'processingConfiguration' => [ 'shape' => 'ProcessingConfiguration', ], 'destinationConfiguration' => [ 'shape' => 'DestinationConfiguration', ], 'clientToken' => [ 'shape' => 'UUID', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateIngestionDestinationResponse' => [ 'type' => 'structure', 'required' => [ 'ingestionDestination', ], 'members' => [ 'ingestionDestination' => [ 'shape' => 'IngestionDestination', ], ], ], 'CreateIngestionRequest' => [ 'type' => 'structure', 'required' => [ 'appBundleIdentifier', 'app', 'tenantId', 'ingestionType', ], 'members' => [ 'appBundleIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'appBundleIdentifier', ], 'app' => [ 'shape' => 'String255', ], 'tenantId' => [ 'shape' => 'TenantIdentifier', ], 'ingestionType' => [ 'shape' => 'IngestionType', ], 'clientToken' => [ 'shape' => 'UUID', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateIngestionResponse' => [ 'type' => 'structure', 'required' => [ 'ingestion', ], 'members' => [ 'ingestion' => [ 'shape' => 'Ingestion', ], ], ], 'Credential' => [ 'type' => 'structure', 'members' => [ 'oauth2Credential' => [ 'shape' => 'Oauth2Credential', ], 'apiKeyCredential' => [ 'shape' => 'ApiKeyCredential', ], ], 'union' => true, ], 'DateTime' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'DeleteAppAuthorizationRequest' => [ 'type' => 'structure', 'required' => [ 'appBundleIdentifier', 'appAuthorizationIdentifier', ], 'members' => [ 'appBundleIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'appBundleIdentifier', ], 'appAuthorizationIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'appAuthorizationIdentifier', ], ], ], 'DeleteAppAuthorizationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAppBundleRequest' => [ 'type' => 'structure', 'required' => [ 'appBundleIdentifier', ], 'members' => [ 'appBundleIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'appBundleIdentifier', ], ], ], 'DeleteAppBundleResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteIngestionDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'appBundleIdentifier', 'ingestionIdentifier', 'ingestionDestinationIdentifier', ], 'members' => [ 'appBundleIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'appBundleIdentifier', ], 'ingestionIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'ingestionIdentifier', ], 'ingestionDestinationIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'ingestionDestinationIdentifier', ], ], ], 'DeleteIngestionDestinationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteIngestionRequest' => [ 'type' => 'structure', 'required' => [ 'appBundleIdentifier', 'ingestionIdentifier', ], 'members' => [ 'appBundleIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'appBundleIdentifier', ], 'ingestionIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'ingestionIdentifier', ], ], ], 'DeleteIngestionResponse' => [ 'type' => 'structure', 'members' => [], ], 'Destination' => [ 'type' => 'structure', 'members' => [ 's3Bucket' => [ 'shape' => 'S3Bucket', ], 'firehoseStream' => [ 'shape' => 'FirehoseStream', ], ], 'union' => true, ], 'DestinationConfiguration' => [ 'type' => 'structure', 'members' => [ 'auditLog' => [ 'shape' => 'AuditLogDestinationConfiguration', ], ], 'union' => true, ], 'Email' => [ 'type' => 'string', 'max' => 320, 'min' => 0, 'pattern' => '[a-zA-Z0-9.!#$%&’*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\\.[a-zA-Z0-9-]+)*', 'sensitive' => true, ], 'FirehoseStream' => [ 'type' => 'structure', 'required' => [ 'streamName', ], 'members' => [ 'streamName' => [ 'shape' => 'String64', ], ], ], 'Format' => [ 'type' => 'string', 'enum' => [ 'json', 'parquet', ], ], 'GetAppAuthorizationRequest' => [ 'type' => 'structure', 'required' => [ 'appBundleIdentifier', 'appAuthorizationIdentifier', ], 'members' => [ 'appBundleIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'appBundleIdentifier', ], 'appAuthorizationIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'appAuthorizationIdentifier', ], ], ], 'GetAppAuthorizationResponse' => [ 'type' => 'structure', 'required' => [ 'appAuthorization', ], 'members' => [ 'appAuthorization' => [ 'shape' => 'AppAuthorization', ], ], ], 'GetAppBundleRequest' => [ 'type' => 'structure', 'required' => [ 'appBundleIdentifier', ], 'members' => [ 'appBundleIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'appBundleIdentifier', ], ], ], 'GetAppBundleResponse' => [ 'type' => 'structure', 'required' => [ 'appBundle', ], 'members' => [ 'appBundle' => [ 'shape' => 'AppBundle', ], ], ], 'GetIngestionDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'appBundleIdentifier', 'ingestionIdentifier', 'ingestionDestinationIdentifier', ], 'members' => [ 'appBundleIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'appBundleIdentifier', ], 'ingestionIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'ingestionIdentifier', ], 'ingestionDestinationIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'ingestionDestinationIdentifier', ], ], ], 'GetIngestionDestinationResponse' => [ 'type' => 'structure', 'required' => [ 'ingestionDestination', ], 'members' => [ 'ingestionDestination' => [ 'shape' => 'IngestionDestination', ], ], ], 'GetIngestionRequest' => [ 'type' => 'structure', 'required' => [ 'appBundleIdentifier', 'ingestionIdentifier', ], 'members' => [ 'appBundleIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'appBundleIdentifier', ], 'ingestionIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'ingestionIdentifier', ], ], ], 'GetIngestionResponse' => [ 'type' => 'structure', 'required' => [ 'ingestion', ], 'members' => [ 'ingestion' => [ 'shape' => 'Ingestion', ], ], ], 'Identifier' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, 'pattern' => 'arn:.+$|^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}', ], 'Ingestion' => [ 'type' => 'structure', 'required' => [ 'arn', 'appBundleArn', 'app', 'tenantId', 'createdAt', 'updatedAt', 'state', 'ingestionType', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'appBundleArn' => [ 'shape' => 'Arn', ], 'app' => [ 'shape' => 'String255', ], 'tenantId' => [ 'shape' => 'TenantIdentifier', ], 'createdAt' => [ 'shape' => 'DateTime', ], 'updatedAt' => [ 'shape' => 'DateTime', ], 'state' => [ 'shape' => 'IngestionState', ], 'ingestionType' => [ 'shape' => 'IngestionType', ], ], ], 'IngestionDestination' => [ 'type' => 'structure', 'required' => [ 'arn', 'ingestionArn', 'processingConfiguration', 'destinationConfiguration', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'ingestionArn' => [ 'shape' => 'Arn', ], 'processingConfiguration' => [ 'shape' => 'ProcessingConfiguration', ], 'destinationConfiguration' => [ 'shape' => 'DestinationConfiguration', ], 'status' => [ 'shape' => 'IngestionDestinationStatus', ], 'statusReason' => [ 'shape' => 'String', ], 'createdAt' => [ 'shape' => 'DateTime', ], 'updatedAt' => [ 'shape' => 'DateTime', ], ], ], 'IngestionDestinationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IngestionDestinationSummary', ], ], 'IngestionDestinationStatus' => [ 'type' => 'string', 'enum' => [ 'Active', 'Failed', ], ], 'IngestionDestinationSummary' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', ], ], ], 'IngestionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IngestionSummary', ], ], 'IngestionState' => [ 'type' => 'string', 'enum' => [ 'enabled', 'disabled', ], ], 'IngestionSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'app', 'tenantId', 'state', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'app' => [ 'shape' => 'String255', ], 'tenantId' => [ 'shape' => 'TenantIdentifier', ], 'state' => [ 'shape' => 'IngestionState', ], ], ], 'IngestionType' => [ 'type' => 'string', 'enum' => [ 'auditLog', ], ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'ListAppAuthorizationsRequest' => [ 'type' => 'structure', 'required' => [ 'appBundleIdentifier', ], 'members' => [ 'appBundleIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'appBundleIdentifier', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String2048', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListAppAuthorizationsResponse' => [ 'type' => 'structure', 'required' => [ 'appAuthorizationSummaryList', ], 'members' => [ 'appAuthorizationSummaryList' => [ 'shape' => 'AppAuthorizationSummaryList', ], 'nextToken' => [ 'shape' => 'String2048', ], ], ], 'ListAppBundlesRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String2048', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListAppBundlesResponse' => [ 'type' => 'structure', 'required' => [ 'appBundleSummaryList', ], 'members' => [ 'appBundleSummaryList' => [ 'shape' => 'AppBundleSummaryList', ], 'nextToken' => [ 'shape' => 'String2048', ], ], ], 'ListIngestionDestinationsRequest' => [ 'type' => 'structure', 'required' => [ 'appBundleIdentifier', 'ingestionIdentifier', ], 'members' => [ 'appBundleIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'appBundleIdentifier', ], 'ingestionIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'ingestionIdentifier', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListIngestionDestinationsResponse' => [ 'type' => 'structure', 'required' => [ 'ingestionDestinations', ], 'members' => [ 'ingestionDestinations' => [ 'shape' => 'IngestionDestinationList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListIngestionsRequest' => [ 'type' => 'structure', 'required' => [ 'appBundleIdentifier', ], 'members' => [ 'appBundleIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'appBundleIdentifier', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListIngestionsResponse' => [ 'type' => 'structure', 'required' => [ 'ingestions', ], 'members' => [ 'ingestions' => [ 'shape' => 'IngestionList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagList', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'Oauth2Credential' => [ 'type' => 'structure', 'required' => [ 'clientId', 'clientSecret', ], 'members' => [ 'clientId' => [ 'shape' => 'String2048', ], 'clientSecret' => [ 'shape' => 'SensitiveString2048', ], ], ], 'Persona' => [ 'type' => 'string', 'enum' => [ 'admin', 'endUser', ], ], 'ProcessingConfiguration' => [ 'type' => 'structure', 'members' => [ 'auditLog' => [ 'shape' => 'AuditLogProcessingConfiguration', ], ], 'union' => true, ], 'RedirectUri' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => 'https://[-a-zA-Z0-9-._~:/?#@!$&\'()*+,;=]+', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResultStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'COMPLETED', 'FAILED', 'EXPIRED', ], ], 'S3Bucket' => [ 'type' => 'structure', 'required' => [ 'bucketName', ], 'members' => [ 'bucketName' => [ 'shape' => 'String63', ], 'prefix' => [ 'shape' => 'String120', ], ], ], 'Schema' => [ 'type' => 'string', 'enum' => [ 'ocsf', 'raw', ], ], 'SensitiveString2048' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'sensitive' => true, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', 'serviceCode', 'quotaCode', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], 'serviceCode' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'StartIngestionRequest' => [ 'type' => 'structure', 'required' => [ 'ingestionIdentifier', 'appBundleIdentifier', ], 'members' => [ 'ingestionIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'ingestionIdentifier', ], 'appBundleIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'appBundleIdentifier', ], ], ], 'StartIngestionResponse' => [ 'type' => 'structure', 'members' => [], ], 'StartUserAccessTasksRequest' => [ 'type' => 'structure', 'required' => [ 'appBundleIdentifier', 'email', ], 'members' => [ 'appBundleIdentifier' => [ 'shape' => 'Identifier', ], 'email' => [ 'shape' => 'Email', ], ], ], 'StartUserAccessTasksResponse' => [ 'type' => 'structure', 'members' => [ 'userAccessTasksList' => [ 'shape' => 'UserAccessTasksList', ], ], ], 'StopIngestionRequest' => [ 'type' => 'structure', 'required' => [ 'ingestionIdentifier', 'appBundleIdentifier', ], 'members' => [ 'ingestionIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'ingestionIdentifier', ], 'appBundleIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'appBundleIdentifier', ], ], ], 'StopIngestionResponse' => [ 'type' => 'structure', 'members' => [], ], 'String' => [ 'type' => 'string', ], 'String120' => [ 'type' => 'string', 'max' => 120, 'min' => 1, ], 'String2048' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'String255' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'String63' => [ 'type' => 'string', 'max' => 63, 'min' => 3, ], 'String64' => [ 'type' => 'string', 'max' => 64, 'min' => 3, ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'key', 'value', ], 'members' => [ 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 50, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TaskError' => [ 'type' => 'structure', 'members' => [ 'errorCode' => [ 'shape' => 'String', ], 'errorMessage' => [ 'shape' => 'String', ], ], ], 'TaskIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UUID', ], 'max' => 50, 'min' => 1, ], 'Tenant' => [ 'type' => 'structure', 'required' => [ 'tenantIdentifier', 'tenantDisplayName', ], 'members' => [ 'tenantIdentifier' => [ 'shape' => 'TenantIdentifier', ], 'tenantDisplayName' => [ 'shape' => 'String2048', ], ], ], 'TenantIdentifier' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'serviceCode' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'UUID' => [ 'type' => 'string', 'pattern' => '[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAppAuthorizationRequest' => [ 'type' => 'structure', 'required' => [ 'appBundleIdentifier', 'appAuthorizationIdentifier', ], 'members' => [ 'appBundleIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'appBundleIdentifier', ], 'appAuthorizationIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'appAuthorizationIdentifier', ], 'credential' => [ 'shape' => 'Credential', ], 'tenant' => [ 'shape' => 'Tenant', ], ], ], 'UpdateAppAuthorizationResponse' => [ 'type' => 'structure', 'required' => [ 'appAuthorization', ], 'members' => [ 'appAuthorization' => [ 'shape' => 'AppAuthorization', ], ], ], 'UpdateIngestionDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'appBundleIdentifier', 'ingestionIdentifier', 'ingestionDestinationIdentifier', 'destinationConfiguration', ], 'members' => [ 'appBundleIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'appBundleIdentifier', ], 'ingestionIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'ingestionIdentifier', ], 'ingestionDestinationIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'ingestionDestinationIdentifier', ], 'destinationConfiguration' => [ 'shape' => 'DestinationConfiguration', ], ], ], 'UpdateIngestionDestinationResponse' => [ 'type' => 'structure', 'required' => [ 'ingestionDestination', ], 'members' => [ 'ingestionDestination' => [ 'shape' => 'IngestionDestination', ], ], ], 'UserAccessResultItem' => [ 'type' => 'structure', 'members' => [ 'app' => [ 'shape' => 'String255', ], 'tenantId' => [ 'shape' => 'TenantIdentifier', ], 'tenantDisplayName' => [ 'shape' => 'String2048', ], 'taskId' => [ 'shape' => 'UUID', ], 'resultStatus' => [ 'shape' => 'ResultStatus', ], 'email' => [ 'shape' => 'Email', ], 'userId' => [ 'shape' => 'SensitiveString2048', ], 'userFullName' => [ 'shape' => 'SensitiveString2048', ], 'userFirstName' => [ 'shape' => 'SensitiveString2048', ], 'userLastName' => [ 'shape' => 'SensitiveString2048', ], 'userStatus' => [ 'shape' => 'String', ], 'taskError' => [ 'shape' => 'TaskError', ], ], ], 'UserAccessResultsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserAccessResultItem', ], ], 'UserAccessTaskItem' => [ 'type' => 'structure', 'required' => [ 'app', 'tenantId', ], 'members' => [ 'app' => [ 'shape' => 'String255', ], 'tenantId' => [ 'shape' => 'TenantIdentifier', ], 'taskId' => [ 'shape' => 'UUID', ], 'error' => [ 'shape' => 'TaskError', ], ], ], 'UserAccessTasksList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserAccessTaskItem', ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', 'reason', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], 'fieldList' => [ 'shape' => 'ValidationExceptionFieldList', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'name', 'message', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'unknownOperation', 'cannotParse', 'fieldValidationFailed', 'other', ], ], ],];
