<?php
// This file was auto-generated from sdk-root/src/data/qbusiness/2023-11-27/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2023-11-27', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'qbusiness', 'protocol' => 'rest-json', 'protocolSettings' => [ 'h2' => 'eventstream', ], 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'QBusiness', 'serviceId' => 'QBusiness', 'signatureVersion' => 'v4', 'signingName' => 'qbusiness', 'uid' => 'qbusiness-2023-11-27', ], 'operations' => [ 'BatchDeleteDocument' => [ 'name' => 'BatchDeleteDocument', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications/{applicationId}/indices/{indexId}/documents/delete', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchDeleteDocumentRequest', ], 'output' => [ 'shape' => 'BatchDeleteDocumentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'BatchPutDocument' => [ 'name' => 'BatchPutDocument', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications/{applicationId}/indices/{indexId}/documents', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchPutDocumentRequest', ], 'output' => [ 'shape' => 'BatchPutDocumentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'ChatSync' => [ 'name' => 'ChatSync', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications/{applicationId}/conversations?sync', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ChatSyncInput', ], 'output' => [ 'shape' => 'ChatSyncOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'LicenseNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateApplication' => [ 'name' => 'CreateApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateApplicationRequest', ], 'output' => [ 'shape' => 'CreateApplicationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateDataSource' => [ 'name' => 'CreateDataSource', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications/{applicationId}/indices/{indexId}/datasources', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateDataSourceRequest', ], 'output' => [ 'shape' => 'CreateDataSourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateIndex' => [ 'name' => 'CreateIndex', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications/{applicationId}/indices', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateIndexRequest', ], 'output' => [ 'shape' => 'CreateIndexResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'CreatePlugin' => [ 'name' => 'CreatePlugin', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications/{applicationId}/plugins', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreatePluginRequest', ], 'output' => [ 'shape' => 'CreatePluginResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateRetriever' => [ 'name' => 'CreateRetriever', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications/{applicationId}/retrievers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateRetrieverRequest', ], 'output' => [ 'shape' => 'CreateRetrieverResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'CreateUser' => [ 'name' => 'CreateUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications/{applicationId}/users', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateUserRequest', ], 'output' => [ 'shape' => 'CreateUserResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateWebExperience' => [ 'name' => 'CreateWebExperience', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications/{applicationId}/experiences', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateWebExperienceRequest', ], 'output' => [ 'shape' => 'CreateWebExperienceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'DeleteApplication' => [ 'name' => 'DeleteApplication', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/applications/{applicationId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteApplicationRequest', ], 'output' => [ 'shape' => 'DeleteApplicationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteChatControlsConfiguration' => [ 'name' => 'DeleteChatControlsConfiguration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/applications/{applicationId}/chatcontrols', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteChatControlsConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteChatControlsConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteConversation' => [ 'name' => 'DeleteConversation', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/applications/{applicationId}/conversations/{conversationId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteConversationRequest', ], 'output' => [ 'shape' => 'DeleteConversationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'LicenseNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteDataSource' => [ 'name' => 'DeleteDataSource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/applications/{applicationId}/indices/{indexId}/datasources/{dataSourceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteDataSourceRequest', ], 'output' => [ 'shape' => 'DeleteDataSourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteGroup' => [ 'name' => 'DeleteGroup', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/applications/{applicationId}/indices/{indexId}/groups/{groupName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteGroupRequest', ], 'output' => [ 'shape' => 'DeleteGroupResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteIndex' => [ 'name' => 'DeleteIndex', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/applications/{applicationId}/indices/{indexId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteIndexRequest', ], 'output' => [ 'shape' => 'DeleteIndexResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeletePlugin' => [ 'name' => 'DeletePlugin', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/applications/{applicationId}/plugins/{pluginId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeletePluginRequest', ], 'output' => [ 'shape' => 'DeletePluginResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteRetriever' => [ 'name' => 'DeleteRetriever', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/applications/{applicationId}/retrievers/{retrieverId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteRetrieverRequest', ], 'output' => [ 'shape' => 'DeleteRetrieverResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteUser' => [ 'name' => 'DeleteUser', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/applications/{applicationId}/users/{userId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteUserRequest', ], 'output' => [ 'shape' => 'DeleteUserResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteWebExperience' => [ 'name' => 'DeleteWebExperience', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/applications/{applicationId}/experiences/{webExperienceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteWebExperienceRequest', ], 'output' => [ 'shape' => 'DeleteWebExperienceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'GetApplication' => [ 'name' => 'GetApplication', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetApplicationRequest', ], 'output' => [ 'shape' => 'GetApplicationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetChatControlsConfiguration' => [ 'name' => 'GetChatControlsConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/chatcontrols', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetChatControlsConfigurationRequest', ], 'output' => [ 'shape' => 'GetChatControlsConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetDataSource' => [ 'name' => 'GetDataSource', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/indices/{indexId}/datasources/{dataSourceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDataSourceRequest', ], 'output' => [ 'shape' => 'GetDataSourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetGroup' => [ 'name' => 'GetGroup', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/indices/{indexId}/groups/{groupName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetGroupRequest', ], 'output' => [ 'shape' => 'GetGroupResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetIndex' => [ 'name' => 'GetIndex', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/indices/{indexId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetIndexRequest', ], 'output' => [ 'shape' => 'GetIndexResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetPlugin' => [ 'name' => 'GetPlugin', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/plugins/{pluginId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPluginRequest', ], 'output' => [ 'shape' => 'GetPluginResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetRetriever' => [ 'name' => 'GetRetriever', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/retrievers/{retrieverId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetRetrieverRequest', ], 'output' => [ 'shape' => 'GetRetrieverResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetUser' => [ 'name' => 'GetUser', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/users/{userId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetUserRequest', ], 'output' => [ 'shape' => 'GetUserResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetWebExperience' => [ 'name' => 'GetWebExperience', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/experiences/{webExperienceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetWebExperienceRequest', ], 'output' => [ 'shape' => 'GetWebExperienceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListApplications' => [ 'name' => 'ListApplications', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListApplicationsRequest', ], 'output' => [ 'shape' => 'ListApplicationsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListConversations' => [ 'name' => 'ListConversations', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/conversations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListConversationsRequest', ], 'output' => [ 'shape' => 'ListConversationsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'LicenseNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListDataSourceSyncJobs' => [ 'name' => 'ListDataSourceSyncJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/indices/{indexId}/datasources/{dataSourceId}/syncjobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDataSourceSyncJobsRequest', ], 'output' => [ 'shape' => 'ListDataSourceSyncJobsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListDataSources' => [ 'name' => 'ListDataSources', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/indices/{indexId}/datasources', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDataSourcesRequest', ], 'output' => [ 'shape' => 'ListDataSourcesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListDocuments' => [ 'name' => 'ListDocuments', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/index/{indexId}/documents', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDocumentsRequest', ], 'output' => [ 'shape' => 'ListDocumentsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListGroups' => [ 'name' => 'ListGroups', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/indices/{indexId}/groups', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListGroupsRequest', ], 'output' => [ 'shape' => 'ListGroupsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListIndices' => [ 'name' => 'ListIndices', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/indices', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListIndicesRequest', ], 'output' => [ 'shape' => 'ListIndicesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListMessages' => [ 'name' => 'ListMessages', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/conversations/{conversationId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMessagesRequest', ], 'output' => [ 'shape' => 'ListMessagesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'LicenseNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListPlugins' => [ 'name' => 'ListPlugins', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/plugins', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPluginsRequest', ], 'output' => [ 'shape' => 'ListPluginsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListRetrievers' => [ 'name' => 'ListRetrievers', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/retrievers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListRetrieversRequest', ], 'output' => [ 'shape' => 'ListRetrieversResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/tags/{resourceARN}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListWebExperiences' => [ 'name' => 'ListWebExperiences', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/experiences', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListWebExperiencesRequest', ], 'output' => [ 'shape' => 'ListWebExperiencesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'PutFeedback' => [ 'name' => 'PutFeedback', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications/{applicationId}/conversations/{conversationId}/messages/{messageId}/feedback', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutFeedbackRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'PutGroup' => [ 'name' => 'PutGroup', 'http' => [ 'method' => 'PUT', 'requestUri' => '/applications/{applicationId}/indices/{indexId}/groups', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutGroupRequest', ], 'output' => [ 'shape' => 'PutGroupResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'StartDataSourceSyncJob' => [ 'name' => 'StartDataSourceSyncJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications/{applicationId}/indices/{indexId}/datasources/{dataSourceId}/startsync', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartDataSourceSyncJobRequest', ], 'output' => [ 'shape' => 'StartDataSourceSyncJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'StopDataSourceSyncJob' => [ 'name' => 'StopDataSourceSyncJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications/{applicationId}/indices/{indexId}/datasources/{dataSourceId}/stopsync', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopDataSourceSyncJobRequest', ], 'output' => [ 'shape' => 'StopDataSourceSyncJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/tags/{resourceARN}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/tags/{resourceARN}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'UpdateApplication' => [ 'name' => 'UpdateApplication', 'http' => [ 'method' => 'PUT', 'requestUri' => '/applications/{applicationId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateApplicationRequest', ], 'output' => [ 'shape' => 'UpdateApplicationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'UpdateChatControlsConfiguration' => [ 'name' => 'UpdateChatControlsConfiguration', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/applications/{applicationId}/chatcontrols', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateChatControlsConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateChatControlsConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'UpdateDataSource' => [ 'name' => 'UpdateDataSource', 'http' => [ 'method' => 'PUT', 'requestUri' => '/applications/{applicationId}/indices/{indexId}/datasources/{dataSourceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateDataSourceRequest', ], 'output' => [ 'shape' => 'UpdateDataSourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'UpdateIndex' => [ 'name' => 'UpdateIndex', 'http' => [ 'method' => 'PUT', 'requestUri' => '/applications/{applicationId}/indices/{indexId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateIndexRequest', ], 'output' => [ 'shape' => 'UpdateIndexResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'UpdatePlugin' => [ 'name' => 'UpdatePlugin', 'http' => [ 'method' => 'PUT', 'requestUri' => '/applications/{applicationId}/plugins/{pluginId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdatePluginRequest', ], 'output' => [ 'shape' => 'UpdatePluginResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'UpdateRetriever' => [ 'name' => 'UpdateRetriever', 'http' => [ 'method' => 'PUT', 'requestUri' => '/applications/{applicationId}/retrievers/{retrieverId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateRetrieverRequest', ], 'output' => [ 'shape' => 'UpdateRetrieverResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'UpdateUser' => [ 'name' => 'UpdateUser', 'http' => [ 'method' => 'PUT', 'requestUri' => '/applications/{applicationId}/users/{userId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateUserRequest', ], 'output' => [ 'shape' => 'UpdateUserResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'UpdateWebExperience' => [ 'name' => 'UpdateWebExperience', 'http' => [ 'method' => 'PUT', 'requestUri' => '/applications/{applicationId}/experiences/{webExperienceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateWebExperienceRequest', ], 'output' => [ 'shape' => 'UpdateWebExperienceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'APISchema' => [ 'type' => 'structure', 'members' => [ 'payload' => [ 'shape' => 'Payload', ], 's3' => [ 'shape' => 'S3', ], ], 'union' => true, ], 'APISchemaType' => [ 'type' => 'string', 'enum' => [ 'OPEN_API_V3', ], ], 'AccessConfiguration' => [ 'type' => 'structure', 'required' => [ 'accessControls', ], 'members' => [ 'accessControls' => [ 'shape' => 'AccessControls', ], 'memberRelation' => [ 'shape' => 'MemberRelation', ], ], ], 'AccessControl' => [ 'type' => 'structure', 'required' => [ 'principals', ], 'members' => [ 'principals' => [ 'shape' => 'Principals', ], 'memberRelation' => [ 'shape' => 'MemberRelation', ], ], ], 'AccessControls' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccessControl', ], ], 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'ActionExecution' => [ 'type' => 'structure', 'required' => [ 'pluginId', 'payload', 'payloadFieldNameSeparator', ], 'members' => [ 'pluginId' => [ 'shape' => 'PluginId', ], 'payload' => [ 'shape' => 'ActionExecutionPayload', ], 'payloadFieldNameSeparator' => [ 'shape' => 'ActionPayloadFieldNameSeparator', ], ], ], 'ActionExecutionEvent' => [ 'type' => 'structure', 'required' => [ 'pluginId', 'payload', 'payloadFieldNameSeparator', ], 'members' => [ 'pluginId' => [ 'shape' => 'PluginId', ], 'payload' => [ 'shape' => 'ActionExecutionPayload', ], 'payloadFieldNameSeparator' => [ 'shape' => 'ActionPayloadFieldNameSeparator', ], ], 'event' => true, ], 'ActionExecutionPayload' => [ 'type' => 'map', 'key' => [ 'shape' => 'ActionPayloadFieldKey', ], 'value' => [ 'shape' => 'ActionExecutionPayloadField', ], ], 'ActionExecutionPayloadField' => [ 'type' => 'structure', 'required' => [ 'value', ], 'members' => [ 'value' => [ 'shape' => 'ActionPayloadFieldValue', ], ], ], 'ActionPayloadFieldKey' => [ 'type' => 'string', 'min' => 1, ], 'ActionPayloadFieldNameSeparator' => [ 'type' => 'string', 'max' => 1, 'min' => 1, ], 'ActionPayloadFieldType' => [ 'type' => 'string', 'enum' => [ 'STRING', 'NUMBER', 'ARRAY', 'BOOLEAN', ], ], 'ActionPayloadFieldValue' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'ActionReview' => [ 'type' => 'structure', 'members' => [ 'pluginId' => [ 'shape' => 'PluginId', ], 'pluginType' => [ 'shape' => 'PluginType', ], 'payload' => [ 'shape' => 'ActionReviewPayload', ], 'payloadFieldNameSeparator' => [ 'shape' => 'ActionPayloadFieldNameSeparator', ], ], ], 'ActionReviewEvent' => [ 'type' => 'structure', 'members' => [ 'conversationId' => [ 'shape' => 'ConversationId', ], 'userMessageId' => [ 'shape' => 'MessageId', ], 'systemMessageId' => [ 'shape' => 'MessageId', ], 'pluginId' => [ 'shape' => 'PluginId', ], 'pluginType' => [ 'shape' => 'PluginType', ], 'payload' => [ 'shape' => 'ActionReviewPayload', ], 'payloadFieldNameSeparator' => [ 'shape' => 'ActionPayloadFieldNameSeparator', ], ], 'event' => true, ], 'ActionReviewPayload' => [ 'type' => 'map', 'key' => [ 'shape' => 'ActionPayloadFieldKey', ], 'value' => [ 'shape' => 'ActionReviewPayloadField', ], ], 'ActionReviewPayloadField' => [ 'type' => 'structure', 'members' => [ 'displayName' => [ 'shape' => 'String', ], 'displayOrder' => [ 'shape' => 'Integer', ], 'displayDescription' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'ActionPayloadFieldType', ], 'value' => [ 'shape' => 'ActionPayloadFieldValue', ], 'allowedValues' => [ 'shape' => 'ActionReviewPayloadFieldAllowedValues', ], 'allowedFormat' => [ 'shape' => 'String', ], 'required' => [ 'shape' => 'Boolean', ], ], ], 'ActionReviewPayloadFieldAllowedValue' => [ 'type' => 'structure', 'members' => [ 'value' => [ 'shape' => 'ActionPayloadFieldValue', ], 'displayValue' => [ 'shape' => 'ActionPayloadFieldValue', ], ], ], 'ActionReviewPayloadFieldAllowedValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'ActionReviewPayloadFieldAllowedValue', ], ], 'AmazonResourceName' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, ], 'Application' => [ 'type' => 'structure', 'members' => [ 'displayName' => [ 'shape' => 'ApplicationName', ], 'applicationId' => [ 'shape' => 'ApplicationId', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'ApplicationStatus', ], 'identityType' => [ 'shape' => 'IdentityType', ], ], ], 'ApplicationArn' => [ 'type' => 'string', 'max' => 1284, 'min' => 0, 'pattern' => 'arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[^/].{0,1023}', ], 'ApplicationId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9-]{35}', ], 'ApplicationName' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9_-]*', ], 'ApplicationStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'DELETING', 'FAILED', 'UPDATING', ], ], 'Applications' => [ 'type' => 'list', 'member' => [ 'shape' => 'Application', ], ], 'AppliedAttachmentsConfiguration' => [ 'type' => 'structure', 'members' => [ 'attachmentsControlMode' => [ 'shape' => 'AttachmentsControlMode', ], ], ], 'AppliedCreatorModeConfiguration' => [ 'type' => 'structure', 'required' => [ 'creatorModeControl', ], 'members' => [ 'creatorModeControl' => [ 'shape' => 'CreatorModeControl', ], ], ], 'AttachmentInput' => [ 'type' => 'structure', 'required' => [ 'name', 'data', ], 'members' => [ 'name' => [ 'shape' => 'AttachmentName', ], 'data' => [ 'shape' => 'Blob', ], ], ], 'AttachmentInputEvent' => [ 'type' => 'structure', 'members' => [ 'attachment' => [ 'shape' => 'AttachmentInput', ], ], 'event' => true, ], 'AttachmentName' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '\\P{C}*', ], 'AttachmentOutput' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'AttachmentName', ], 'status' => [ 'shape' => 'AttachmentStatus', ], 'error' => [ 'shape' => 'ErrorDetail', ], ], ], 'AttachmentStatus' => [ 'type' => 'string', 'enum' => [ 'FAILED', 'SUCCEEDED', ], ], 'AttachmentsConfiguration' => [ 'type' => 'structure', 'required' => [ 'attachmentsControlMode', ], 'members' => [ 'attachmentsControlMode' => [ 'shape' => 'AttachmentsControlMode', ], ], ], 'AttachmentsControlMode' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'AttachmentsInput' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttachmentInput', ], 'min' => 1, ], 'AttachmentsOutput' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttachmentOutput', ], ], 'AttributeFilter' => [ 'type' => 'structure', 'members' => [ 'andAllFilters' => [ 'shape' => 'AttributeFilters', ], 'orAllFilters' => [ 'shape' => 'AttributeFilters', ], 'notFilter' => [ 'shape' => 'AttributeFilter', ], 'equalsTo' => [ 'shape' => 'DocumentAttribute', ], 'containsAll' => [ 'shape' => 'DocumentAttribute', ], 'containsAny' => [ 'shape' => 'DocumentAttribute', ], 'greaterThan' => [ 'shape' => 'DocumentAttribute', ], 'greaterThanOrEquals' => [ 'shape' => 'DocumentAttribute', ], 'lessThan' => [ 'shape' => 'DocumentAttribute', ], 'lessThanOrEquals' => [ 'shape' => 'DocumentAttribute', ], ], ], 'AttributeFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttributeFilter', ], ], 'AttributeType' => [ 'type' => 'string', 'enum' => [ 'STRING', 'STRING_LIST', 'NUMBER', 'DATE', ], ], 'AttributeValueOperator' => [ 'type' => 'string', 'enum' => [ 'DELETE', ], ], 'AuthChallengeRequest' => [ 'type' => 'structure', 'required' => [ 'authorizationUrl', ], 'members' => [ 'authorizationUrl' => [ 'shape' => 'Url', ], ], ], 'AuthChallengeRequestEvent' => [ 'type' => 'structure', 'required' => [ 'authorizationUrl', ], 'members' => [ 'authorizationUrl' => [ 'shape' => 'Url', ], ], 'event' => true, ], 'AuthChallengeResponse' => [ 'type' => 'structure', 'required' => [ 'responseMap', ], 'members' => [ 'responseMap' => [ 'shape' => 'AuthorizationResponseMap', ], ], ], 'AuthChallengeResponseEvent' => [ 'type' => 'structure', 'required' => [ 'responseMap', ], 'members' => [ 'responseMap' => [ 'shape' => 'AuthorizationResponseMap', ], ], 'event' => true, ], 'AuthResponseKey' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'AuthResponseValue' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'AuthorizationResponseMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'AuthResponseKey', ], 'value' => [ 'shape' => 'AuthResponseValue', ], ], 'AutoSubscriptionConfiguration' => [ 'type' => 'structure', 'required' => [ 'autoSubscribe', ], 'members' => [ 'autoSubscribe' => [ 'shape' => 'AutoSubscriptionStatus', ], 'defaultSubscriptionType' => [ 'shape' => 'SubscriptionType', ], ], ], 'AutoSubscriptionStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'BasicAuthConfiguration' => [ 'type' => 'structure', 'required' => [ 'secretArn', 'roleArn', ], 'members' => [ 'secretArn' => [ 'shape' => 'SecretArn', ], 'roleArn' => [ 'shape' => 'RoleArn', ], ], ], 'BatchDeleteDocumentRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'indexId', 'documents', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], 'documents' => [ 'shape' => 'DeleteDocuments', ], 'dataSourceSyncId' => [ 'shape' => 'ExecutionId', ], ], ], 'BatchDeleteDocumentResponse' => [ 'type' => 'structure', 'members' => [ 'failedDocuments' => [ 'shape' => 'FailedDocuments', ], ], ], 'BatchPutDocumentRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'indexId', 'documents', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], 'documents' => [ 'shape' => 'Documents', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'dataSourceSyncId' => [ 'shape' => 'ExecutionId', ], ], ], 'BatchPutDocumentResponse' => [ 'type' => 'structure', 'members' => [ 'failedDocuments' => [ 'shape' => 'FailedDocuments', ], ], ], 'Blob' => [ 'type' => 'blob', ], 'BlockedPhrase' => [ 'type' => 'string', 'max' => 36, 'min' => 0, 'pattern' => '\\P{C}*', ], 'BlockedPhrases' => [ 'type' => 'list', 'member' => [ 'shape' => 'BlockedPhrase', ], 'max' => 5, 'min' => 0, ], 'BlockedPhrasesConfiguration' => [ 'type' => 'structure', 'members' => [ 'blockedPhrases' => [ 'shape' => 'BlockedPhrases', ], 'systemMessageOverride' => [ 'shape' => 'SystemMessageOverride', ], ], ], 'BlockedPhrasesConfigurationUpdate' => [ 'type' => 'structure', 'members' => [ 'blockedPhrasesToCreateOrUpdate' => [ 'shape' => 'BlockedPhrases', ], 'blockedPhrasesToDelete' => [ 'shape' => 'BlockedPhrases', ], 'systemMessageOverride' => [ 'shape' => 'SystemMessageOverride', ], ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'BoostingDurationInSeconds' => [ 'type' => 'long', 'box' => true, 'max' => 999999999, 'min' => 0, ], 'ChatInput' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'userId' => [ 'shape' => 'UserId', 'location' => 'querystring', 'locationName' => 'userId', ], 'userGroups' => [ 'shape' => 'UserGroups', 'location' => 'querystring', 'locationName' => 'userGroups', ], 'conversationId' => [ 'shape' => 'ConversationId', 'location' => 'querystring', 'locationName' => 'conversationId', ], 'parentMessageId' => [ 'shape' => 'MessageId', 'location' => 'querystring', 'locationName' => 'parentMessageId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], 'inputStream' => [ 'shape' => 'ChatInputStream', ], ], 'payload' => 'inputStream', ], 'ChatInputStream' => [ 'type' => 'structure', 'members' => [ 'configurationEvent' => [ 'shape' => 'ConfigurationEvent', ], 'textEvent' => [ 'shape' => 'TextInputEvent', ], 'attachmentEvent' => [ 'shape' => 'AttachmentInputEvent', ], 'actionExecutionEvent' => [ 'shape' => 'ActionExecutionEvent', ], 'endOfInputEvent' => [ 'shape' => 'EndOfInputEvent', ], 'authChallengeResponseEvent' => [ 'shape' => 'AuthChallengeResponseEvent', ], ], 'eventstream' => true, ], 'ChatMode' => [ 'type' => 'string', 'enum' => [ 'RETRIEVAL_MODE', 'CREATOR_MODE', 'PLUGIN_MODE', ], ], 'ChatModeConfiguration' => [ 'type' => 'structure', 'members' => [ 'pluginConfiguration' => [ 'shape' => 'PluginConfiguration', ], ], 'union' => true, ], 'ChatOutput' => [ 'type' => 'structure', 'members' => [ 'outputStream' => [ 'shape' => 'ChatOutputStream', ], ], 'payload' => 'outputStream', ], 'ChatOutputStream' => [ 'type' => 'structure', 'members' => [ 'textEvent' => [ 'shape' => 'TextOutputEvent', ], 'metadataEvent' => [ 'shape' => 'MetadataEvent', ], 'actionReviewEvent' => [ 'shape' => 'ActionReviewEvent', ], 'failedAttachmentEvent' => [ 'shape' => 'FailedAttachmentEvent', ], 'authChallengeRequestEvent' => [ 'shape' => 'AuthChallengeRequestEvent', ], ], 'eventstream' => true, ], 'ChatSyncInput' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'userId' => [ 'shape' => 'UserId', 'location' => 'querystring', 'locationName' => 'userId', ], 'userGroups' => [ 'shape' => 'UserGroups', 'location' => 'querystring', 'locationName' => 'userGroups', ], 'userMessage' => [ 'shape' => 'UserMessage', ], 'attachments' => [ 'shape' => 'AttachmentsInput', ], 'actionExecution' => [ 'shape' => 'ActionExecution', ], 'authChallengeResponse' => [ 'shape' => 'AuthChallengeResponse', ], 'conversationId' => [ 'shape' => 'ConversationId', ], 'parentMessageId' => [ 'shape' => 'MessageId', ], 'attributeFilter' => [ 'shape' => 'AttributeFilter', ], 'chatMode' => [ 'shape' => 'ChatMode', ], 'chatModeConfiguration' => [ 'shape' => 'ChatModeConfiguration', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'ChatSyncOutput' => [ 'type' => 'structure', 'members' => [ 'conversationId' => [ 'shape' => 'ConversationId', ], 'systemMessage' => [ 'shape' => 'String', ], 'systemMessageId' => [ 'shape' => 'MessageId', ], 'userMessageId' => [ 'shape' => 'MessageId', ], 'actionReview' => [ 'shape' => 'ActionReview', ], 'authChallengeRequest' => [ 'shape' => 'AuthChallengeRequest', ], 'sourceAttributions' => [ 'shape' => 'SourceAttributions', ], 'failedAttachments' => [ 'shape' => 'AttachmentsOutput', ], ], ], 'ClientIdForOIDC' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'ClientIdsForOIDC' => [ 'type' => 'list', 'member' => [ 'shape' => 'ClientIdForOIDC', ], ], 'ClientToken' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'ConfigurationEvent' => [ 'type' => 'structure', 'members' => [ 'chatMode' => [ 'shape' => 'ChatMode', ], 'chatModeConfiguration' => [ 'shape' => 'ChatModeConfiguration', ], 'attributeFilter' => [ 'shape' => 'AttributeFilter', ], ], 'event' => true, ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ContentBlockerRule' => [ 'type' => 'structure', 'members' => [ 'systemMessageOverride' => [ 'shape' => 'SystemMessageOverride', ], ], ], 'ContentRetrievalRule' => [ 'type' => 'structure', 'members' => [ 'eligibleDataSources' => [ 'shape' => 'EligibleDataSources', ], ], ], 'ContentType' => [ 'type' => 'string', 'enum' => [ 'PDF', 'HTML', 'MS_WORD', 'PLAIN_TEXT', 'PPT', 'RTF', 'XML', 'XSLT', 'MS_EXCEL', 'CSV', 'JSON', 'MD', ], ], 'Conversation' => [ 'type' => 'structure', 'members' => [ 'conversationId' => [ 'shape' => 'ConversationId', ], 'title' => [ 'shape' => 'ConversationTitle', ], 'startTime' => [ 'shape' => 'Timestamp', ], ], ], 'ConversationId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9-]{35}', ], 'ConversationTitle' => [ 'type' => 'string', ], 'Conversations' => [ 'type' => 'list', 'member' => [ 'shape' => 'Conversation', ], ], 'CreateApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'displayName', ], 'members' => [ 'displayName' => [ 'shape' => 'ApplicationName', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'identityType' => [ 'shape' => 'IdentityType', ], 'iamIdentityProviderArn' => [ 'shape' => 'IamIdentityProviderArn', ], 'identityCenterInstanceArn' => [ 'shape' => 'InstanceArn', ], 'clientIdsForOIDC' => [ 'shape' => 'ClientIdsForOIDC', ], 'description' => [ 'shape' => 'Description', ], 'encryptionConfiguration' => [ 'shape' => 'EncryptionConfiguration', ], 'tags' => [ 'shape' => 'Tags', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'attachmentsConfiguration' => [ 'shape' => 'AttachmentsConfiguration', ], 'qAppsConfiguration' => [ 'shape' => 'QAppsConfiguration', ], 'personalizationConfiguration' => [ 'shape' => 'PersonalizationConfiguration', ], ], ], 'CreateApplicationResponse' => [ 'type' => 'structure', 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', ], 'applicationArn' => [ 'shape' => 'ApplicationArn', ], ], ], 'CreateDataSourceRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'indexId', 'displayName', 'configuration', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], 'displayName' => [ 'shape' => 'DataSourceName', ], 'configuration' => [ 'shape' => 'DataSourceConfiguration', ], 'vpcConfiguration' => [ 'shape' => 'DataSourceVpcConfiguration', ], 'description' => [ 'shape' => 'Description', ], 'tags' => [ 'shape' => 'Tags', ], 'syncSchedule' => [ 'shape' => 'SyncSchedule', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'documentEnrichmentConfiguration' => [ 'shape' => 'DocumentEnrichmentConfiguration', ], ], ], 'CreateDataSourceResponse' => [ 'type' => 'structure', 'members' => [ 'dataSourceId' => [ 'shape' => 'DataSourceId', ], 'dataSourceArn' => [ 'shape' => 'DataSourceArn', ], ], ], 'CreateIndexRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'displayName', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'displayName' => [ 'shape' => 'IndexName', ], 'type' => [ 'shape' => 'IndexType', ], 'description' => [ 'shape' => 'Description', ], 'tags' => [ 'shape' => 'Tags', ], 'capacityConfiguration' => [ 'shape' => 'IndexCapacityConfiguration', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateIndexResponse' => [ 'type' => 'structure', 'members' => [ 'indexId' => [ 'shape' => 'IndexId', ], 'indexArn' => [ 'shape' => 'IndexArn', ], ], ], 'CreatePluginRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'displayName', 'type', 'authConfiguration', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'displayName' => [ 'shape' => 'PluginName', ], 'type' => [ 'shape' => 'PluginType', ], 'authConfiguration' => [ 'shape' => 'PluginAuthConfiguration', ], 'serverUrl' => [ 'shape' => 'Url', ], 'customPluginConfiguration' => [ 'shape' => 'CustomPluginConfiguration', ], 'tags' => [ 'shape' => 'Tags', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreatePluginResponse' => [ 'type' => 'structure', 'members' => [ 'pluginId' => [ 'shape' => 'PluginId', ], 'pluginArn' => [ 'shape' => 'PluginArn', ], 'buildStatus' => [ 'shape' => 'PluginBuildStatus', ], ], ], 'CreateRetrieverRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'type', 'displayName', 'configuration', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'type' => [ 'shape' => 'RetrieverType', ], 'displayName' => [ 'shape' => 'RetrieverName', ], 'configuration' => [ 'shape' => 'RetrieverConfiguration', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateRetrieverResponse' => [ 'type' => 'structure', 'members' => [ 'retrieverId' => [ 'shape' => 'RetrieverId', ], 'retrieverArn' => [ 'shape' => 'RetrieverArn', ], ], ], 'CreateUserRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'userId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'userId' => [ 'shape' => 'String', ], 'userAliases' => [ 'shape' => 'CreateUserRequestUserAliasesList', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateUserRequestUserAliasesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserAlias', ], 'max' => 100, 'min' => 0, ], 'CreateUserResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateWebExperienceRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'title' => [ 'shape' => 'WebExperienceTitle', ], 'subtitle' => [ 'shape' => 'WebExperienceSubtitle', ], 'welcomeMessage' => [ 'shape' => 'WebExperienceWelcomeMessage', ], 'samplePromptsControlMode' => [ 'shape' => 'WebExperienceSamplePromptsControlMode', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'tags' => [ 'shape' => 'Tags', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'identityProviderConfiguration' => [ 'shape' => 'IdentityProviderConfiguration', ], ], ], 'CreateWebExperienceResponse' => [ 'type' => 'structure', 'members' => [ 'webExperienceId' => [ 'shape' => 'WebExperienceId', ], 'webExperienceArn' => [ 'shape' => 'WebExperienceArn', ], ], ], 'CreatorModeConfiguration' => [ 'type' => 'structure', 'required' => [ 'creatorModeControl', ], 'members' => [ 'creatorModeControl' => [ 'shape' => 'CreatorModeControl', ], ], ], 'CreatorModeControl' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'CustomPluginConfiguration' => [ 'type' => 'structure', 'required' => [ 'description', 'apiSchemaType', 'apiSchema', ], 'members' => [ 'description' => [ 'shape' => 'PluginDescription', ], 'apiSchemaType' => [ 'shape' => 'APISchemaType', ], 'apiSchema' => [ 'shape' => 'APISchema', ], ], ], 'DataSource' => [ 'type' => 'structure', 'members' => [ 'displayName' => [ 'shape' => 'DataSourceName', ], 'dataSourceId' => [ 'shape' => 'DataSourceId', ], 'type' => [ 'shape' => 'String', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'DataSourceStatus', ], ], ], 'DataSourceArn' => [ 'type' => 'string', 'max' => 1284, 'min' => 0, 'pattern' => 'arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[^/].{0,1023}', ], 'DataSourceConfiguration' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'DataSourceId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9-]{35}', ], 'DataSourceIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSourceId', ], 'max' => 1, 'min' => 1, ], 'DataSourceName' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9_-]*', ], 'DataSourceStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING_CREATION', 'CREATING', 'ACTIVE', 'DELETING', 'FAILED', 'UPDATING', ], ], 'DataSourceSyncJob' => [ 'type' => 'structure', 'members' => [ 'executionId' => [ 'shape' => 'ExecutionId', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'DataSourceSyncJobStatus', ], 'error' => [ 'shape' => 'ErrorDetail', ], 'dataSourceErrorCode' => [ 'shape' => 'String', ], 'metrics' => [ 'shape' => 'DataSourceSyncJobMetrics', ], ], ], 'DataSourceSyncJobMetrics' => [ 'type' => 'structure', 'members' => [ 'documentsAdded' => [ 'shape' => 'MetricValue', ], 'documentsModified' => [ 'shape' => 'MetricValue', ], 'documentsDeleted' => [ 'shape' => 'MetricValue', ], 'documentsFailed' => [ 'shape' => 'MetricValue', ], 'documentsScanned' => [ 'shape' => 'MetricValue', ], ], ], 'DataSourceSyncJobStatus' => [ 'type' => 'string', 'enum' => [ 'FAILED', 'SUCCEEDED', 'SYNCING', 'INCOMPLETE', 'STOPPING', 'ABORTED', 'SYNCING_INDEXING', ], ], 'DataSourceSyncJobs' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSourceSyncJob', ], ], 'DataSourceUserId' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '\\P{C}*', ], 'DataSourceVpcConfiguration' => [ 'type' => 'structure', 'required' => [ 'subnetIds', 'securityGroupIds', ], 'members' => [ 'subnetIds' => [ 'shape' => 'SubnetIds', ], 'securityGroupIds' => [ 'shape' => 'SecurityGroupIds', ], ], ], 'DataSources' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSource', ], ], 'DateAttributeBoostingConfiguration' => [ 'type' => 'structure', 'required' => [ 'boostingLevel', ], 'members' => [ 'boostingLevel' => [ 'shape' => 'DocumentAttributeBoostingLevel', ], 'boostingDurationInSeconds' => [ 'shape' => 'BoostingDurationInSeconds', ], ], ], 'DeleteApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], ], ], 'DeleteApplicationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteChatControlsConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], ], ], 'DeleteChatControlsConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteConversationRequest' => [ 'type' => 'structure', 'required' => [ 'conversationId', 'applicationId', ], 'members' => [ 'conversationId' => [ 'shape' => 'ConversationId', 'location' => 'uri', 'locationName' => 'conversationId', ], 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'userId' => [ 'shape' => 'UserId', 'location' => 'querystring', 'locationName' => 'userId', ], ], ], 'DeleteConversationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDataSourceRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'indexId', 'dataSourceId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], 'dataSourceId' => [ 'shape' => 'DataSourceId', 'location' => 'uri', 'locationName' => 'dataSourceId', ], ], ], 'DeleteDataSourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDocument' => [ 'type' => 'structure', 'required' => [ 'documentId', ], 'members' => [ 'documentId' => [ 'shape' => 'DocumentId', ], ], ], 'DeleteDocuments' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeleteDocument', ], ], 'DeleteGroupRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'indexId', 'groupName', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], 'groupName' => [ 'shape' => 'GroupName', 'location' => 'uri', 'locationName' => 'groupName', ], 'dataSourceId' => [ 'shape' => 'DataSourceId', 'location' => 'querystring', 'locationName' => 'dataSourceId', ], ], ], 'DeleteGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteIndexRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'indexId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], ], ], 'DeleteIndexResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeletePluginRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'pluginId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'pluginId' => [ 'shape' => 'PluginId', 'location' => 'uri', 'locationName' => 'pluginId', ], ], ], 'DeletePluginResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRetrieverRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'retrieverId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'retrieverId' => [ 'shape' => 'RetrieverId', 'location' => 'uri', 'locationName' => 'retrieverId', ], ], ], 'DeleteRetrieverResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteUserRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'userId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'userId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'userId', ], ], ], 'DeleteUserResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteWebExperienceRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'webExperienceId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'webExperienceId' => [ 'shape' => 'WebExperienceId', 'location' => 'uri', 'locationName' => 'webExperienceId', ], ], ], 'DeleteWebExperienceResponse' => [ 'type' => 'structure', 'members' => [], ], 'Description' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, 'pattern' => '\\P{C}*', ], 'Document' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'DocumentId', ], 'attributes' => [ 'shape' => 'DocumentAttributes', ], 'content' => [ 'shape' => 'DocumentContent', ], 'contentType' => [ 'shape' => 'ContentType', ], 'title' => [ 'shape' => 'Title', ], 'accessConfiguration' => [ 'shape' => 'AccessConfiguration', ], 'documentEnrichmentConfiguration' => [ 'shape' => 'DocumentEnrichmentConfiguration', ], ], ], 'DocumentAttribute' => [ 'type' => 'structure', 'required' => [ 'name', 'value', ], 'members' => [ 'name' => [ 'shape' => 'DocumentAttributeKey', ], 'value' => [ 'shape' => 'DocumentAttributeValue', ], ], ], 'DocumentAttributeBoostingConfiguration' => [ 'type' => 'structure', 'members' => [ 'numberConfiguration' => [ 'shape' => 'NumberAttributeBoostingConfiguration', ], 'stringConfiguration' => [ 'shape' => 'StringAttributeBoostingConfiguration', ], 'dateConfiguration' => [ 'shape' => 'DateAttributeBoostingConfiguration', ], 'stringListConfiguration' => [ 'shape' => 'StringListAttributeBoostingConfiguration', ], ], 'union' => true, ], 'DocumentAttributeBoostingLevel' => [ 'type' => 'string', 'enum' => [ 'NONE', 'LOW', 'MEDIUM', 'HIGH', 'VERY_HIGH', ], ], 'DocumentAttributeBoostingOverrideMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'DocumentAttributeKey', ], 'value' => [ 'shape' => 'DocumentAttributeBoostingConfiguration', ], 'min' => 1, ], 'DocumentAttributeCondition' => [ 'type' => 'structure', 'required' => [ 'key', 'operator', ], 'members' => [ 'key' => [ 'shape' => 'DocumentAttributeKey', ], 'operator' => [ 'shape' => 'DocumentEnrichmentConditionOperator', ], 'value' => [ 'shape' => 'DocumentAttributeValue', ], ], ], 'DocumentAttributeConfiguration' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'DocumentMetadataConfigurationName', ], 'type' => [ 'shape' => 'AttributeType', ], 'search' => [ 'shape' => 'Status', ], ], ], 'DocumentAttributeConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentAttributeConfiguration', ], 'max' => 500, 'min' => 1, ], 'DocumentAttributeKey' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '[a-zA-Z0-9_][a-zA-Z0-9_-]*', ], 'DocumentAttributeStringListValue' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'DocumentAttributeTarget' => [ 'type' => 'structure', 'required' => [ 'key', ], 'members' => [ 'key' => [ 'shape' => 'DocumentAttributeKey', ], 'value' => [ 'shape' => 'DocumentAttributeValue', ], 'attributeValueOperator' => [ 'shape' => 'AttributeValueOperator', ], ], ], 'DocumentAttributeValue' => [ 'type' => 'structure', 'members' => [ 'stringValue' => [ 'shape' => 'DocumentAttributeValueStringValueString', ], 'stringListValue' => [ 'shape' => 'DocumentAttributeStringListValue', ], 'longValue' => [ 'shape' => 'Long', ], 'dateValue' => [ 'shape' => 'Timestamp', ], ], 'union' => true, ], 'DocumentAttributeValueStringValueString' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, ], 'DocumentAttributes' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentAttribute', ], 'max' => 500, 'min' => 1, ], 'DocumentContent' => [ 'type' => 'structure', 'members' => [ 'blob' => [ 'shape' => 'Blob', ], 's3' => [ 'shape' => 'S3', ], ], 'union' => true, ], 'DocumentContentOperator' => [ 'type' => 'string', 'enum' => [ 'DELETE', ], ], 'DocumentDetailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentDetails', ], ], 'DocumentDetails' => [ 'type' => 'structure', 'members' => [ 'documentId' => [ 'shape' => 'DocumentId', ], 'status' => [ 'shape' => 'DocumentStatus', ], 'error' => [ 'shape' => 'ErrorDetail', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'DocumentEnrichmentConditionOperator' => [ 'type' => 'string', 'enum' => [ 'GREATER_THAN', 'GREATER_THAN_OR_EQUALS', 'LESS_THAN', 'LESS_THAN_OR_EQUALS', 'EQUALS', 'NOT_EQUALS', 'CONTAINS', 'NOT_CONTAINS', 'EXISTS', 'NOT_EXISTS', 'BEGINS_WITH', ], ], 'DocumentEnrichmentConfiguration' => [ 'type' => 'structure', 'members' => [ 'inlineConfigurations' => [ 'shape' => 'InlineDocumentEnrichmentConfigurations', ], 'preExtractionHookConfiguration' => [ 'shape' => 'HookConfiguration', ], 'postExtractionHookConfiguration' => [ 'shape' => 'HookConfiguration', ], ], ], 'DocumentId' => [ 'type' => 'string', 'max' => 1825, 'min' => 1, 'pattern' => '\\P{C}*', ], 'DocumentMetadataConfigurationName' => [ 'type' => 'string', 'max' => 30, 'min' => 1, 'pattern' => '[a-zA-Z0-9_][a-zA-Z0-9_-]*', ], 'DocumentStatus' => [ 'type' => 'string', 'enum' => [ 'RECEIVED', 'PROCESSING', 'INDEXED', 'UPDATED', 'FAILED', 'DELETING', 'DELETED', 'DOCUMENT_FAILED_TO_INDEX', ], ], 'Documents' => [ 'type' => 'list', 'member' => [ 'shape' => 'Document', ], 'max' => 10, 'min' => 1, ], 'EligibleDataSource' => [ 'type' => 'structure', 'members' => [ 'indexId' => [ 'shape' => 'IndexId', ], 'dataSourceId' => [ 'shape' => 'DataSourceId', ], ], ], 'EligibleDataSources' => [ 'type' => 'list', 'member' => [ 'shape' => 'EligibleDataSource', ], 'max' => 5, 'min' => 0, ], 'EncryptionConfiguration' => [ 'type' => 'structure', 'members' => [ 'kmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'EndOfInputEvent' => [ 'type' => 'structure', 'members' => [], 'event' => true, ], 'ErrorCode' => [ 'type' => 'string', 'enum' => [ 'InternalError', 'InvalidRequest', 'ResourceInactive', 'ResourceNotFound', ], ], 'ErrorDetail' => [ 'type' => 'structure', 'members' => [ 'errorMessage' => [ 'shape' => 'ErrorMessage', ], 'errorCode' => [ 'shape' => 'ErrorCode', ], ], ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '\\P{C}*', ], 'ExampleChatMessage' => [ 'type' => 'string', 'max' => 350, 'min' => 0, 'pattern' => '\\P{C}*', ], 'ExampleChatMessages' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExampleChatMessage', ], 'max' => 5, 'min' => 0, ], 'ExecutionId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9-]{35}', ], 'FailedAttachmentEvent' => [ 'type' => 'structure', 'members' => [ 'conversationId' => [ 'shape' => 'ConversationId', ], 'userMessageId' => [ 'shape' => 'MessageId', ], 'systemMessageId' => [ 'shape' => 'MessageId', ], 'attachment' => [ 'shape' => 'AttachmentOutput', ], ], 'event' => true, ], 'FailedDocument' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'DocumentId', ], 'error' => [ 'shape' => 'ErrorDetail', ], 'dataSourceId' => [ 'shape' => 'DataSourceId', ], ], ], 'FailedDocuments' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailedDocument', ], ], 'GetApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], ], ], 'GetApplicationResponse' => [ 'type' => 'structure', 'members' => [ 'displayName' => [ 'shape' => 'ApplicationName', ], 'applicationId' => [ 'shape' => 'ApplicationId', ], 'applicationArn' => [ 'shape' => 'ApplicationArn', ], 'identityType' => [ 'shape' => 'IdentityType', ], 'iamIdentityProviderArn' => [ 'shape' => 'IamIdentityProviderArn', ], 'identityCenterApplicationArn' => [ 'shape' => 'IdcApplicationArn', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'status' => [ 'shape' => 'ApplicationStatus', ], 'description' => [ 'shape' => 'Description', ], 'encryptionConfiguration' => [ 'shape' => 'EncryptionConfiguration', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'error' => [ 'shape' => 'ErrorDetail', ], 'attachmentsConfiguration' => [ 'shape' => 'AppliedAttachmentsConfiguration', ], 'qAppsConfiguration' => [ 'shape' => 'QAppsConfiguration', ], 'personalizationConfiguration' => [ 'shape' => 'PersonalizationConfiguration', ], 'autoSubscriptionConfiguration' => [ 'shape' => 'AutoSubscriptionConfiguration', ], 'clientIdsForOIDC' => [ 'shape' => 'ClientIdsForOIDC', ], ], ], 'GetChatControlsConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'maxResults' => [ 'shape' => 'MaxResultsIntegerForGetTopicConfigurations', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'GetChatControlsConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'responseScope' => [ 'shape' => 'ResponseScope', ], 'blockedPhrases' => [ 'shape' => 'BlockedPhrasesConfiguration', ], 'topicConfigurations' => [ 'shape' => 'TopicConfigurations', ], 'creatorModeConfiguration' => [ 'shape' => 'AppliedCreatorModeConfiguration', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetDataSourceRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'indexId', 'dataSourceId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], 'dataSourceId' => [ 'shape' => 'DataSourceId', 'location' => 'uri', 'locationName' => 'dataSourceId', ], ], ], 'GetDataSourceResponse' => [ 'type' => 'structure', 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', ], 'indexId' => [ 'shape' => 'IndexId', ], 'dataSourceId' => [ 'shape' => 'DataSourceId', ], 'dataSourceArn' => [ 'shape' => 'DataSourceArn', ], 'displayName' => [ 'shape' => 'DataSourceName', ], 'type' => [ 'shape' => 'String', ], 'configuration' => [ 'shape' => 'DataSourceConfiguration', ], 'vpcConfiguration' => [ 'shape' => 'DataSourceVpcConfiguration', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'status' => [ 'shape' => 'DataSourceStatus', ], 'syncSchedule' => [ 'shape' => 'SyncSchedule', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'error' => [ 'shape' => 'ErrorDetail', ], 'documentEnrichmentConfiguration' => [ 'shape' => 'DocumentEnrichmentConfiguration', ], ], ], 'GetGroupRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'indexId', 'groupName', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], 'groupName' => [ 'shape' => 'GroupName', 'location' => 'uri', 'locationName' => 'groupName', ], 'dataSourceId' => [ 'shape' => 'DataSourceId', 'location' => 'querystring', 'locationName' => 'dataSourceId', ], ], ], 'GetGroupResponse' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'GroupStatusDetail', ], 'statusHistory' => [ 'shape' => 'GroupStatusDetails', ], ], ], 'GetIndexRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'indexId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], ], ], 'GetIndexResponse' => [ 'type' => 'structure', 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', ], 'indexId' => [ 'shape' => 'IndexId', ], 'displayName' => [ 'shape' => 'IndexName', ], 'type' => [ 'shape' => 'IndexType', ], 'indexArn' => [ 'shape' => 'IndexArn', ], 'status' => [ 'shape' => 'IndexStatus', ], 'description' => [ 'shape' => 'Description', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'capacityConfiguration' => [ 'shape' => 'IndexCapacityConfiguration', ], 'documentAttributeConfigurations' => [ 'shape' => 'DocumentAttributeConfigurations', ], 'error' => [ 'shape' => 'ErrorDetail', ], 'indexStatistics' => [ 'shape' => 'IndexStatistics', ], ], ], 'GetPluginRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'pluginId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'pluginId' => [ 'shape' => 'PluginId', 'location' => 'uri', 'locationName' => 'pluginId', ], ], ], 'GetPluginResponse' => [ 'type' => 'structure', 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', ], 'pluginId' => [ 'shape' => 'PluginId', ], 'displayName' => [ 'shape' => 'PluginName', ], 'type' => [ 'shape' => 'PluginType', ], 'serverUrl' => [ 'shape' => 'Url', ], 'authConfiguration' => [ 'shape' => 'PluginAuthConfiguration', ], 'customPluginConfiguration' => [ 'shape' => 'CustomPluginConfiguration', ], 'buildStatus' => [ 'shape' => 'PluginBuildStatus', ], 'pluginArn' => [ 'shape' => 'PluginArn', ], 'state' => [ 'shape' => 'PluginState', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'GetRetrieverRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'retrieverId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'retrieverId' => [ 'shape' => 'RetrieverId', 'location' => 'uri', 'locationName' => 'retrieverId', ], ], ], 'GetRetrieverResponse' => [ 'type' => 'structure', 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', ], 'retrieverId' => [ 'shape' => 'RetrieverId', ], 'retrieverArn' => [ 'shape' => 'RetrieverArn', ], 'type' => [ 'shape' => 'RetrieverType', ], 'status' => [ 'shape' => 'RetrieverStatus', ], 'displayName' => [ 'shape' => 'RetrieverName', ], 'configuration' => [ 'shape' => 'RetrieverConfiguration', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'GetUserRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'userId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'userId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'userId', ], ], ], 'GetUserResponse' => [ 'type' => 'structure', 'members' => [ 'userAliases' => [ 'shape' => 'UserAliases', ], ], ], 'GetWebExperienceRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'webExperienceId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'webExperienceId' => [ 'shape' => 'WebExperienceId', 'location' => 'uri', 'locationName' => 'webExperienceId', ], ], ], 'GetWebExperienceResponse' => [ 'type' => 'structure', 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', ], 'webExperienceId' => [ 'shape' => 'WebExperienceId', ], 'webExperienceArn' => [ 'shape' => 'WebExperienceArn', ], 'defaultEndpoint' => [ 'shape' => 'Url', ], 'status' => [ 'shape' => 'WebExperienceStatus', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'title' => [ 'shape' => 'WebExperienceTitle', ], 'subtitle' => [ 'shape' => 'WebExperienceSubtitle', ], 'welcomeMessage' => [ 'shape' => 'WebExperienceWelcomeMessage', ], 'samplePromptsControlMode' => [ 'shape' => 'WebExperienceSamplePromptsControlMode', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'identityProviderConfiguration' => [ 'shape' => 'IdentityProviderConfiguration', ], 'authenticationConfiguration' => [ 'shape' => 'WebExperienceAuthConfiguration', 'deprecated' => true, 'deprecatedMessage' => 'Property associated with legacy SAML IdP flow. Deprecated in favor of using AWS IAM Identity Center for user management.', ], 'error' => [ 'shape' => 'ErrorDetail', ], ], ], 'GroupMembers' => [ 'type' => 'structure', 'members' => [ 'memberGroups' => [ 'shape' => 'MemberGroups', ], 'memberUsers' => [ 'shape' => 'MemberUsers', ], ], ], 'GroupName' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '\\P{C}*', ], 'GroupStatus' => [ 'type' => 'string', 'enum' => [ 'FAILED', 'SUCCEEDED', 'PROCESSING', 'DELETING', 'DELETED', ], ], 'GroupStatusDetail' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'GroupStatus', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'errorDetail' => [ 'shape' => 'ErrorDetail', ], ], ], 'GroupStatusDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'GroupStatusDetail', ], ], 'GroupSummary' => [ 'type' => 'structure', 'members' => [ 'groupName' => [ 'shape' => 'GroupName', ], ], ], 'GroupSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GroupSummary', ], ], 'HookConfiguration' => [ 'type' => 'structure', 'members' => [ 'invocationCondition' => [ 'shape' => 'DocumentAttributeCondition', ], 'lambdaArn' => [ 'shape' => 'LambdaArn', ], 's3BucketName' => [ 'shape' => 'S3BucketName', ], 'roleArn' => [ 'shape' => 'RoleArn', ], ], ], 'IamIdentityProviderArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws:iam::\\d{12}:(oidc-provider|saml-provider)/[a-zA-Z0-9_\\.\\/@\\-]+', ], 'IdcApplicationArn' => [ 'type' => 'string', 'max' => 1224, 'min' => 10, 'pattern' => 'arn:(aws|aws-us-gov|aws-cn|aws-iso|aws-iso-b):sso::\\d{12}:application/(sso)?ins-[a-zA-Z0-9-.]{16}/apl-[a-zA-Z0-9]{16}', ], 'IdentityProviderConfiguration' => [ 'type' => 'structure', 'members' => [ 'samlConfiguration' => [ 'shape' => 'SamlProviderConfiguration', ], 'openIDConnectConfiguration' => [ 'shape' => 'OpenIDConnectProviderConfiguration', ], ], 'union' => true, ], 'IdentityType' => [ 'type' => 'string', 'enum' => [ 'AWS_IAM_IDP_SAML', 'AWS_IAM_IDP_OIDC', 'AWS_IAM_IDC', ], ], 'Index' => [ 'type' => 'structure', 'members' => [ 'displayName' => [ 'shape' => 'IndexName', ], 'indexId' => [ 'shape' => 'IndexId', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'IndexStatus', ], ], ], 'IndexArn' => [ 'type' => 'string', 'max' => 1284, 'min' => 0, 'pattern' => 'arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[^/].{0,1023}', ], 'IndexCapacityConfiguration' => [ 'type' => 'structure', 'members' => [ 'units' => [ 'shape' => 'IndexCapacityInteger', ], ], ], 'IndexCapacityInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'IndexId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9-]{35}', ], 'IndexName' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9_-]*', ], 'IndexStatistics' => [ 'type' => 'structure', 'members' => [ 'textDocumentStatistics' => [ 'shape' => 'TextDocumentStatistics', ], ], ], 'IndexStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'DELETING', 'FAILED', 'UPDATING', ], ], 'IndexType' => [ 'type' => 'string', 'enum' => [ 'ENTERPRISE', 'STARTER', ], ], 'IndexedTextBytes' => [ 'type' => 'long', 'box' => true, 'min' => 0, ], 'IndexedTextDocument' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'Indices' => [ 'type' => 'list', 'member' => [ 'shape' => 'Index', ], ], 'InlineDocumentEnrichmentConfiguration' => [ 'type' => 'structure', 'members' => [ 'condition' => [ 'shape' => 'DocumentAttributeCondition', ], 'target' => [ 'shape' => 'DocumentAttributeTarget', ], 'documentContentOperator' => [ 'shape' => 'DocumentContentOperator', ], ], ], 'InlineDocumentEnrichmentConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'InlineDocumentEnrichmentConfiguration', ], 'max' => 100, 'min' => 1, ], 'InstanceArn' => [ 'type' => 'string', 'max' => 1224, 'min' => 10, 'pattern' => 'arn:(aws|aws-us-gov|aws-cn|aws-iso|aws-iso-b):sso:::instance/(sso)?ins-[a-zA-Z0-9-.]{16}', ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'KendraIndexConfiguration' => [ 'type' => 'structure', 'required' => [ 'indexId', ], 'members' => [ 'indexId' => [ 'shape' => 'KendraIndexId', ], ], ], 'KendraIndexId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9-]{35}', ], 'KmsKeyId' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'sensitive' => true, ], 'LambdaArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => 'arn:aws[a-zA-Z-]*:lambda:[a-z-]*-[0-9]:[0-9]{12}:function:[a-zA-Z0-9-_]+(/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})?(:[a-zA-Z0-9-_]+)?', ], 'LicenseNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ListApplicationsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResultsIntegerForListApplications', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListApplicationsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'applications' => [ 'shape' => 'Applications', ], ], ], 'ListConversationsRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'userId' => [ 'shape' => 'UserId', 'location' => 'querystring', 'locationName' => 'userId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResultsIntegerForListConversations', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListConversationsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'conversations' => [ 'shape' => 'Conversations', ], ], ], 'ListDataSourceSyncJobsRequest' => [ 'type' => 'structure', 'required' => [ 'dataSourceId', 'applicationId', 'indexId', ], 'members' => [ 'dataSourceId' => [ 'shape' => 'DataSourceId', 'location' => 'uri', 'locationName' => 'dataSourceId', ], 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResultsIntegerForListDataSourcesSyncJobs', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'startTime' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'startTime', ], 'endTime' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'endTime', ], 'statusFilter' => [ 'shape' => 'DataSourceSyncJobStatus', 'location' => 'querystring', 'locationName' => 'syncStatus', ], ], ], 'ListDataSourceSyncJobsResponse' => [ 'type' => 'structure', 'members' => [ 'history' => [ 'shape' => 'DataSourceSyncJobs', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDataSourcesRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'indexId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResultsIntegerForListDataSources', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListDataSourcesResponse' => [ 'type' => 'structure', 'members' => [ 'dataSources' => [ 'shape' => 'DataSources', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDocumentsRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'indexId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], 'dataSourceIds' => [ 'shape' => 'DataSourceIds', 'location' => 'querystring', 'locationName' => 'dataSourceIds', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResultsIntegerForListDocuments', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListDocumentsResponse' => [ 'type' => 'structure', 'members' => [ 'documentDetailList' => [ 'shape' => 'DocumentDetailList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListGroupsRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'indexId', 'updatedEarlierThan', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], 'updatedEarlierThan' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'updatedEarlierThan', ], 'dataSourceId' => [ 'shape' => 'DataSourceId', 'location' => 'querystring', 'locationName' => 'dataSourceId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResultsIntegerForListGroupsRequest', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'items' => [ 'shape' => 'GroupSummaryList', ], ], ], 'ListIndicesRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResultsIntegerForListIndices', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListIndicesResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'indices' => [ 'shape' => 'Indices', ], ], ], 'ListMessagesRequest' => [ 'type' => 'structure', 'required' => [ 'conversationId', 'applicationId', ], 'members' => [ 'conversationId' => [ 'shape' => 'ConversationId', 'location' => 'uri', 'locationName' => 'conversationId', ], 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'userId' => [ 'shape' => 'UserId', 'location' => 'querystring', 'locationName' => 'userId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResultsIntegerForListMessages', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListMessagesResponse' => [ 'type' => 'structure', 'members' => [ 'messages' => [ 'shape' => 'Messages', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListPluginsRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResultsIntegerForListPlugins', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListPluginsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'plugins' => [ 'shape' => 'Plugins', ], ], ], 'ListRetrieversRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResultsIntegerForListRetrieversRequest', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListRetrieversResponse' => [ 'type' => 'structure', 'members' => [ 'retrievers' => [ 'shape' => 'Retrievers', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', ], 'members' => [ 'resourceARN' => [ 'shape' => 'AmazonResourceName', 'location' => 'uri', 'locationName' => 'resourceARN', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'Tags', ], ], ], 'ListWebExperiencesRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResultsIntegerForListWebExperiencesRequest', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListWebExperiencesResponse' => [ 'type' => 'structure', 'members' => [ 'webExperiences' => [ 'shape' => 'WebExperiences', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'Long' => [ 'type' => 'long', 'box' => true, ], 'MaxResultsIntegerForGetTopicConfigurations' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 1, ], 'MaxResultsIntegerForListApplications' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxResultsIntegerForListConversations' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxResultsIntegerForListDataSources' => [ 'type' => 'integer', 'box' => true, 'max' => 10, 'min' => 1, ], 'MaxResultsIntegerForListDataSourcesSyncJobs' => [ 'type' => 'integer', 'box' => true, 'max' => 10, 'min' => 1, ], 'MaxResultsIntegerForListDocuments' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxResultsIntegerForListGroupsRequest' => [ 'type' => 'integer', 'box' => true, 'max' => 10, 'min' => 1, ], 'MaxResultsIntegerForListIndices' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxResultsIntegerForListMessages' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxResultsIntegerForListPlugins' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 1, ], 'MaxResultsIntegerForListRetrieversRequest' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 1, ], 'MaxResultsIntegerForListWebExperiencesRequest' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MemberGroup' => [ 'type' => 'structure', 'required' => [ 'groupName', ], 'members' => [ 'groupName' => [ 'shape' => 'GroupName', ], 'type' => [ 'shape' => 'MembershipType', ], ], ], 'MemberGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'MemberGroup', ], 'max' => 1000, 'min' => 1, ], 'MemberRelation' => [ 'type' => 'string', 'enum' => [ 'AND', 'OR', ], ], 'MemberUser' => [ 'type' => 'structure', 'required' => [ 'userId', ], 'members' => [ 'userId' => [ 'shape' => 'DataSourceUserId', ], 'type' => [ 'shape' => 'MembershipType', ], ], ], 'MemberUsers' => [ 'type' => 'list', 'member' => [ 'shape' => 'MemberUser', ], 'max' => 1000, 'min' => 1, ], 'MembershipType' => [ 'type' => 'string', 'enum' => [ 'INDEX', 'DATASOURCE', ], ], 'Message' => [ 'type' => 'structure', 'members' => [ 'messageId' => [ 'shape' => 'String', ], 'body' => [ 'shape' => 'MessageBody', ], 'time' => [ 'shape' => 'Timestamp', ], 'type' => [ 'shape' => 'MessageType', ], 'attachments' => [ 'shape' => 'AttachmentsOutput', ], 'sourceAttribution' => [ 'shape' => 'SourceAttributions', ], 'actionReview' => [ 'shape' => 'ActionReview', ], 'actionExecution' => [ 'shape' => 'ActionExecution', ], ], ], 'MessageBody' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, 'pattern' => '\\P{C}*$}', ], 'MessageId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9-]{35}', ], 'MessageType' => [ 'type' => 'string', 'enum' => [ 'USER', 'SYSTEM', ], ], 'MessageUsefulness' => [ 'type' => 'string', 'enum' => [ 'USEFUL', 'NOT_USEFUL', ], ], 'MessageUsefulnessComment' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, 'pattern' => '\\P{C}*', ], 'MessageUsefulnessFeedback' => [ 'type' => 'structure', 'required' => [ 'usefulness', 'submittedAt', ], 'members' => [ 'usefulness' => [ 'shape' => 'MessageUsefulness', ], 'reason' => [ 'shape' => 'MessageUsefulnessReason', ], 'comment' => [ 'shape' => 'MessageUsefulnessComment', ], 'submittedAt' => [ 'shape' => 'Timestamp', ], ], ], 'MessageUsefulnessReason' => [ 'type' => 'string', 'enum' => [ 'NOT_FACTUALLY_CORRECT', 'HARMFUL_OR_UNSAFE', 'INCORRECT_OR_MISSING_SOURCES', 'NOT_HELPFUL', 'FACTUALLY_CORRECT', 'COMPLETE', 'RELEVANT_SOURCES', 'HELPFUL', 'NOT_BASED_ON_DOCUMENTS', 'NOT_COMPLETE', 'NOT_CONCISE', 'OTHER', ], ], 'Messages' => [ 'type' => 'list', 'member' => [ 'shape' => 'Message', ], ], 'MetadataEvent' => [ 'type' => 'structure', 'members' => [ 'conversationId' => [ 'shape' => 'ConversationId', ], 'userMessageId' => [ 'shape' => 'MessageId', ], 'systemMessageId' => [ 'shape' => 'MessageId', ], 'sourceAttributions' => [ 'shape' => 'SourceAttributions', ], 'finalTextMessage' => [ 'shape' => 'String', ], ], 'event' => true, ], 'MetricValue' => [ 'type' => 'string', 'pattern' => '(([1-9][0-9]*)|0)', ], 'NativeIndexConfiguration' => [ 'type' => 'structure', 'required' => [ 'indexId', ], 'members' => [ 'indexId' => [ 'shape' => 'IndexId', ], 'boostingOverride' => [ 'shape' => 'DocumentAttributeBoostingOverrideMap', ], ], ], 'NextToken' => [ 'type' => 'string', 'max' => 800, 'min' => 1, ], 'NoAuthConfiguration' => [ 'type' => 'structure', 'members' => [], ], 'NumberAttributeBoostingConfiguration' => [ 'type' => 'structure', 'required' => [ 'boostingLevel', ], 'members' => [ 'boostingLevel' => [ 'shape' => 'DocumentAttributeBoostingLevel', ], 'boostingType' => [ 'shape' => 'NumberAttributeBoostingType', ], ], ], 'NumberAttributeBoostingType' => [ 'type' => 'string', 'enum' => [ 'PRIORITIZE_LARGER_VALUES', 'PRIORITIZE_SMALLER_VALUES', ], ], 'OAuth2ClientCredentialConfiguration' => [ 'type' => 'structure', 'required' => [ 'secretArn', 'roleArn', ], 'members' => [ 'secretArn' => [ 'shape' => 'SecretArn', ], 'roleArn' => [ 'shape' => 'RoleArn', ], ], ], 'OpenIDConnectProviderConfiguration' => [ 'type' => 'structure', 'required' => [ 'secretsArn', 'secretsRole', ], 'members' => [ 'secretsArn' => [ 'shape' => 'SecretArn', ], 'secretsRole' => [ 'shape' => 'RoleArn', ], ], ], 'Payload' => [ 'type' => 'string', 'sensitive' => true, ], 'PersonalizationConfiguration' => [ 'type' => 'structure', 'required' => [ 'personalizationControlMode', ], 'members' => [ 'personalizationControlMode' => [ 'shape' => 'PersonalizationControlMode', ], ], ], 'PersonalizationControlMode' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'Plugin' => [ 'type' => 'structure', 'members' => [ 'pluginId' => [ 'shape' => 'PluginId', ], 'displayName' => [ 'shape' => 'PluginName', ], 'type' => [ 'shape' => 'PluginType', ], 'serverUrl' => [ 'shape' => 'Url', ], 'state' => [ 'shape' => 'PluginState', ], 'buildStatus' => [ 'shape' => 'PluginBuildStatus', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'PluginArn' => [ 'type' => 'string', 'max' => 1284, 'min' => 0, 'pattern' => 'arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[^/].{0,1023}', ], 'PluginAuthConfiguration' => [ 'type' => 'structure', 'members' => [ 'basicAuthConfiguration' => [ 'shape' => 'BasicAuthConfiguration', ], 'oAuth2ClientCredentialConfiguration' => [ 'shape' => 'OAuth2ClientCredentialConfiguration', ], 'noAuthConfiguration' => [ 'shape' => 'NoAuthConfiguration', ], ], 'union' => true, ], 'PluginBuildStatus' => [ 'type' => 'string', 'enum' => [ 'READY', 'CREATE_IN_PROGRESS', 'CREATE_FAILED', 'UPDATE_IN_PROGRESS', 'UPDATE_FAILED', 'DELETE_IN_PROGRESS', 'DELETE_FAILED', ], ], 'PluginConfiguration' => [ 'type' => 'structure', 'required' => [ 'pluginId', ], 'members' => [ 'pluginId' => [ 'shape' => 'PluginId', ], ], ], 'PluginDescription' => [ 'type' => 'string', 'max' => 200, 'min' => 1, ], 'PluginId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}', ], 'PluginName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9_-]*', ], 'PluginState' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'PluginType' => [ 'type' => 'string', 'enum' => [ 'SERVICE_NOW', 'SALESFORCE', 'JIRA', 'ZENDESK', 'CUSTOM', ], ], 'Plugins' => [ 'type' => 'list', 'member' => [ 'shape' => 'Plugin', ], ], 'Principal' => [ 'type' => 'structure', 'members' => [ 'user' => [ 'shape' => 'PrincipalUser', ], 'group' => [ 'shape' => 'PrincipalGroup', ], ], 'union' => true, ], 'PrincipalGroup' => [ 'type' => 'structure', 'required' => [ 'access', ], 'members' => [ 'name' => [ 'shape' => 'GroupName', ], 'access' => [ 'shape' => 'ReadAccessType', ], 'membershipType' => [ 'shape' => 'MembershipType', ], ], ], 'PrincipalUser' => [ 'type' => 'structure', 'required' => [ 'access', ], 'members' => [ 'id' => [ 'shape' => 'UserId', ], 'access' => [ 'shape' => 'ReadAccessType', ], 'membershipType' => [ 'shape' => 'MembershipType', ], ], ], 'Principals' => [ 'type' => 'list', 'member' => [ 'shape' => 'Principal', ], ], 'PutFeedbackRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'conversationId', 'messageId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'userId' => [ 'shape' => 'UserId', 'location' => 'querystring', 'locationName' => 'userId', ], 'conversationId' => [ 'shape' => 'ConversationId', 'location' => 'uri', 'locationName' => 'conversationId', ], 'messageId' => [ 'shape' => 'SystemMessageId', 'location' => 'uri', 'locationName' => 'messageId', ], 'messageCopiedAt' => [ 'shape' => 'Timestamp', ], 'messageUsefulness' => [ 'shape' => 'MessageUsefulnessFeedback', ], ], ], 'PutGroupRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'indexId', 'groupName', 'type', 'groupMembers', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], 'groupName' => [ 'shape' => 'GroupName', ], 'dataSourceId' => [ 'shape' => 'DataSourceId', ], 'type' => [ 'shape' => 'MembershipType', ], 'groupMembers' => [ 'shape' => 'GroupMembers', ], ], ], 'PutGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'QAppsConfiguration' => [ 'type' => 'structure', 'required' => [ 'qAppsControlMode', ], 'members' => [ 'qAppsControlMode' => [ 'shape' => 'QAppsControlMode', ], ], ], 'QAppsControlMode' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'ReadAccessType' => [ 'type' => 'string', 'enum' => [ 'ALLOW', 'DENY', ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResponseScope' => [ 'type' => 'string', 'enum' => [ 'ENTERPRISE_CONTENT_ONLY', 'EXTENDED_KNOWLEDGE_ENABLED', ], ], 'Retriever' => [ 'type' => 'structure', 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', ], 'retrieverId' => [ 'shape' => 'RetrieverId', ], 'type' => [ 'shape' => 'RetrieverType', ], 'status' => [ 'shape' => 'RetrieverStatus', ], 'displayName' => [ 'shape' => 'RetrieverName', ], ], ], 'RetrieverArn' => [ 'type' => 'string', 'max' => 1284, 'min' => 0, 'pattern' => 'arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[^/].{0,1023}', ], 'RetrieverConfiguration' => [ 'type' => 'structure', 'members' => [ 'nativeIndexConfiguration' => [ 'shape' => 'NativeIndexConfiguration', ], 'kendraIndexConfiguration' => [ 'shape' => 'KendraIndexConfiguration', ], ], 'union' => true, ], 'RetrieverId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9-]{35}', ], 'RetrieverName' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9_-]*', ], 'RetrieverStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'FAILED', ], ], 'RetrieverType' => [ 'type' => 'string', 'enum' => [ 'NATIVE_INDEX', 'KENDRA_INDEX', ], ], 'Retrievers' => [ 'type' => 'list', 'member' => [ 'shape' => 'Retriever', ], ], 'RoleArn' => [ 'type' => 'string', 'max' => 1284, 'min' => 0, 'pattern' => 'arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[^/].{0,1023}', ], 'Rule' => [ 'type' => 'structure', 'required' => [ 'ruleType', ], 'members' => [ 'includedUsersAndGroups' => [ 'shape' => 'UsersAndGroups', ], 'excludedUsersAndGroups' => [ 'shape' => 'UsersAndGroups', ], 'ruleType' => [ 'shape' => 'RuleType', ], 'ruleConfiguration' => [ 'shape' => 'RuleConfiguration', ], ], ], 'RuleConfiguration' => [ 'type' => 'structure', 'members' => [ 'contentBlockerRule' => [ 'shape' => 'ContentBlockerRule', ], 'contentRetrievalRule' => [ 'shape' => 'ContentRetrievalRule', ], ], 'union' => true, ], 'RuleType' => [ 'type' => 'string', 'enum' => [ 'CONTENT_BLOCKER_RULE', 'CONTENT_RETRIEVAL_RULE', ], ], 'Rules' => [ 'type' => 'list', 'member' => [ 'shape' => 'Rule', ], 'max' => 10, 'min' => 0, ], 'S3' => [ 'type' => 'structure', 'required' => [ 'bucket', 'key', ], 'members' => [ 'bucket' => [ 'shape' => 'S3BucketName', ], 'key' => [ 'shape' => 'S3ObjectKey', ], ], ], 'S3BucketName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '[a-z0-9][\\.\\-a-z0-9]{1,61}[a-z0-9]', ], 'S3ObjectKey' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'SamlAttribute' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'SamlAuthenticationUrl' => [ 'type' => 'string', 'max' => 1284, 'min' => 1, 'pattern' => 'https://.*', ], 'SamlConfiguration' => [ 'type' => 'structure', 'required' => [ 'metadataXML', 'roleArn', 'userIdAttribute', ], 'members' => [ 'metadataXML' => [ 'shape' => 'SamlMetadataXML', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'userIdAttribute' => [ 'shape' => 'SamlAttribute', ], 'userGroupAttribute' => [ 'shape' => 'SamlAttribute', ], ], ], 'SamlMetadataXML' => [ 'type' => 'string', 'max' => 10000000, 'min' => 1000, 'pattern' => '.*', ], 'SamlProviderConfiguration' => [ 'type' => 'structure', 'required' => [ 'authenticationUrl', ], 'members' => [ 'authenticationUrl' => [ 'shape' => 'SamlAuthenticationUrl', ], ], ], 'SecretArn' => [ 'type' => 'string', 'max' => 1284, 'min' => 0, 'pattern' => 'arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[^/].{0,1023}', ], 'SecurityGroupId' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '[-0-9a-zA-Z]+', ], 'SecurityGroupIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupId', ], 'max' => 10, 'min' => 1, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'SnippetExcerpt' => [ 'type' => 'structure', 'members' => [ 'text' => [ 'shape' => 'SnippetExcerptText', ], ], ], 'SnippetExcerptText' => [ 'type' => 'string', ], 'SourceAttribution' => [ 'type' => 'structure', 'members' => [ 'title' => [ 'shape' => 'String', ], 'snippet' => [ 'shape' => 'String', ], 'url' => [ 'shape' => 'String', ], 'citationNumber' => [ 'shape' => 'Integer', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'textMessageSegments' => [ 'shape' => 'TextSegmentList', ], ], ], 'SourceAttributions' => [ 'type' => 'list', 'member' => [ 'shape' => 'SourceAttribution', ], ], 'StartDataSourceSyncJobRequest' => [ 'type' => 'structure', 'required' => [ 'dataSourceId', 'applicationId', 'indexId', ], 'members' => [ 'dataSourceId' => [ 'shape' => 'DataSourceId', 'location' => 'uri', 'locationName' => 'dataSourceId', ], 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], ], ], 'StartDataSourceSyncJobResponse' => [ 'type' => 'structure', 'members' => [ 'executionId' => [ 'shape' => 'ExecutionId', ], ], ], 'Status' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'StopDataSourceSyncJobRequest' => [ 'type' => 'structure', 'required' => [ 'dataSourceId', 'applicationId', 'indexId', ], 'members' => [ 'dataSourceId' => [ 'shape' => 'DataSourceId', 'location' => 'uri', 'locationName' => 'dataSourceId', ], 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], ], ], 'StopDataSourceSyncJobResponse' => [ 'type' => 'structure', 'members' => [], ], 'String' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'StringAttributeBoostingConfiguration' => [ 'type' => 'structure', 'required' => [ 'boostingLevel', ], 'members' => [ 'boostingLevel' => [ 'shape' => 'DocumentAttributeBoostingLevel', ], 'attributeValueBoosting' => [ 'shape' => 'StringAttributeValueBoosting', ], ], ], 'StringAttributeValueBoosting' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'StringAttributeValueBoostingLevel', ], 'max' => 10, 'min' => 1, ], 'StringAttributeValueBoostingLevel' => [ 'type' => 'string', 'enum' => [ 'LOW', 'MEDIUM', 'HIGH', 'VERY_HIGH', ], ], 'StringListAttributeBoostingConfiguration' => [ 'type' => 'structure', 'required' => [ 'boostingLevel', ], 'members' => [ 'boostingLevel' => [ 'shape' => 'DocumentAttributeBoostingLevel', ], ], ], 'SubnetId' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '[-0-9a-zA-Z]+', ], 'SubnetIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubnetId', ], ], 'SubscriptionType' => [ 'type' => 'string', 'enum' => [ 'Q_LITE', 'Q_BUSINESS', ], ], 'SyncSchedule' => [ 'type' => 'string', 'max' => 998, 'min' => 0, 'pattern' => '\\P{C}*', ], 'SystemMessageId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9-]{35}', ], 'SystemMessageOverride' => [ 'type' => 'string', 'max' => 350, 'min' => 0, 'pattern' => '\\P{C}*', ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'key', 'value', ], 'members' => [ 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', 'tags', ], 'members' => [ 'resourceARN' => [ 'shape' => 'AmazonResourceName', 'location' => 'uri', 'locationName' => 'resourceARN', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'Tags' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, 'min' => 0, ], 'TextDocumentStatistics' => [ 'type' => 'structure', 'members' => [ 'indexedTextBytes' => [ 'shape' => 'IndexedTextBytes', ], 'indexedTextDocumentCount' => [ 'shape' => 'IndexedTextDocument', ], ], ], 'TextInputEvent' => [ 'type' => 'structure', 'required' => [ 'userMessage', ], 'members' => [ 'userMessage' => [ 'shape' => 'UserMessage', ], ], 'event' => true, ], 'TextOutputEvent' => [ 'type' => 'structure', 'members' => [ 'conversationId' => [ 'shape' => 'ConversationId', ], 'userMessageId' => [ 'shape' => 'MessageId', ], 'systemMessageId' => [ 'shape' => 'MessageId', ], 'systemMessage' => [ 'shape' => 'String', ], ], 'event' => true, ], 'TextSegment' => [ 'type' => 'structure', 'members' => [ 'beginOffset' => [ 'shape' => 'Integer', ], 'endOffset' => [ 'shape' => 'Integer', ], 'snippetExcerpt' => [ 'shape' => 'SnippetExcerpt', ], ], ], 'TextSegmentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TextSegment', ], ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'Title' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'TopicConfiguration' => [ 'type' => 'structure', 'required' => [ 'name', 'rules', ], 'members' => [ 'name' => [ 'shape' => 'TopicConfigurationName', ], 'description' => [ 'shape' => 'TopicDescription', ], 'exampleChatMessages' => [ 'shape' => 'ExampleChatMessages', ], 'rules' => [ 'shape' => 'Rules', ], ], ], 'TopicConfigurationName' => [ 'type' => 'string', 'max' => 36, 'min' => 1, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9-]{0,35}', ], 'TopicConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'TopicConfiguration', ], 'max' => 10, 'min' => 0, ], 'TopicDescription' => [ 'type' => 'string', 'max' => 350, 'min' => 0, 'pattern' => '\\P{C}*', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', 'tagKeys', ], 'members' => [ 'resourceARN' => [ 'shape' => 'AmazonResourceName', 'location' => 'uri', 'locationName' => 'resourceARN', ], 'tagKeys' => [ 'shape' => 'TagKeys', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'identityCenterInstanceArn' => [ 'shape' => 'InstanceArn', ], 'displayName' => [ 'shape' => 'ApplicationName', ], 'description' => [ 'shape' => 'Description', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'attachmentsConfiguration' => [ 'shape' => 'AttachmentsConfiguration', ], 'qAppsConfiguration' => [ 'shape' => 'QAppsConfiguration', ], 'personalizationConfiguration' => [ 'shape' => 'PersonalizationConfiguration', ], 'autoSubscriptionConfiguration' => [ 'shape' => 'AutoSubscriptionConfiguration', ], ], ], 'UpdateApplicationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateChatControlsConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'responseScope' => [ 'shape' => 'ResponseScope', ], 'blockedPhrasesConfigurationUpdate' => [ 'shape' => 'BlockedPhrasesConfigurationUpdate', ], 'topicConfigurationsToCreateOrUpdate' => [ 'shape' => 'TopicConfigurations', ], 'topicConfigurationsToDelete' => [ 'shape' => 'TopicConfigurations', ], 'creatorModeConfiguration' => [ 'shape' => 'CreatorModeConfiguration', ], ], ], 'UpdateChatControlsConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDataSourceRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'indexId', 'dataSourceId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], 'dataSourceId' => [ 'shape' => 'DataSourceId', 'location' => 'uri', 'locationName' => 'dataSourceId', ], 'displayName' => [ 'shape' => 'DataSourceName', ], 'configuration' => [ 'shape' => 'DataSourceConfiguration', ], 'vpcConfiguration' => [ 'shape' => 'DataSourceVpcConfiguration', ], 'description' => [ 'shape' => 'Description', ], 'syncSchedule' => [ 'shape' => 'SyncSchedule', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'documentEnrichmentConfiguration' => [ 'shape' => 'DocumentEnrichmentConfiguration', ], ], ], 'UpdateDataSourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateIndexRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'indexId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], 'displayName' => [ 'shape' => 'ApplicationName', ], 'description' => [ 'shape' => 'Description', ], 'capacityConfiguration' => [ 'shape' => 'IndexCapacityConfiguration', ], 'documentAttributeConfigurations' => [ 'shape' => 'DocumentAttributeConfigurations', ], ], ], 'UpdateIndexResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdatePluginRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'pluginId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'pluginId' => [ 'shape' => 'PluginId', 'location' => 'uri', 'locationName' => 'pluginId', ], 'displayName' => [ 'shape' => 'PluginName', ], 'state' => [ 'shape' => 'PluginState', ], 'serverUrl' => [ 'shape' => 'Url', ], 'customPluginConfiguration' => [ 'shape' => 'CustomPluginConfiguration', ], 'authConfiguration' => [ 'shape' => 'PluginAuthConfiguration', ], ], ], 'UpdatePluginResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateRetrieverRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'retrieverId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'retrieverId' => [ 'shape' => 'RetrieverId', 'location' => 'uri', 'locationName' => 'retrieverId', ], 'configuration' => [ 'shape' => 'RetrieverConfiguration', ], 'displayName' => [ 'shape' => 'RetrieverName', ], 'roleArn' => [ 'shape' => 'RoleArn', ], ], ], 'UpdateRetrieverResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateUserRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'userId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'userId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'userId', ], 'userAliasesToUpdate' => [ 'shape' => 'UserAliases', ], 'userAliasesToDelete' => [ 'shape' => 'UserAliases', ], ], ], 'UpdateUserResponse' => [ 'type' => 'structure', 'members' => [ 'userAliasesAdded' => [ 'shape' => 'UserAliases', ], 'userAliasesUpdated' => [ 'shape' => 'UserAliases', ], 'userAliasesDeleted' => [ 'shape' => 'UserAliases', ], ], ], 'UpdateWebExperienceRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'webExperienceId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'webExperienceId' => [ 'shape' => 'WebExperienceId', 'location' => 'uri', 'locationName' => 'webExperienceId', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'authenticationConfiguration' => [ 'shape' => 'WebExperienceAuthConfiguration', 'deprecated' => true, 'deprecatedMessage' => 'Property associated with legacy SAML IdP flow. Deprecated in favor of using AWS IAM Identity Center for user management.', ], 'title' => [ 'shape' => 'WebExperienceTitle', ], 'subtitle' => [ 'shape' => 'WebExperienceSubtitle', ], 'welcomeMessage' => [ 'shape' => 'WebExperienceWelcomeMessage', ], 'samplePromptsControlMode' => [ 'shape' => 'WebExperienceSamplePromptsControlMode', ], 'identityProviderConfiguration' => [ 'shape' => 'IdentityProviderConfiguration', ], ], ], 'UpdateWebExperienceResponse' => [ 'type' => 'structure', 'members' => [], ], 'Url' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '(https?|ftp|file)://([^\\s]*)', ], 'UserAlias' => [ 'type' => 'structure', 'required' => [ 'userId', ], 'members' => [ 'indexId' => [ 'shape' => 'IndexId', ], 'dataSourceId' => [ 'shape' => 'DataSourceId', ], 'userId' => [ 'shape' => 'String', ], ], ], 'UserAliases' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserAlias', ], ], 'UserGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'UserId' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '\\P{C}*', ], 'UserIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'UserMessage' => [ 'type' => 'string', 'max' => 7000, 'min' => 1, ], 'UsersAndGroups' => [ 'type' => 'structure', 'members' => [ 'userIds' => [ 'shape' => 'UserIds', ], 'userGroups' => [ 'shape' => 'UserGroups', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', 'reason', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], 'fields' => [ 'shape' => 'ValidationExceptionFields', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'name', 'message', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'CANNOT_PARSE', 'FIELD_VALIDATION_FAILED', 'UNKNOWN_OPERATION', ], ], 'WebExperience' => [ 'type' => 'structure', 'members' => [ 'webExperienceId' => [ 'shape' => 'WebExperienceId', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'defaultEndpoint' => [ 'shape' => 'Url', ], 'status' => [ 'shape' => 'WebExperienceStatus', ], ], ], 'WebExperienceArn' => [ 'type' => 'string', 'max' => 1284, 'min' => 0, 'pattern' => 'arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[^/].{0,1023}', ], 'WebExperienceAuthConfiguration' => [ 'type' => 'structure', 'members' => [ 'samlConfiguration' => [ 'shape' => 'SamlConfiguration', ], ], 'union' => true, ], 'WebExperienceId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9-]*', ], 'WebExperienceSamplePromptsControlMode' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'WebExperienceStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'DELETING', 'FAILED', 'PENDING_AUTH_CONFIG', ], ], 'WebExperienceSubtitle' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'pattern' => '\\P{C}*', ], 'WebExperienceTitle' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'pattern' => '\\P{C}*', ], 'WebExperienceWelcomeMessage' => [ 'type' => 'string', 'max' => 300, 'min' => 0, ], 'WebExperiences' => [ 'type' => 'list', 'member' => [ 'shape' => 'WebExperience', ], ], ],];
