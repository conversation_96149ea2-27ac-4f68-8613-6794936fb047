<?php
// This file was auto-generated from sdk-root/src/data/controltower/2018-05-10/paginators-1.json
return [ 'pagination' => [ 'ListBaselines' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'baselines', ], 'ListControlOperations' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'controlOperations', ], 'ListEnabledBaselines' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'enabledBaselines', ], 'ListEnabledControls' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'enabledControls', ], 'ListLandingZoneOperations' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'landingZoneOperations', ], 'ListLandingZones' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'landingZones', ], ],];
