<?php
// This file was auto-generated from sdk-root/src/data/inspector2/2020-06-08/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-06-08', 'endpointPrefix' => 'inspector2', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceAbbreviation' => 'Inspector2', 'serviceFullName' => 'Inspector2', 'serviceId' => 'Inspector2', 'signatureVersion' => 'v4', 'signingName' => 'inspector2', 'uid' => 'inspector2-2020-06-08', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'AssociateMember' => [ 'name' => 'AssociateMember', 'http' => [ 'method' => 'POST', 'requestUri' => '/members/associate', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateMemberRequest', ], 'output' => [ 'shape' => 'AssociateMemberResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'BatchGetAccountStatus' => [ 'name' => 'BatchGetAccountStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/status/batch/get', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchGetAccountStatusRequest', ], 'output' => [ 'shape' => 'BatchGetAccountStatusResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'BatchGetCodeSnippet' => [ 'name' => 'BatchGetCodeSnippet', 'http' => [ 'method' => 'POST', 'requestUri' => '/codesnippet/batchget', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchGetCodeSnippetRequest', ], 'output' => [ 'shape' => 'BatchGetCodeSnippetResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'BatchGetFindingDetails' => [ 'name' => 'BatchGetFindingDetails', 'http' => [ 'method' => 'POST', 'requestUri' => '/findings/details/batch/get', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchGetFindingDetailsRequest', ], 'output' => [ 'shape' => 'BatchGetFindingDetailsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'BatchGetFreeTrialInfo' => [ 'name' => 'BatchGetFreeTrialInfo', 'http' => [ 'method' => 'POST', 'requestUri' => '/freetrialinfo/batchget', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchGetFreeTrialInfoRequest', ], 'output' => [ 'shape' => 'BatchGetFreeTrialInfoResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'BatchGetMemberEc2DeepInspectionStatus' => [ 'name' => 'BatchGetMemberEc2DeepInspectionStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/ec2deepinspectionstatus/member/batch/get', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchGetMemberEc2DeepInspectionStatusRequest', ], 'output' => [ 'shape' => 'BatchGetMemberEc2DeepInspectionStatusResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'BatchUpdateMemberEc2DeepInspectionStatus' => [ 'name' => 'BatchUpdateMemberEc2DeepInspectionStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/ec2deepinspectionstatus/member/batch/update', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchUpdateMemberEc2DeepInspectionStatusRequest', ], 'output' => [ 'shape' => 'BatchUpdateMemberEc2DeepInspectionStatusResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CancelFindingsReport' => [ 'name' => 'CancelFindingsReport', 'http' => [ 'method' => 'POST', 'requestUri' => '/reporting/cancel', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CancelFindingsReportRequest', ], 'output' => [ 'shape' => 'CancelFindingsReportResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CancelSbomExport' => [ 'name' => 'CancelSbomExport', 'http' => [ 'method' => 'POST', 'requestUri' => '/sbomexport/cancel', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CancelSbomExportRequest', ], 'output' => [ 'shape' => 'CancelSbomExportResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateCisScanConfiguration' => [ 'name' => 'CreateCisScanConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/cis/scan-configuration/create', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateCisScanConfigurationRequest', ], 'output' => [ 'shape' => 'CreateCisScanConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateFilter' => [ 'name' => 'CreateFilter', 'http' => [ 'method' => 'POST', 'requestUri' => '/filters/create', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateFilterRequest', ], 'output' => [ 'shape' => 'CreateFilterResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateFindingsReport' => [ 'name' => 'CreateFindingsReport', 'http' => [ 'method' => 'POST', 'requestUri' => '/reporting/create', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateFindingsReportRequest', ], 'output' => [ 'shape' => 'CreateFindingsReportResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateSbomExport' => [ 'name' => 'CreateSbomExport', 'http' => [ 'method' => 'POST', 'requestUri' => '/sbomexport/create', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateSbomExportRequest', ], 'output' => [ 'shape' => 'CreateSbomExportResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteCisScanConfiguration' => [ 'name' => 'DeleteCisScanConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/cis/scan-configuration/delete', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteCisScanConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteCisScanConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteFilter' => [ 'name' => 'DeleteFilter', 'http' => [ 'method' => 'POST', 'requestUri' => '/filters/delete', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteFilterRequest', ], 'output' => [ 'shape' => 'DeleteFilterResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeOrganizationConfiguration' => [ 'name' => 'DescribeOrganizationConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/organizationconfiguration/describe', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeOrganizationConfigurationRequest', ], 'output' => [ 'shape' => 'DescribeOrganizationConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'Disable' => [ 'name' => 'Disable', 'http' => [ 'method' => 'POST', 'requestUri' => '/disable', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisableRequest', ], 'output' => [ 'shape' => 'DisableResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DisableDelegatedAdminAccount' => [ 'name' => 'DisableDelegatedAdminAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/delegatedadminaccounts/disable', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisableDelegatedAdminAccountRequest', ], 'output' => [ 'shape' => 'DisableDelegatedAdminAccountResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DisassociateMember' => [ 'name' => 'DisassociateMember', 'http' => [ 'method' => 'POST', 'requestUri' => '/members/disassociate', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateMemberRequest', ], 'output' => [ 'shape' => 'DisassociateMemberResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'Enable' => [ 'name' => 'Enable', 'http' => [ 'method' => 'POST', 'requestUri' => '/enable', 'responseCode' => 200, ], 'input' => [ 'shape' => 'EnableRequest', ], 'output' => [ 'shape' => 'EnableResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'EnableDelegatedAdminAccount' => [ 'name' => 'EnableDelegatedAdminAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/delegatedadminaccounts/enable', 'responseCode' => 200, ], 'input' => [ 'shape' => 'EnableDelegatedAdminAccountRequest', ], 'output' => [ 'shape' => 'EnableDelegatedAdminAccountResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetCisScanReport' => [ 'name' => 'GetCisScanReport', 'http' => [ 'method' => 'POST', 'requestUri' => '/cis/scan/report/get', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCisScanReportRequest', ], 'output' => [ 'shape' => 'GetCisScanReportResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetCisScanResultDetails' => [ 'name' => 'GetCisScanResultDetails', 'http' => [ 'method' => 'POST', 'requestUri' => '/cis/scan-result/details/get', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCisScanResultDetailsRequest', ], 'output' => [ 'shape' => 'GetCisScanResultDetailsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetConfiguration' => [ 'name' => 'GetConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/configuration/get', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetConfigurationRequest', ], 'output' => [ 'shape' => 'GetConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetDelegatedAdminAccount' => [ 'name' => 'GetDelegatedAdminAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/delegatedadminaccounts/get', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDelegatedAdminAccountRequest', ], 'output' => [ 'shape' => 'GetDelegatedAdminAccountResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetEc2DeepInspectionConfiguration' => [ 'name' => 'GetEc2DeepInspectionConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/ec2deepinspectionconfiguration/get', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetEc2DeepInspectionConfigurationRequest', ], 'output' => [ 'shape' => 'GetEc2DeepInspectionConfigurationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetEncryptionKey' => [ 'name' => 'GetEncryptionKey', 'http' => [ 'method' => 'GET', 'requestUri' => '/encryptionkey/get', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetEncryptionKeyRequest', ], 'output' => [ 'shape' => 'GetEncryptionKeyResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetFindingsReportStatus' => [ 'name' => 'GetFindingsReportStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/reporting/status/get', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFindingsReportStatusRequest', ], 'output' => [ 'shape' => 'GetFindingsReportStatusResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetMember' => [ 'name' => 'GetMember', 'http' => [ 'method' => 'POST', 'requestUri' => '/members/get', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMemberRequest', ], 'output' => [ 'shape' => 'GetMemberResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetSbomExport' => [ 'name' => 'GetSbomExport', 'http' => [ 'method' => 'POST', 'requestUri' => '/sbomexport/get', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSbomExportRequest', ], 'output' => [ 'shape' => 'GetSbomExportResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'ListAccountPermissions' => [ 'name' => 'ListAccountPermissions', 'http' => [ 'method' => 'POST', 'requestUri' => '/accountpermissions/list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAccountPermissionsRequest', ], 'output' => [ 'shape' => 'ListAccountPermissionsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListCisScanConfigurations' => [ 'name' => 'ListCisScanConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/cis/scan-configuration/list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCisScanConfigurationsRequest', ], 'output' => [ 'shape' => 'ListCisScanConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListCisScanResultsAggregatedByChecks' => [ 'name' => 'ListCisScanResultsAggregatedByChecks', 'http' => [ 'method' => 'POST', 'requestUri' => '/cis/scan-result/check/list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCisScanResultsAggregatedByChecksRequest', ], 'output' => [ 'shape' => 'ListCisScanResultsAggregatedByChecksResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListCisScanResultsAggregatedByTargetResource' => [ 'name' => 'ListCisScanResultsAggregatedByTargetResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/cis/scan-result/resource/list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCisScanResultsAggregatedByTargetResourceRequest', ], 'output' => [ 'shape' => 'ListCisScanResultsAggregatedByTargetResourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListCisScans' => [ 'name' => 'ListCisScans', 'http' => [ 'method' => 'POST', 'requestUri' => '/cis/scan/list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCisScansRequest', ], 'output' => [ 'shape' => 'ListCisScansResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListCoverage' => [ 'name' => 'ListCoverage', 'http' => [ 'method' => 'POST', 'requestUri' => '/coverage/list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCoverageRequest', ], 'output' => [ 'shape' => 'ListCoverageResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListCoverageStatistics' => [ 'name' => 'ListCoverageStatistics', 'http' => [ 'method' => 'POST', 'requestUri' => '/coverage/statistics/list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCoverageStatisticsRequest', ], 'output' => [ 'shape' => 'ListCoverageStatisticsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListDelegatedAdminAccounts' => [ 'name' => 'ListDelegatedAdminAccounts', 'http' => [ 'method' => 'POST', 'requestUri' => '/delegatedadminaccounts/list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDelegatedAdminAccountsRequest', ], 'output' => [ 'shape' => 'ListDelegatedAdminAccountsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListFilters' => [ 'name' => 'ListFilters', 'http' => [ 'method' => 'POST', 'requestUri' => '/filters/list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFiltersRequest', ], 'output' => [ 'shape' => 'ListFiltersResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListFindingAggregations' => [ 'name' => 'ListFindingAggregations', 'http' => [ 'method' => 'POST', 'requestUri' => '/findings/aggregation/list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFindingAggregationsRequest', ], 'output' => [ 'shape' => 'ListFindingAggregationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListFindings' => [ 'name' => 'ListFindings', 'http' => [ 'method' => 'POST', 'requestUri' => '/findings/list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFindingsRequest', ], 'output' => [ 'shape' => 'ListFindingsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListMembers' => [ 'name' => 'ListMembers', 'http' => [ 'method' => 'POST', 'requestUri' => '/members/list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMembersRequest', ], 'output' => [ 'shape' => 'ListMembersResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListUsageTotals' => [ 'name' => 'ListUsageTotals', 'http' => [ 'method' => 'POST', 'requestUri' => '/usage/list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListUsageTotalsRequest', ], 'output' => [ 'shape' => 'ListUsageTotalsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ResetEncryptionKey' => [ 'name' => 'ResetEncryptionKey', 'http' => [ 'method' => 'PUT', 'requestUri' => '/encryptionkey/reset', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ResetEncryptionKeyRequest', ], 'output' => [ 'shape' => 'ResetEncryptionKeyResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'SearchVulnerabilities' => [ 'name' => 'SearchVulnerabilities', 'http' => [ 'method' => 'POST', 'requestUri' => '/vulnerabilities/search', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchVulnerabilitiesRequest', ], 'output' => [ 'shape' => 'SearchVulnerabilitiesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'SendCisSessionHealth' => [ 'name' => 'SendCisSessionHealth', 'http' => [ 'method' => 'PUT', 'requestUri' => '/cissession/health/send', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SendCisSessionHealthRequest', ], 'output' => [ 'shape' => 'SendCisSessionHealthResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'SendCisSessionTelemetry' => [ 'name' => 'SendCisSessionTelemetry', 'http' => [ 'method' => 'PUT', 'requestUri' => '/cissession/telemetry/send', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SendCisSessionTelemetryRequest', ], 'output' => [ 'shape' => 'SendCisSessionTelemetryResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'StartCisSession' => [ 'name' => 'StartCisSession', 'http' => [ 'method' => 'PUT', 'requestUri' => '/cissession/start', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartCisSessionRequest', ], 'output' => [ 'shape' => 'StartCisSessionResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'StopCisSession' => [ 'name' => 'StopCisSession', 'http' => [ 'method' => 'PUT', 'requestUri' => '/cissession/stop', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopCisSessionRequest', ], 'output' => [ 'shape' => 'StopCisSessionResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateCisScanConfiguration' => [ 'name' => 'UpdateCisScanConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/cis/scan-configuration/update', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateCisScanConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateCisScanConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateConfiguration' => [ 'name' => 'UpdateConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/configuration/update', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateEc2DeepInspectionConfiguration' => [ 'name' => 'UpdateEc2DeepInspectionConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/ec2deepinspectionconfiguration/update', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateEc2DeepInspectionConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateEc2DeepInspectionConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateEncryptionKey' => [ 'name' => 'UpdateEncryptionKey', 'http' => [ 'method' => 'PUT', 'requestUri' => '/encryptionkey/update', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateEncryptionKeyRequest', ], 'output' => [ 'shape' => 'UpdateEncryptionKeyResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UpdateFilter' => [ 'name' => 'UpdateFilter', 'http' => [ 'method' => 'POST', 'requestUri' => '/filters/update', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateFilterRequest', ], 'output' => [ 'shape' => 'UpdateFilterResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateOrgEc2DeepInspectionConfiguration' => [ 'name' => 'UpdateOrgEc2DeepInspectionConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/ec2deepinspectionconfiguration/org/update', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateOrgEc2DeepInspectionConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateOrgEc2DeepInspectionConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateOrganizationConfiguration' => [ 'name' => 'UpdateOrganizationConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/organizationconfiguration/update', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateOrganizationConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateOrganizationConfigurationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'Account' => [ 'type' => 'structure', 'required' => [ 'accountId', 'resourceStatus', 'status', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'resourceStatus' => [ 'shape' => 'ResourceStatus', ], 'status' => [ 'shape' => 'Status', ], ], ], 'AccountAggregation' => [ 'type' => 'structure', 'members' => [ 'findingType' => [ 'shape' => 'AggregationFindingType', ], 'resourceType' => [ 'shape' => 'AggregationResourceType', ], 'sortBy' => [ 'shape' => 'AccountSortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'AccountAggregationResponse' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'exploitAvailableCount' => [ 'shape' => 'Long', ], 'fixAvailableCount' => [ 'shape' => 'Long', ], 'severityCounts' => [ 'shape' => 'SeverityCounts', ], ], ], 'AccountId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '^\\d{12}$', ], 'AccountIdFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CisStringFilter', ], 'max' => 10, 'min' => 1, ], 'AccountIdSet' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountId', ], 'max' => 100, 'min' => 0, ], 'AccountList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Account', ], ], 'AccountSortBy' => [ 'type' => 'string', 'enum' => [ 'CRITICAL', 'HIGH', 'ALL', ], ], 'AccountState' => [ 'type' => 'structure', 'required' => [ 'accountId', 'resourceState', 'state', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'resourceState' => [ 'shape' => 'ResourceState', ], 'state' => [ 'shape' => 'State', ], ], ], 'AccountStateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountState', ], 'max' => 100, 'min' => 0, ], 'AggCounts' => [ 'type' => 'long', ], 'AggregationFindingType' => [ 'type' => 'string', 'enum' => [ 'NETWORK_REACHABILITY', 'PACKAGE_VULNERABILITY', 'CODE_VULNERABILITY', ], ], 'AggregationRequest' => [ 'type' => 'structure', 'members' => [ 'accountAggregation' => [ 'shape' => 'AccountAggregation', ], 'amiAggregation' => [ 'shape' => 'AmiAggregation', ], 'awsEcrContainerAggregation' => [ 'shape' => 'AwsEcrContainerAggregation', ], 'ec2InstanceAggregation' => [ 'shape' => 'Ec2InstanceAggregation', ], 'findingTypeAggregation' => [ 'shape' => 'FindingTypeAggregation', ], 'imageLayerAggregation' => [ 'shape' => 'ImageLayerAggregation', ], 'lambdaFunctionAggregation' => [ 'shape' => 'LambdaFunctionAggregation', ], 'lambdaLayerAggregation' => [ 'shape' => 'LambdaLayerAggregation', ], 'packageAggregation' => [ 'shape' => 'PackageAggregation', ], 'repositoryAggregation' => [ 'shape' => 'RepositoryAggregation', ], 'titleAggregation' => [ 'shape' => 'TitleAggregation', ], ], 'union' => true, ], 'AggregationResourceType' => [ 'type' => 'string', 'enum' => [ 'AWS_EC2_INSTANCE', 'AWS_ECR_CONTAINER_IMAGE', 'AWS_LAMBDA_FUNCTION', ], ], 'AggregationResponse' => [ 'type' => 'structure', 'members' => [ 'accountAggregation' => [ 'shape' => 'AccountAggregationResponse', ], 'amiAggregation' => [ 'shape' => 'AmiAggregationResponse', ], 'awsEcrContainerAggregation' => [ 'shape' => 'AwsEcrContainerAggregationResponse', ], 'ec2InstanceAggregation' => [ 'shape' => 'Ec2InstanceAggregationResponse', ], 'findingTypeAggregation' => [ 'shape' => 'FindingTypeAggregationResponse', ], 'imageLayerAggregation' => [ 'shape' => 'ImageLayerAggregationResponse', ], 'lambdaFunctionAggregation' => [ 'shape' => 'LambdaFunctionAggregationResponse', ], 'lambdaLayerAggregation' => [ 'shape' => 'LambdaLayerAggregationResponse', ], 'packageAggregation' => [ 'shape' => 'PackageAggregationResponse', ], 'repositoryAggregation' => [ 'shape' => 'RepositoryAggregationResponse', ], 'titleAggregation' => [ 'shape' => 'TitleAggregationResponse', ], ], 'union' => true, ], 'AggregationResponseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AggregationResponse', ], ], 'AggregationType' => [ 'type' => 'string', 'enum' => [ 'FINDING_TYPE', 'PACKAGE', 'TITLE', 'REPOSITORY', 'AMI', 'AWS_EC2_INSTANCE', 'AWS_ECR_CONTAINER', 'IMAGE_LAYER', 'ACCOUNT', 'AWS_LAMBDA_FUNCTION', 'LAMBDA_LAYER', ], ], 'AmiAggregation' => [ 'type' => 'structure', 'members' => [ 'amis' => [ 'shape' => 'StringFilterList', ], 'sortBy' => [ 'shape' => 'AmiSortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'AmiAggregationResponse' => [ 'type' => 'structure', 'required' => [ 'ami', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'affectedInstances' => [ 'shape' => 'Long', ], 'ami' => [ 'shape' => 'AmiId', ], 'severityCounts' => [ 'shape' => 'SeverityCounts', ], ], ], 'AmiId' => [ 'type' => 'string', 'pattern' => '^ami-([a-z0-9]{8}|[a-z0-9]{17}|\\*)$', ], 'AmiSortBy' => [ 'type' => 'string', 'enum' => [ 'CRITICAL', 'HIGH', 'ALL', 'AFFECTED_INSTANCES', ], ], 'Architecture' => [ 'type' => 'string', 'enum' => [ 'X86_64', 'ARM64', ], ], 'ArchitectureList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Architecture', ], 'max' => 1, 'min' => 1, ], 'Arn' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, ], 'AssociateMemberRequest' => [ 'type' => 'structure', 'required' => [ 'accountId', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], ], ], 'AssociateMemberResponse' => [ 'type' => 'structure', 'required' => [ 'accountId', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], ], ], 'AtigData' => [ 'type' => 'structure', 'members' => [ 'firstSeen' => [ 'shape' => 'FirstSeen', ], 'lastSeen' => [ 'shape' => 'LastSeen', ], 'targets' => [ 'shape' => 'Targets', ], 'ttps' => [ 'shape' => 'Ttps', ], ], ], 'AutoEnable' => [ 'type' => 'structure', 'required' => [ 'ec2', 'ecr', ], 'members' => [ 'ec2' => [ 'shape' => 'Boolean', ], 'ecr' => [ 'shape' => 'Boolean', ], 'lambda' => [ 'shape' => 'Boolean', ], 'lambdaCode' => [ 'shape' => 'Boolean', ], ], ], 'AwsEc2InstanceDetails' => [ 'type' => 'structure', 'members' => [ 'iamInstanceProfileArn' => [ 'shape' => 'NonEmptyString', ], 'imageId' => [ 'shape' => 'NonEmptyString', ], 'ipV4Addresses' => [ 'shape' => 'IpV4AddressList', ], 'ipV6Addresses' => [ 'shape' => 'IpV6AddressList', ], 'keyName' => [ 'shape' => 'NonEmptyString', ], 'launchedAt' => [ 'shape' => 'DateTimeTimestamp', ], 'platform' => [ 'shape' => 'Platform', ], 'subnetId' => [ 'shape' => 'NonEmptyString', ], 'type' => [ 'shape' => 'NonEmptyString', ], 'vpcId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcrContainerAggregation' => [ 'type' => 'structure', 'members' => [ 'architectures' => [ 'shape' => 'StringFilterList', ], 'imageShas' => [ 'shape' => 'StringFilterList', ], 'imageTags' => [ 'shape' => 'StringFilterList', ], 'repositories' => [ 'shape' => 'StringFilterList', ], 'resourceIds' => [ 'shape' => 'StringFilterList', ], 'sortBy' => [ 'shape' => 'AwsEcrContainerSortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'AwsEcrContainerAggregationResponse' => [ 'type' => 'structure', 'required' => [ 'resourceId', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'architecture' => [ 'shape' => 'String', ], 'imageSha' => [ 'shape' => 'String', ], 'imageTags' => [ 'shape' => 'StringList', ], 'repository' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'NonEmptyString', ], 'severityCounts' => [ 'shape' => 'SeverityCounts', ], ], ], 'AwsEcrContainerImageDetails' => [ 'type' => 'structure', 'required' => [ 'imageHash', 'registry', 'repositoryName', ], 'members' => [ 'architecture' => [ 'shape' => 'NonEmptyString', ], 'author' => [ 'shape' => 'String', ], 'imageHash' => [ 'shape' => 'ImageHash', ], 'imageTags' => [ 'shape' => 'ImageTagList', ], 'platform' => [ 'shape' => 'Platform', ], 'pushedAt' => [ 'shape' => 'DateTimeTimestamp', ], 'registry' => [ 'shape' => 'NonEmptyString', ], 'repositoryName' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcrContainerSortBy' => [ 'type' => 'string', 'enum' => [ 'CRITICAL', 'HIGH', 'ALL', ], ], 'AwsLambdaFunctionDetails' => [ 'type' => 'structure', 'required' => [ 'codeSha256', 'executionRoleArn', 'functionName', 'runtime', 'version', ], 'members' => [ 'architectures' => [ 'shape' => 'ArchitectureList', ], 'codeSha256' => [ 'shape' => 'NonEmptyString', ], 'executionRoleArn' => [ 'shape' => 'ExecutionRoleArn', ], 'functionName' => [ 'shape' => 'FunctionName', ], 'lastModifiedAt' => [ 'shape' => 'Timestamp', ], 'layers' => [ 'shape' => 'LayerList', ], 'packageType' => [ 'shape' => 'PackageType', ], 'runtime' => [ 'shape' => 'Runtime', ], 'version' => [ 'shape' => 'Version', ], 'vpcConfig' => [ 'shape' => 'LambdaVpcConfig', ], ], ], 'BadRequestException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'BatchGetAccountStatusRequest' => [ 'type' => 'structure', 'members' => [ 'accountIds' => [ 'shape' => 'AccountIdSet', ], ], ], 'BatchGetAccountStatusResponse' => [ 'type' => 'structure', 'required' => [ 'accounts', ], 'members' => [ 'accounts' => [ 'shape' => 'AccountStateList', ], 'failedAccounts' => [ 'shape' => 'FailedAccountList', ], ], ], 'BatchGetCodeSnippetRequest' => [ 'type' => 'structure', 'required' => [ 'findingArns', ], 'members' => [ 'findingArns' => [ 'shape' => 'BatchGetCodeSnippetRequestFindingArnsList', ], ], ], 'BatchGetCodeSnippetRequestFindingArnsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FindingArn', ], 'max' => 10, 'min' => 1, ], 'BatchGetCodeSnippetResponse' => [ 'type' => 'structure', 'members' => [ 'codeSnippetResults' => [ 'shape' => 'CodeSnippetResultList', ], 'errors' => [ 'shape' => 'CodeSnippetErrorList', ], ], ], 'BatchGetFindingDetailsRequest' => [ 'type' => 'structure', 'required' => [ 'findingArns', ], 'members' => [ 'findingArns' => [ 'shape' => 'FindingArnList', ], ], ], 'BatchGetFindingDetailsResponse' => [ 'type' => 'structure', 'members' => [ 'errors' => [ 'shape' => 'FindingDetailsErrorList', ], 'findingDetails' => [ 'shape' => 'FindingDetails', ], ], ], 'BatchGetFreeTrialInfoRequest' => [ 'type' => 'structure', 'required' => [ 'accountIds', ], 'members' => [ 'accountIds' => [ 'shape' => 'BatchGetFreeTrialInfoRequestAccountIdsList', ], ], ], 'BatchGetFreeTrialInfoRequestAccountIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MeteringAccountId', ], 'max' => 100, 'min' => 1, ], 'BatchGetFreeTrialInfoResponse' => [ 'type' => 'structure', 'required' => [ 'accounts', 'failedAccounts', ], 'members' => [ 'accounts' => [ 'shape' => 'FreeTrialAccountInfoList', ], 'failedAccounts' => [ 'shape' => 'FreeTrialInfoErrorList', ], ], ], 'BatchGetMemberEc2DeepInspectionStatusRequest' => [ 'type' => 'structure', 'members' => [ 'accountIds' => [ 'shape' => 'AccountIdSet', ], ], ], 'BatchGetMemberEc2DeepInspectionStatusResponse' => [ 'type' => 'structure', 'members' => [ 'accountIds' => [ 'shape' => 'MemberAccountEc2DeepInspectionStatusStateList', ], 'failedAccountIds' => [ 'shape' => 'FailedMemberAccountEc2DeepInspectionStatusStateList', ], ], ], 'BatchUpdateMemberEc2DeepInspectionStatusRequest' => [ 'type' => 'structure', 'required' => [ 'accountIds', ], 'members' => [ 'accountIds' => [ 'shape' => 'MemberAccountEc2DeepInspectionStatusList', ], ], ], 'BatchUpdateMemberEc2DeepInspectionStatusResponse' => [ 'type' => 'structure', 'members' => [ 'accountIds' => [ 'shape' => 'MemberAccountEc2DeepInspectionStatusStateList', ], 'failedAccountIds' => [ 'shape' => 'FailedMemberAccountEc2DeepInspectionStatusStateList', ], ], ], 'BenchmarkProfile' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'BenchmarkVersion' => [ 'type' => 'string', 'max' => 8, 'min' => 0, ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'CancelFindingsReportRequest' => [ 'type' => 'structure', 'required' => [ 'reportId', ], 'members' => [ 'reportId' => [ 'shape' => 'ReportId', ], ], ], 'CancelFindingsReportResponse' => [ 'type' => 'structure', 'required' => [ 'reportId', ], 'members' => [ 'reportId' => [ 'shape' => 'ReportId', ], ], ], 'CancelSbomExportRequest' => [ 'type' => 'structure', 'required' => [ 'reportId', ], 'members' => [ 'reportId' => [ 'shape' => 'ReportId', ], ], ], 'CancelSbomExportResponse' => [ 'type' => 'structure', 'members' => [ 'reportId' => [ 'shape' => 'ReportId', ], ], ], 'CheckCount' => [ 'type' => 'integer', 'max' => 65536, 'min' => 0, ], 'CheckIdFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CisStringFilter', ], 'max' => 10, 'min' => 1, ], 'CisAccountIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountId', ], 'max' => 10000, 'min' => 1, ], 'CisCheckAggregation' => [ 'type' => 'structure', 'required' => [ 'scanArn', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'checkDescription' => [ 'shape' => 'String', ], 'checkId' => [ 'shape' => 'String', ], 'level' => [ 'shape' => 'CisSecurityLevel', ], 'platform' => [ 'shape' => 'String', ], 'scanArn' => [ 'shape' => 'CisScanArn', ], 'statusCounts' => [ 'shape' => 'StatusCounts', ], 'title' => [ 'shape' => 'String', ], ], ], 'CisCheckAggregationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CisCheckAggregation', ], 'max' => 1000, 'min' => 1, ], 'CisDateFilter' => [ 'type' => 'structure', 'members' => [ 'earliestScanStartTime' => [ 'shape' => 'Timestamp', ], 'latestScanStartTime' => [ 'shape' => 'Timestamp', ], ], ], 'CisFindingArn' => [ 'type' => 'string', 'pattern' => '^arn:aws(-gov|-cn)?:inspector2:[-.a-z0-9]{0,20}:\\d{12}:owner/\\d{12}/cis-finding/[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$', ], 'CisFindingArnFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CisStringFilter', ], 'max' => 10, 'min' => 1, ], 'CisFindingStatus' => [ 'type' => 'string', 'enum' => [ 'PASSED', 'FAILED', 'SKIPPED', ], ], 'CisFindingStatusComparison' => [ 'type' => 'string', 'enum' => [ 'EQUALS', ], ], 'CisFindingStatusFilter' => [ 'type' => 'structure', 'required' => [ 'comparison', 'value', ], 'members' => [ 'comparison' => [ 'shape' => 'CisFindingStatusComparison', ], 'value' => [ 'shape' => 'CisFindingStatus', ], ], ], 'CisFindingStatusFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CisFindingStatusFilter', ], 'max' => 10, 'min' => 1, ], 'CisNumberFilter' => [ 'type' => 'structure', 'members' => [ 'lowerInclusive' => [ 'shape' => 'Integer', ], 'upperInclusive' => [ 'shape' => 'Integer', ], ], ], 'CisNumberFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CisNumberFilter', ], 'max' => 10, 'min' => 1, ], 'CisOwnerId' => [ 'type' => 'string', 'pattern' => '^\\d{12}|o-[a-z0-9]{10,32}$', ], 'CisReportFormat' => [ 'type' => 'string', 'enum' => [ 'PDF', 'CSV', ], ], 'CisReportStatus' => [ 'type' => 'string', 'enum' => [ 'SUCCEEDED', 'FAILED', 'IN_PROGRESS', ], ], 'CisResultStatus' => [ 'type' => 'string', 'enum' => [ 'PASSED', 'FAILED', 'SKIPPED', ], ], 'CisResultStatusComparison' => [ 'type' => 'string', 'enum' => [ 'EQUALS', ], ], 'CisResultStatusFilter' => [ 'type' => 'structure', 'required' => [ 'comparison', 'value', ], 'members' => [ 'comparison' => [ 'shape' => 'CisResultStatusComparison', ], 'value' => [ 'shape' => 'CisResultStatus', ], ], ], 'CisResultStatusFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CisResultStatusFilter', ], 'max' => 10, 'min' => 1, ], 'CisRuleDetails' => [ 'type' => 'blob', 'max' => 1000, 'min' => 0, ], 'CisRuleStatus' => [ 'type' => 'string', 'enum' => [ 'FAILED', 'PASSED', 'NOT_EVALUATED', 'INFORMATIONAL', 'UNKNOWN', 'NOT_APPLICABLE', 'ERROR', ], ], 'CisScan' => [ 'type' => 'structure', 'required' => [ 'scanArn', 'scanConfigurationArn', ], 'members' => [ 'failedChecks' => [ 'shape' => 'Integer', ], 'scanArn' => [ 'shape' => 'CisScanArn', ], 'scanConfigurationArn' => [ 'shape' => 'CisScanConfigurationArn', ], 'scanDate' => [ 'shape' => 'Timestamp', ], 'scanName' => [ 'shape' => 'CisScanName', ], 'scheduledBy' => [ 'shape' => 'String', ], 'securityLevel' => [ 'shape' => 'CisSecurityLevel', ], 'status' => [ 'shape' => 'CisScanStatus', ], 'targets' => [ 'shape' => 'CisTargets', ], 'totalChecks' => [ 'shape' => 'Integer', ], ], ], 'CisScanArn' => [ 'type' => 'string', 'pattern' => '^arn:aws(-us-gov|-cn)?:inspector2:[-.a-z0-9]{0,20}:\\d{12}:owner/(\\d{12}|o-[a-z0-9]{10,32})/cis-scan/[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$', ], 'CisScanArnFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CisStringFilter', ], 'max' => 10, 'min' => 1, ], 'CisScanConfiguration' => [ 'type' => 'structure', 'required' => [ 'scanConfigurationArn', ], 'members' => [ 'ownerId' => [ 'shape' => 'CisOwnerId', ], 'scanConfigurationArn' => [ 'shape' => 'CisScanConfigurationArn', ], 'scanName' => [ 'shape' => 'CisScanName', ], 'schedule' => [ 'shape' => 'Schedule', ], 'securityLevel' => [ 'shape' => 'CisSecurityLevel', ], 'tags' => [ 'shape' => 'CisTagMap', ], 'targets' => [ 'shape' => 'CisTargets', ], ], ], 'CisScanConfigurationArn' => [ 'type' => 'string', 'pattern' => '^arn:aws(-us-gov|-cn)?:inspector2:[a-z]{2}(-gov)?-[a-z]+-[0-9]{1}:[0-9]{12}:owner/(o-[a-z0-9]+|[0-9]{12})/cis-configuration/[0-9a-fA-F-]+$', ], 'CisScanConfigurationArnFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CisStringFilter', ], 'max' => 10, 'min' => 1, ], 'CisScanConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CisScanConfiguration', ], 'max' => 100, 'min' => 0, ], 'CisScanConfigurationsSortBy' => [ 'type' => 'string', 'enum' => [ 'SCAN_NAME', 'SCAN_CONFIGURATION_ARN', ], ], 'CisScanDateFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CisDateFilter', ], 'max' => 1, 'min' => 1, ], 'CisScanList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CisScan', ], 'max' => 50, 'min' => 0, ], 'CisScanName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'CisScanNameFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CisStringFilter', ], 'max' => 10, 'min' => 1, ], 'CisScanResultDetails' => [ 'type' => 'structure', 'required' => [ 'scanArn', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'checkDescription' => [ 'shape' => 'String', ], 'checkId' => [ 'shape' => 'String', ], 'findingArn' => [ 'shape' => 'CisFindingArn', ], 'level' => [ 'shape' => 'CisSecurityLevel', ], 'platform' => [ 'shape' => 'String', ], 'remediation' => [ 'shape' => 'String', ], 'scanArn' => [ 'shape' => 'CisScanArn', ], 'status' => [ 'shape' => 'CisFindingStatus', ], 'statusReason' => [ 'shape' => 'String', ], 'targetResourceId' => [ 'shape' => 'ResourceId', ], 'title' => [ 'shape' => 'String', ], ], ], 'CisScanResultDetailsFilterCriteria' => [ 'type' => 'structure', 'members' => [ 'checkIdFilters' => [ 'shape' => 'CheckIdFilterList', ], 'findingArnFilters' => [ 'shape' => 'CisFindingArnFilterList', ], 'findingStatusFilters' => [ 'shape' => 'CisFindingStatusFilterList', ], 'securityLevelFilters' => [ 'shape' => 'CisSecurityLevelFilterList', ], 'titleFilters' => [ 'shape' => 'TitleFilterList', ], ], ], 'CisScanResultDetailsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CisScanResultDetails', ], 'max' => 1000, 'min' => 1, ], 'CisScanResultDetailsSortBy' => [ 'type' => 'string', 'enum' => [ 'CHECK_ID', 'STATUS', ], ], 'CisScanResultsAggregatedByChecksFilterCriteria' => [ 'type' => 'structure', 'members' => [ 'accountIdFilters' => [ 'shape' => 'OneAccountIdFilterList', ], 'checkIdFilters' => [ 'shape' => 'CheckIdFilterList', ], 'failedResourcesFilters' => [ 'shape' => 'CisNumberFilterList', ], 'platformFilters' => [ 'shape' => 'PlatformFilterList', ], 'securityLevelFilters' => [ 'shape' => 'CisSecurityLevelFilterList', ], 'titleFilters' => [ 'shape' => 'TitleFilterList', ], ], ], 'CisScanResultsAggregatedByChecksSortBy' => [ 'type' => 'string', 'enum' => [ 'CHECK_ID', 'TITLE', 'PLATFORM', 'FAILED_COUNTS', 'SECURITY_LEVEL', ], ], 'CisScanResultsAggregatedByTargetResourceFilterCriteria' => [ 'type' => 'structure', 'members' => [ 'accountIdFilters' => [ 'shape' => 'AccountIdFilterList', ], 'checkIdFilters' => [ 'shape' => 'CheckIdFilterList', ], 'failedChecksFilters' => [ 'shape' => 'CisNumberFilterList', ], 'platformFilters' => [ 'shape' => 'PlatformFilterList', ], 'statusFilters' => [ 'shape' => 'CisResultStatusFilterList', ], 'targetResourceIdFilters' => [ 'shape' => 'ResourceIdFilterList', ], 'targetResourceTagFilters' => [ 'shape' => 'ResourceTagFilterList', ], 'targetStatusFilters' => [ 'shape' => 'TargetStatusFilterList', ], 'targetStatusReasonFilters' => [ 'shape' => 'TargetStatusReasonFilterList', ], ], ], 'CisScanResultsAggregatedByTargetResourceSortBy' => [ 'type' => 'string', 'enum' => [ 'RESOURCE_ID', 'FAILED_COUNTS', 'ACCOUNT_ID', 'PLATFORM', 'TARGET_STATUS', 'TARGET_STATUS_REASON', ], ], 'CisScanResultsMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'CisScanStatus' => [ 'type' => 'string', 'enum' => [ 'FAILED', 'COMPLETED', 'CANCELLED', 'IN_PROGRESS', ], ], 'CisScanStatusComparison' => [ 'type' => 'string', 'enum' => [ 'EQUALS', ], ], 'CisScanStatusFilter' => [ 'type' => 'structure', 'required' => [ 'comparison', 'value', ], 'members' => [ 'comparison' => [ 'shape' => 'CisScanStatusComparison', ], 'value' => [ 'shape' => 'CisScanStatus', ], ], ], 'CisScanStatusFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CisScanStatusFilter', ], 'max' => 10, 'min' => 1, ], 'CisScheduledByFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CisStringFilter', ], 'max' => 10, 'min' => 1, ], 'CisSecurityLevel' => [ 'type' => 'string', 'enum' => [ 'LEVEL_1', 'LEVEL_2', ], ], 'CisSecurityLevelComparison' => [ 'type' => 'string', 'enum' => [ 'EQUALS', ], ], 'CisSecurityLevelFilter' => [ 'type' => 'structure', 'required' => [ 'comparison', 'value', ], 'members' => [ 'comparison' => [ 'shape' => 'CisSecurityLevelComparison', ], 'value' => [ 'shape' => 'CisSecurityLevel', ], ], ], 'CisSecurityLevelFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CisSecurityLevelFilter', ], 'max' => 10, 'min' => 1, ], 'CisSessionMessage' => [ 'type' => 'structure', 'required' => [ 'cisRuleDetails', 'ruleId', 'status', ], 'members' => [ 'cisRuleDetails' => [ 'shape' => 'CisRuleDetails', ], 'ruleId' => [ 'shape' => 'RuleId', ], 'status' => [ 'shape' => 'CisRuleStatus', ], ], ], 'CisSessionMessages' => [ 'type' => 'list', 'member' => [ 'shape' => 'CisSessionMessage', ], 'max' => 150, 'min' => 1, ], 'CisSortOrder' => [ 'type' => 'string', 'enum' => [ 'ASC', 'DESC', ], ], 'CisStringComparison' => [ 'type' => 'string', 'enum' => [ 'EQUALS', 'PREFIX', 'NOT_EQUALS', ], ], 'CisStringFilter' => [ 'type' => 'structure', 'required' => [ 'comparison', 'value', ], 'members' => [ 'comparison' => [ 'shape' => 'CisStringComparison', ], 'value' => [ 'shape' => 'String', ], ], ], 'CisTagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'MapKey', ], 'value' => [ 'shape' => 'MapValue', ], ], 'CisTargetResourceAggregation' => [ 'type' => 'structure', 'required' => [ 'scanArn', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'platform' => [ 'shape' => 'String', ], 'scanArn' => [ 'shape' => 'CisScanArn', ], 'statusCounts' => [ 'shape' => 'StatusCounts', ], 'targetResourceId' => [ 'shape' => 'ResourceId', ], 'targetResourceTags' => [ 'shape' => 'TargetResourceTags', ], 'targetStatus' => [ 'shape' => 'CisTargetStatus', ], 'targetStatusReason' => [ 'shape' => 'CisTargetStatusReason', ], ], ], 'CisTargetResourceAggregationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CisTargetResourceAggregation', ], 'max' => 1000, 'min' => 1, ], 'CisTargetStatus' => [ 'type' => 'string', 'enum' => [ 'TIMED_OUT', 'CANCELLED', 'COMPLETED', ], ], 'CisTargetStatusComparison' => [ 'type' => 'string', 'enum' => [ 'EQUALS', ], ], 'CisTargetStatusFilter' => [ 'type' => 'structure', 'required' => [ 'comparison', 'value', ], 'members' => [ 'comparison' => [ 'shape' => 'CisTargetStatusComparison', ], 'value' => [ 'shape' => 'CisTargetStatus', ], ], ], 'CisTargetStatusReason' => [ 'type' => 'string', 'enum' => [ 'SCAN_IN_PROGRESS', 'UNSUPPORTED_OS', 'SSM_UNMANAGED', ], ], 'CisTargetStatusReasonFilter' => [ 'type' => 'structure', 'required' => [ 'comparison', 'value', ], 'members' => [ 'comparison' => [ 'shape' => 'CisTargetStatusComparison', ], 'value' => [ 'shape' => 'CisTargetStatusReason', ], ], ], 'CisTargets' => [ 'type' => 'structure', 'members' => [ 'accountIds' => [ 'shape' => 'CisAccountIdList', ], 'targetResourceTags' => [ 'shape' => 'TargetResourceTags', ], ], ], 'CisaAction' => [ 'type' => 'string', 'min' => 0, ], 'CisaData' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'CisaAction', ], 'dateAdded' => [ 'shape' => 'CisaDateAdded', ], 'dateDue' => [ 'shape' => 'CisaDateDue', ], ], ], 'CisaDateAdded' => [ 'type' => 'timestamp', ], 'CisaDateDue' => [ 'type' => 'timestamp', ], 'ClientToken' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'CodeFilePath' => [ 'type' => 'structure', 'required' => [ 'endLine', 'fileName', 'filePath', 'startLine', ], 'members' => [ 'endLine' => [ 'shape' => 'Integer', ], 'fileName' => [ 'shape' => 'NonEmptyString', ], 'filePath' => [ 'shape' => 'NonEmptyString', ], 'startLine' => [ 'shape' => 'Integer', ], ], ], 'CodeLine' => [ 'type' => 'structure', 'required' => [ 'content', 'lineNumber', ], 'members' => [ 'content' => [ 'shape' => 'CodeLineContentString', ], 'lineNumber' => [ 'shape' => 'Integer', ], ], ], 'CodeLineContentString' => [ 'type' => 'string', 'max' => 240, 'min' => 0, ], 'CodeLineList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CodeLine', ], 'max' => 20, 'min' => 1, ], 'CodeSnippetError' => [ 'type' => 'structure', 'required' => [ 'errorCode', 'errorMessage', 'findingArn', ], 'members' => [ 'errorCode' => [ 'shape' => 'CodeSnippetErrorCode', ], 'errorMessage' => [ 'shape' => 'NonEmptyString', ], 'findingArn' => [ 'shape' => 'FindingArn', ], ], ], 'CodeSnippetErrorCode' => [ 'type' => 'string', 'enum' => [ 'INTERNAL_ERROR', 'ACCESS_DENIED', 'CODE_SNIPPET_NOT_FOUND', 'INVALID_INPUT', ], ], 'CodeSnippetErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CodeSnippetError', ], ], 'CodeSnippetResult' => [ 'type' => 'structure', 'members' => [ 'codeSnippet' => [ 'shape' => 'CodeLineList', ], 'endLine' => [ 'shape' => 'Integer', ], 'findingArn' => [ 'shape' => 'FindingArn', ], 'startLine' => [ 'shape' => 'Integer', ], 'suggestedFixes' => [ 'shape' => 'SuggestedFixes', ], ], ], 'CodeSnippetResultList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CodeSnippetResult', ], ], 'CodeVulnerabilityDetails' => [ 'type' => 'structure', 'required' => [ 'cwes', 'detectorId', 'detectorName', 'filePath', ], 'members' => [ 'cwes' => [ 'shape' => 'CweList', ], 'detectorId' => [ 'shape' => 'NonEmptyString', ], 'detectorName' => [ 'shape' => 'NonEmptyString', ], 'detectorTags' => [ 'shape' => 'DetectorTagList', ], 'filePath' => [ 'shape' => 'CodeFilePath', ], 'referenceUrls' => [ 'shape' => 'ReferenceUrls', ], 'ruleId' => [ 'shape' => 'NonEmptyString', ], 'sourceLambdaLayerArn' => [ 'shape' => 'LambdaLayerArn', ], ], ], 'Component' => [ 'type' => 'string', ], 'ComponentType' => [ 'type' => 'string', ], 'ComputePlatform' => [ 'type' => 'structure', 'members' => [ 'product' => [ 'shape' => 'Product', ], 'vendor' => [ 'shape' => 'Vendor', ], 'version' => [ 'shape' => 'PlatformVersion', ], ], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'Counts' => [ 'type' => 'structure', 'members' => [ 'count' => [ 'shape' => 'AggCounts', ], 'groupKey' => [ 'shape' => 'GroupKey', ], ], ], 'CountsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Counts', ], 'max' => 5, 'min' => 1, ], 'CoverageDateFilter' => [ 'type' => 'structure', 'members' => [ 'endInclusive' => [ 'shape' => 'DateTimeTimestamp', ], 'startInclusive' => [ 'shape' => 'DateTimeTimestamp', ], ], ], 'CoverageDateFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CoverageDateFilter', ], 'max' => 10, 'min' => 1, ], 'CoverageFilterCriteria' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => 'CoverageStringFilterList', ], 'ec2InstanceTags' => [ 'shape' => 'CoverageMapFilterList', ], 'ecrImageTags' => [ 'shape' => 'CoverageStringFilterList', ], 'ecrRepositoryName' => [ 'shape' => 'CoverageStringFilterList', ], 'imagePulledAt' => [ 'shape' => 'CoverageDateFilterList', ], 'lambdaFunctionName' => [ 'shape' => 'CoverageStringFilterList', ], 'lambdaFunctionRuntime' => [ 'shape' => 'CoverageStringFilterList', ], 'lambdaFunctionTags' => [ 'shape' => 'CoverageMapFilterList', ], 'lastScannedAt' => [ 'shape' => 'CoverageDateFilterList', ], 'resourceId' => [ 'shape' => 'CoverageStringFilterList', ], 'resourceType' => [ 'shape' => 'CoverageStringFilterList', ], 'scanMode' => [ 'shape' => 'CoverageStringFilterList', ], 'scanStatusCode' => [ 'shape' => 'CoverageStringFilterList', ], 'scanStatusReason' => [ 'shape' => 'CoverageStringFilterList', ], 'scanType' => [ 'shape' => 'CoverageStringFilterList', ], ], ], 'CoverageMapComparison' => [ 'type' => 'string', 'enum' => [ 'EQUALS', ], ], 'CoverageMapFilter' => [ 'type' => 'structure', 'required' => [ 'comparison', 'key', ], 'members' => [ 'comparison' => [ 'shape' => 'CoverageMapComparison', ], 'key' => [ 'shape' => 'NonEmptyString', ], 'value' => [ 'shape' => 'NonEmptyString', ], ], ], 'CoverageMapFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CoverageMapFilter', ], 'max' => 10, 'min' => 1, ], 'CoverageResourceType' => [ 'type' => 'string', 'enum' => [ 'AWS_EC2_INSTANCE', 'AWS_ECR_CONTAINER_IMAGE', 'AWS_ECR_REPOSITORY', 'AWS_LAMBDA_FUNCTION', ], ], 'CoverageStringComparison' => [ 'type' => 'string', 'enum' => [ 'EQUALS', 'NOT_EQUALS', ], ], 'CoverageStringFilter' => [ 'type' => 'structure', 'required' => [ 'comparison', 'value', ], 'members' => [ 'comparison' => [ 'shape' => 'CoverageStringComparison', ], 'value' => [ 'shape' => 'CoverageStringInput', ], ], ], 'CoverageStringFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CoverageStringFilter', ], 'max' => 10, 'min' => 1, ], 'CoverageStringInput' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'CoveredResource' => [ 'type' => 'structure', 'required' => [ 'accountId', 'resourceId', 'resourceType', 'scanType', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'lastScannedAt' => [ 'shape' => 'DateTimeTimestamp', ], 'resourceId' => [ 'shape' => 'ResourceId', ], 'resourceMetadata' => [ 'shape' => 'ResourceScanMetadata', ], 'resourceType' => [ 'shape' => 'CoverageResourceType', ], 'scanMode' => [ 'shape' => 'ScanMode', ], 'scanStatus' => [ 'shape' => 'ScanStatus', ], 'scanType' => [ 'shape' => 'ScanType', ], ], ], 'CoveredResources' => [ 'type' => 'list', 'member' => [ 'shape' => 'CoveredResource', ], ], 'CreateCisScanConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'scanName', 'schedule', 'securityLevel', 'targets', ], 'members' => [ 'scanName' => [ 'shape' => 'CisScanName', ], 'schedule' => [ 'shape' => 'Schedule', ], 'securityLevel' => [ 'shape' => 'CisSecurityLevel', ], 'tags' => [ 'shape' => 'CisTagMap', ], 'targets' => [ 'shape' => 'CreateCisTargets', ], ], ], 'CreateCisScanConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'scanConfigurationArn' => [ 'shape' => 'CisScanConfigurationArn', ], ], ], 'CreateCisTargets' => [ 'type' => 'structure', 'required' => [ 'accountIds', 'targetResourceTags', ], 'members' => [ 'accountIds' => [ 'shape' => 'TargetAccountList', ], 'targetResourceTags' => [ 'shape' => 'TargetResourceTags', ], ], ], 'CreateFilterRequest' => [ 'type' => 'structure', 'required' => [ 'action', 'filterCriteria', 'name', ], 'members' => [ 'action' => [ 'shape' => 'FilterAction', ], 'description' => [ 'shape' => 'FilterDescription', ], 'filterCriteria' => [ 'shape' => 'FilterCriteria', ], 'name' => [ 'shape' => 'FilterName', ], 'reason' => [ 'shape' => 'FilterReason', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateFilterResponse' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'FilterArn', ], ], ], 'CreateFindingsReportRequest' => [ 'type' => 'structure', 'required' => [ 'reportFormat', 's3Destination', ], 'members' => [ 'filterCriteria' => [ 'shape' => 'FilterCriteria', ], 'reportFormat' => [ 'shape' => 'ReportFormat', ], 's3Destination' => [ 'shape' => 'Destination', ], ], ], 'CreateFindingsReportResponse' => [ 'type' => 'structure', 'members' => [ 'reportId' => [ 'shape' => 'ReportId', ], ], ], 'CreateSbomExportRequest' => [ 'type' => 'structure', 'required' => [ 'reportFormat', 's3Destination', ], 'members' => [ 'reportFormat' => [ 'shape' => 'SbomReportFormat', ], 'resourceFilterCriteria' => [ 'shape' => 'ResourceFilterCriteria', ], 's3Destination' => [ 'shape' => 'Destination', ], ], ], 'CreateSbomExportResponse' => [ 'type' => 'structure', 'members' => [ 'reportId' => [ 'shape' => 'ReportId', ], ], ], 'Currency' => [ 'type' => 'string', 'enum' => [ 'USD', ], ], 'Cvss2' => [ 'type' => 'structure', 'members' => [ 'baseScore' => [ 'shape' => 'Cvss2BaseScore', ], 'scoringVector' => [ 'shape' => 'Cvss2ScoringVector', ], ], ], 'Cvss2BaseScore' => [ 'type' => 'double', ], 'Cvss2ScoringVector' => [ 'type' => 'string', 'min' => 0, ], 'Cvss3' => [ 'type' => 'structure', 'members' => [ 'baseScore' => [ 'shape' => 'Cvss3BaseScore', ], 'scoringVector' => [ 'shape' => 'Cvss3ScoringVector', ], ], ], 'Cvss3BaseScore' => [ 'type' => 'double', ], 'Cvss3ScoringVector' => [ 'type' => 'string', 'min' => 0, ], 'CvssScore' => [ 'type' => 'structure', 'required' => [ 'baseScore', 'scoringVector', 'source', 'version', ], 'members' => [ 'baseScore' => [ 'shape' => 'Double', ], 'scoringVector' => [ 'shape' => 'NonEmptyString', ], 'source' => [ 'shape' => 'NonEmptyString', ], 'version' => [ 'shape' => 'NonEmptyString', ], ], ], 'CvssScoreAdjustment' => [ 'type' => 'structure', 'required' => [ 'metric', 'reason', ], 'members' => [ 'metric' => [ 'shape' => 'NonEmptyString', ], 'reason' => [ 'shape' => 'NonEmptyString', ], ], ], 'CvssScoreAdjustmentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CvssScoreAdjustment', ], ], 'CvssScoreDetails' => [ 'type' => 'structure', 'required' => [ 'score', 'scoreSource', 'scoringVector', 'version', ], 'members' => [ 'adjustments' => [ 'shape' => 'CvssScoreAdjustmentList', ], 'cvssSource' => [ 'shape' => 'NonEmptyString', ], 'score' => [ 'shape' => 'Double', ], 'scoreSource' => [ 'shape' => 'NonEmptyString', ], 'scoringVector' => [ 'shape' => 'NonEmptyString', ], 'version' => [ 'shape' => 'NonEmptyString', ], ], ], 'CvssScoreList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CvssScore', ], ], 'Cwe' => [ 'type' => 'string', 'min' => 0, ], 'CweList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], 'max' => 10, 'min' => 1, ], 'Cwes' => [ 'type' => 'list', 'member' => [ 'shape' => 'Cwe', ], 'min' => 0, ], 'DailySchedule' => [ 'type' => 'structure', 'required' => [ 'startTime', ], 'members' => [ 'startTime' => [ 'shape' => 'Time', ], ], ], 'DateFilter' => [ 'type' => 'structure', 'members' => [ 'endInclusive' => [ 'shape' => 'Timestamp', ], 'startInclusive' => [ 'shape' => 'Timestamp', ], ], ], 'DateFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DateFilter', ], 'max' => 10, 'min' => 1, ], 'DateTimeTimestamp' => [ 'type' => 'timestamp', ], 'Day' => [ 'type' => 'string', 'enum' => [ 'SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', ], ], 'DaysList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Day', ], 'max' => 7, 'min' => 1, ], 'DelegatedAdmin' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'relationshipStatus' => [ 'shape' => 'RelationshipStatus', ], ], ], 'DelegatedAdminAccount' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'status' => [ 'shape' => 'DelegatedAdminStatus', ], ], ], 'DelegatedAdminAccountList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DelegatedAdminAccount', ], 'max' => 5, 'min' => 0, ], 'DelegatedAdminStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLE_IN_PROGRESS', ], ], 'DeleteCisScanConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'scanConfigurationArn', ], 'members' => [ 'scanConfigurationArn' => [ 'shape' => 'CisScanConfigurationArn', ], ], ], 'DeleteCisScanConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'scanConfigurationArn', ], 'members' => [ 'scanConfigurationArn' => [ 'shape' => 'CisScanConfigurationArn', ], ], ], 'DeleteFilterRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'FilterArn', ], ], ], 'DeleteFilterResponse' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'FilterArn', ], ], ], 'DescribeOrganizationConfigurationRequest' => [ 'type' => 'structure', 'members' => [], ], 'DescribeOrganizationConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'autoEnable' => [ 'shape' => 'AutoEnable', ], 'maxAccountLimitReached' => [ 'shape' => 'Boolean', ], ], ], 'Destination' => [ 'type' => 'structure', 'required' => [ 'bucketName', 'kmsKeyArn', ], 'members' => [ 'bucketName' => [ 'shape' => 'String', ], 'keyPrefix' => [ 'shape' => 'String', ], 'kmsKeyArn' => [ 'shape' => 'String', ], ], ], 'DetectionPlatforms' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], 'max' => 100, 'min' => 0, ], 'DetectorTagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], 'max' => 10, 'min' => 1, ], 'DisableDelegatedAdminAccountRequest' => [ 'type' => 'structure', 'required' => [ 'delegatedAdminAccountId', ], 'members' => [ 'delegatedAdminAccountId' => [ 'shape' => 'AccountId', ], ], ], 'DisableDelegatedAdminAccountResponse' => [ 'type' => 'structure', 'required' => [ 'delegatedAdminAccountId', ], 'members' => [ 'delegatedAdminAccountId' => [ 'shape' => 'AccountId', ], ], ], 'DisableRequest' => [ 'type' => 'structure', 'members' => [ 'accountIds' => [ 'shape' => 'AccountIdSet', ], 'resourceTypes' => [ 'shape' => 'DisableResourceTypeList', ], ], ], 'DisableResourceTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceScanType', ], 'max' => 3, 'min' => 0, ], 'DisableResponse' => [ 'type' => 'structure', 'required' => [ 'accounts', ], 'members' => [ 'accounts' => [ 'shape' => 'AccountList', ], 'failedAccounts' => [ 'shape' => 'FailedAccountList', ], ], ], 'DisassociateMemberRequest' => [ 'type' => 'structure', 'required' => [ 'accountId', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], ], ], 'DisassociateMemberResponse' => [ 'type' => 'structure', 'required' => [ 'accountId', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], ], ], 'Double' => [ 'type' => 'double', 'box' => true, ], 'Ec2Configuration' => [ 'type' => 'structure', 'required' => [ 'scanMode', ], 'members' => [ 'scanMode' => [ 'shape' => 'Ec2ScanMode', ], ], ], 'Ec2ConfigurationState' => [ 'type' => 'structure', 'members' => [ 'scanModeState' => [ 'shape' => 'Ec2ScanModeState', ], ], ], 'Ec2DeepInspectionStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVATED', 'DEACTIVATED', 'PENDING', 'FAILED', ], ], 'Ec2InstanceAggregation' => [ 'type' => 'structure', 'members' => [ 'amis' => [ 'shape' => 'StringFilterList', ], 'instanceIds' => [ 'shape' => 'StringFilterList', ], 'instanceTags' => [ 'shape' => 'MapFilterList', ], 'operatingSystems' => [ 'shape' => 'StringFilterList', ], 'sortBy' => [ 'shape' => 'Ec2InstanceSortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'Ec2InstanceAggregationResponse' => [ 'type' => 'structure', 'required' => [ 'instanceId', ], 'members' => [ 'accountId' => [ 'shape' => 'String', ], 'ami' => [ 'shape' => 'AmiId', ], 'instanceId' => [ 'shape' => 'NonEmptyString', ], 'instanceTags' => [ 'shape' => 'TagMap', ], 'networkFindings' => [ 'shape' => 'Long', ], 'operatingSystem' => [ 'shape' => 'String', ], 'severityCounts' => [ 'shape' => 'SeverityCounts', ], ], ], 'Ec2InstanceSortBy' => [ 'type' => 'string', 'enum' => [ 'NETWORK_FINDINGS', 'CRITICAL', 'HIGH', 'ALL', ], ], 'Ec2Metadata' => [ 'type' => 'structure', 'members' => [ 'amiId' => [ 'shape' => 'AmiId', ], 'platform' => [ 'shape' => 'Ec2Platform', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'Ec2Platform' => [ 'type' => 'string', 'enum' => [ 'WINDOWS', 'LINUX', 'UNKNOWN', 'MACOS', ], ], 'Ec2ScanMode' => [ 'type' => 'string', 'enum' => [ 'EC2_SSM_AGENT_BASED', 'EC2_HYBRID', ], ], 'Ec2ScanModeState' => [ 'type' => 'structure', 'members' => [ 'scanMode' => [ 'shape' => 'Ec2ScanMode', ], 'scanModeStatus' => [ 'shape' => 'Ec2ScanModeStatus', ], ], ], 'Ec2ScanModeStatus' => [ 'type' => 'string', 'enum' => [ 'SUCCESS', 'PENDING', ], ], 'EcrConfiguration' => [ 'type' => 'structure', 'required' => [ 'rescanDuration', ], 'members' => [ 'pullDateRescanDuration' => [ 'shape' => 'EcrPullDateRescanDuration', ], 'rescanDuration' => [ 'shape' => 'EcrRescanDuration', ], ], ], 'EcrConfigurationState' => [ 'type' => 'structure', 'members' => [ 'rescanDurationState' => [ 'shape' => 'EcrRescanDurationState', ], ], ], 'EcrContainerImageMetadata' => [ 'type' => 'structure', 'members' => [ 'imagePulledAt' => [ 'shape' => 'DateTimeTimestamp', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'EcrPullDateRescanDuration' => [ 'type' => 'string', 'enum' => [ 'DAYS_14', 'DAYS_30', 'DAYS_60', 'DAYS_90', 'DAYS_180', ], ], 'EcrRepositoryMetadata' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'scanFrequency' => [ 'shape' => 'EcrScanFrequency', ], ], ], 'EcrRescanDuration' => [ 'type' => 'string', 'enum' => [ 'LIFETIME', 'DAYS_30', 'DAYS_180', 'DAYS_14', 'DAYS_60', 'DAYS_90', ], ], 'EcrRescanDurationState' => [ 'type' => 'structure', 'members' => [ 'pullDateRescanDuration' => [ 'shape' => 'EcrPullDateRescanDuration', ], 'rescanDuration' => [ 'shape' => 'EcrRescanDuration', ], 'status' => [ 'shape' => 'EcrRescanDurationStatus', ], 'updatedAt' => [ 'shape' => 'DateTimeTimestamp', ], ], ], 'EcrRescanDurationStatus' => [ 'type' => 'string', 'enum' => [ 'SUCCESS', 'PENDING', 'FAILED', ], ], 'EcrScanFrequency' => [ 'type' => 'string', 'enum' => [ 'MANUAL', 'SCAN_ON_PUSH', 'CONTINUOUS_SCAN', ], ], 'EnableDelegatedAdminAccountRequest' => [ 'type' => 'structure', 'required' => [ 'delegatedAdminAccountId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'delegatedAdminAccountId' => [ 'shape' => 'AccountId', ], ], ], 'EnableDelegatedAdminAccountResponse' => [ 'type' => 'structure', 'required' => [ 'delegatedAdminAccountId', ], 'members' => [ 'delegatedAdminAccountId' => [ 'shape' => 'AccountId', ], ], ], 'EnableRequest' => [ 'type' => 'structure', 'required' => [ 'resourceTypes', ], 'members' => [ 'accountIds' => [ 'shape' => 'AccountIdSet', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'resourceTypes' => [ 'shape' => 'EnableResourceTypeList', ], ], ], 'EnableResourceTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceScanType', ], 'max' => 3, 'min' => 1, ], 'EnableResponse' => [ 'type' => 'structure', 'required' => [ 'accounts', ], 'members' => [ 'accounts' => [ 'shape' => 'AccountList', ], 'failedAccounts' => [ 'shape' => 'FailedAccountList', ], ], ], 'Epss' => [ 'type' => 'structure', 'members' => [ 'score' => [ 'shape' => 'EpssScore', ], ], ], 'EpssDetails' => [ 'type' => 'structure', 'members' => [ 'score' => [ 'shape' => 'EpssScoreValue', ], ], ], 'EpssScore' => [ 'type' => 'double', ], 'EpssScoreValue' => [ 'type' => 'double', 'max' => 1.0, 'min' => 0.0, ], 'ErrorCode' => [ 'type' => 'string', 'enum' => [ 'ALREADY_ENABLED', 'ENABLE_IN_PROGRESS', 'DISABLE_IN_PROGRESS', 'SUSPEND_IN_PROGRESS', 'RESOURCE_NOT_FOUND', 'ACCESS_DENIED', 'INTERNAL_ERROR', 'SSM_UNAVAILABLE', 'SSM_THROTTLED', 'EVENTBRIDGE_UNAVAILABLE', 'EVENTBRIDGE_THROTTLED', 'RESOURCE_SCAN_NOT_DISABLED', 'DISASSOCIATE_ALL_MEMBERS', 'ACCOUNT_IS_ISOLATED', 'EC2_SSM_RESOURCE_DATA_SYNC_LIMIT_EXCEEDED', 'EC2_SSM_ASSOCIATION_VERSION_LIMIT_EXCEEDED', ], ], 'ErrorMessage' => [ 'type' => 'string', ], 'Evidence' => [ 'type' => 'structure', 'members' => [ 'evidenceDetail' => [ 'shape' => 'EvidenceDetail', ], 'evidenceRule' => [ 'shape' => 'EvidenceRule', ], 'severity' => [ 'shape' => 'EvidenceSeverity', ], ], ], 'EvidenceDetail' => [ 'type' => 'string', 'min' => 0, ], 'EvidenceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Evidence', ], ], 'EvidenceRule' => [ 'type' => 'string', 'min' => 0, ], 'EvidenceSeverity' => [ 'type' => 'string', 'min' => 0, ], 'ExecutionRoleArn' => [ 'type' => 'string', 'pattern' => '^arn:(aws[a-zA-Z-]*)?:iam::\\d{12}:role/?[a-zA-Z_0-9+=,.@\\-_/]+$', ], 'ExploitAvailable' => [ 'type' => 'string', 'enum' => [ 'YES', 'NO', ], ], 'ExploitObserved' => [ 'type' => 'structure', 'members' => [ 'firstSeen' => [ 'shape' => 'FirstSeen', ], 'lastSeen' => [ 'shape' => 'LastSeen', ], ], ], 'ExploitabilityDetails' => [ 'type' => 'structure', 'members' => [ 'lastKnownExploitAt' => [ 'shape' => 'DateTimeTimestamp', ], ], ], 'ExternalReportStatus' => [ 'type' => 'string', 'enum' => [ 'SUCCEEDED', 'IN_PROGRESS', 'CANCELLED', 'FAILED', ], ], 'FailedAccount' => [ 'type' => 'structure', 'required' => [ 'accountId', 'errorCode', 'errorMessage', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'errorCode' => [ 'shape' => 'ErrorCode', ], 'errorMessage' => [ 'shape' => 'NonEmptyString', ], 'resourceStatus' => [ 'shape' => 'ResourceStatus', ], 'status' => [ 'shape' => 'Status', ], ], ], 'FailedAccountList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailedAccount', ], 'max' => 100, 'min' => 0, ], 'FailedMemberAccountEc2DeepInspectionStatusState' => [ 'type' => 'structure', 'required' => [ 'accountId', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'ec2ScanStatus' => [ 'shape' => 'Status', ], 'errorMessage' => [ 'shape' => 'NonEmptyString', ], ], ], 'FailedMemberAccountEc2DeepInspectionStatusStateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailedMemberAccountEc2DeepInspectionStatusState', ], 'max' => 100, 'min' => 0, ], 'FilePath' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'Filter' => [ 'type' => 'structure', 'required' => [ 'action', 'arn', 'createdAt', 'criteria', 'name', 'ownerId', 'updatedAt', ], 'members' => [ 'action' => [ 'shape' => 'FilterAction', ], 'arn' => [ 'shape' => 'FilterArn', ], 'createdAt' => [ 'shape' => 'DateTimeTimestamp', ], 'criteria' => [ 'shape' => 'FilterCriteria', ], 'description' => [ 'shape' => 'FilterDescription', ], 'name' => [ 'shape' => 'FilterName', ], 'ownerId' => [ 'shape' => 'OwnerId', ], 'reason' => [ 'shape' => 'FilterReason', ], 'tags' => [ 'shape' => 'TagMap', ], 'updatedAt' => [ 'shape' => 'DateTimeTimestamp', ], ], ], 'FilterAction' => [ 'type' => 'string', 'enum' => [ 'NONE', 'SUPPRESS', ], ], 'FilterArn' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'FilterArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterArn', ], ], 'FilterCriteria' => [ 'type' => 'structure', 'members' => [ 'awsAccountId' => [ 'shape' => 'StringFilterList', ], 'codeVulnerabilityDetectorName' => [ 'shape' => 'StringFilterList', ], 'codeVulnerabilityDetectorTags' => [ 'shape' => 'StringFilterList', ], 'codeVulnerabilityFilePath' => [ 'shape' => 'StringFilterList', ], 'componentId' => [ 'shape' => 'StringFilterList', ], 'componentType' => [ 'shape' => 'StringFilterList', ], 'ec2InstanceImageId' => [ 'shape' => 'StringFilterList', ], 'ec2InstanceSubnetId' => [ 'shape' => 'StringFilterList', ], 'ec2InstanceVpcId' => [ 'shape' => 'StringFilterList', ], 'ecrImageArchitecture' => [ 'shape' => 'StringFilterList', ], 'ecrImageHash' => [ 'shape' => 'StringFilterList', ], 'ecrImagePushedAt' => [ 'shape' => 'DateFilterList', ], 'ecrImageRegistry' => [ 'shape' => 'StringFilterList', ], 'ecrImageRepositoryName' => [ 'shape' => 'StringFilterList', ], 'ecrImageTags' => [ 'shape' => 'StringFilterList', ], 'epssScore' => [ 'shape' => 'NumberFilterList', ], 'exploitAvailable' => [ 'shape' => 'StringFilterList', ], 'findingArn' => [ 'shape' => 'StringFilterList', ], 'findingStatus' => [ 'shape' => 'StringFilterList', ], 'findingType' => [ 'shape' => 'StringFilterList', ], 'firstObservedAt' => [ 'shape' => 'DateFilterList', ], 'fixAvailable' => [ 'shape' => 'StringFilterList', ], 'inspectorScore' => [ 'shape' => 'NumberFilterList', ], 'lambdaFunctionExecutionRoleArn' => [ 'shape' => 'StringFilterList', ], 'lambdaFunctionLastModifiedAt' => [ 'shape' => 'DateFilterList', ], 'lambdaFunctionLayers' => [ 'shape' => 'StringFilterList', ], 'lambdaFunctionName' => [ 'shape' => 'StringFilterList', ], 'lambdaFunctionRuntime' => [ 'shape' => 'StringFilterList', ], 'lastObservedAt' => [ 'shape' => 'DateFilterList', ], 'networkProtocol' => [ 'shape' => 'StringFilterList', ], 'portRange' => [ 'shape' => 'PortRangeFilterList', ], 'relatedVulnerabilities' => [ 'shape' => 'StringFilterList', ], 'resourceId' => [ 'shape' => 'StringFilterList', ], 'resourceTags' => [ 'shape' => 'MapFilterList', ], 'resourceType' => [ 'shape' => 'StringFilterList', ], 'severity' => [ 'shape' => 'StringFilterList', ], 'title' => [ 'shape' => 'StringFilterList', ], 'updatedAt' => [ 'shape' => 'DateFilterList', ], 'vendorSeverity' => [ 'shape' => 'StringFilterList', ], 'vulnerabilityId' => [ 'shape' => 'StringFilterList', ], 'vulnerabilitySource' => [ 'shape' => 'StringFilterList', ], 'vulnerablePackages' => [ 'shape' => 'PackageFilterList', ], ], ], 'FilterDescription' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'FilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], ], 'FilterName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'FilterReason' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'Finding' => [ 'type' => 'structure', 'required' => [ 'awsAccountId', 'description', 'findingArn', 'firstObservedAt', 'lastObservedAt', 'remediation', 'resources', 'severity', 'status', 'type', ], 'members' => [ 'awsAccountId' => [ 'shape' => 'AccountId', ], 'codeVulnerabilityDetails' => [ 'shape' => 'CodeVulnerabilityDetails', ], 'description' => [ 'shape' => 'FindingDescription', ], 'epss' => [ 'shape' => 'EpssDetails', ], 'exploitAvailable' => [ 'shape' => 'ExploitAvailable', ], 'exploitabilityDetails' => [ 'shape' => 'ExploitabilityDetails', ], 'findingArn' => [ 'shape' => 'FindingArn', ], 'firstObservedAt' => [ 'shape' => 'DateTimeTimestamp', ], 'fixAvailable' => [ 'shape' => 'FixAvailable', ], 'inspectorScore' => [ 'shape' => 'Double', ], 'inspectorScoreDetails' => [ 'shape' => 'InspectorScoreDetails', ], 'lastObservedAt' => [ 'shape' => 'DateTimeTimestamp', ], 'networkReachabilityDetails' => [ 'shape' => 'NetworkReachabilityDetails', ], 'packageVulnerabilityDetails' => [ 'shape' => 'PackageVulnerabilityDetails', ], 'remediation' => [ 'shape' => 'Remediation', ], 'resources' => [ 'shape' => 'ResourceList', ], 'severity' => [ 'shape' => 'Severity', ], 'status' => [ 'shape' => 'FindingStatus', ], 'title' => [ 'shape' => 'FindingTitle', ], 'type' => [ 'shape' => 'FindingType', ], 'updatedAt' => [ 'shape' => 'DateTimeTimestamp', ], ], ], 'FindingArn' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^arn:(aws[a-zA-Z-]*)?:inspector2:[a-z]{2}(-gov)?-[a-z]+-\\d{1}:\\d{12}:finding/[a-f0-9]{32}$', ], 'FindingArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FindingArn', ], 'max' => 10, 'min' => 1, ], 'FindingDescription' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'FindingDetail' => [ 'type' => 'structure', 'members' => [ 'cisaData' => [ 'shape' => 'CisaData', ], 'cwes' => [ 'shape' => 'Cwes', ], 'epssScore' => [ 'shape' => 'Double', ], 'evidences' => [ 'shape' => 'EvidenceList', ], 'exploitObserved' => [ 'shape' => 'ExploitObserved', ], 'findingArn' => [ 'shape' => 'FindingArn', ], 'referenceUrls' => [ 'shape' => 'VulnerabilityReferenceUrls', ], 'riskScore' => [ 'shape' => 'RiskScore', ], 'tools' => [ 'shape' => 'Tools', ], 'ttps' => [ 'shape' => 'Ttps', ], ], ], 'FindingDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'FindingDetail', ], 'min' => 0, ], 'FindingDetailsError' => [ 'type' => 'structure', 'required' => [ 'errorCode', 'errorMessage', 'findingArn', ], 'members' => [ 'errorCode' => [ 'shape' => 'FindingDetailsErrorCode', ], 'errorMessage' => [ 'shape' => 'NonEmptyString', ], 'findingArn' => [ 'shape' => 'FindingArn', ], ], ], 'FindingDetailsErrorCode' => [ 'type' => 'string', 'enum' => [ 'INTERNAL_ERROR', 'ACCESS_DENIED', 'FINDING_DETAILS_NOT_FOUND', 'INVALID_INPUT', ], ], 'FindingDetailsErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FindingDetailsError', ], ], 'FindingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Finding', ], 'max' => 25, 'min' => 0, ], 'FindingStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'SUPPRESSED', 'CLOSED', ], ], 'FindingTitle' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'FindingType' => [ 'type' => 'string', 'enum' => [ 'NETWORK_REACHABILITY', 'PACKAGE_VULNERABILITY', 'CODE_VULNERABILITY', ], ], 'FindingTypeAggregation' => [ 'type' => 'structure', 'members' => [ 'findingType' => [ 'shape' => 'AggregationFindingType', ], 'resourceType' => [ 'shape' => 'AggregationResourceType', ], 'sortBy' => [ 'shape' => 'FindingTypeSortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'FindingTypeAggregationResponse' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'exploitAvailableCount' => [ 'shape' => 'Long', ], 'fixAvailableCount' => [ 'shape' => 'Long', ], 'severityCounts' => [ 'shape' => 'SeverityCounts', ], ], ], 'FindingTypeSortBy' => [ 'type' => 'string', 'enum' => [ 'CRITICAL', 'HIGH', 'ALL', ], ], 'FirstSeen' => [ 'type' => 'timestamp', ], 'FixAvailable' => [ 'type' => 'string', 'enum' => [ 'YES', 'NO', 'PARTIAL', ], ], 'FreeTrialAccountInfo' => [ 'type' => 'structure', 'required' => [ 'accountId', 'freeTrialInfo', ], 'members' => [ 'accountId' => [ 'shape' => 'MeteringAccountId', ], 'freeTrialInfo' => [ 'shape' => 'FreeTrialInfoList', ], ], ], 'FreeTrialAccountInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FreeTrialAccountInfo', ], ], 'FreeTrialInfo' => [ 'type' => 'structure', 'required' => [ 'end', 'start', 'status', 'type', ], 'members' => [ 'end' => [ 'shape' => 'Timestamp', ], 'start' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'FreeTrialStatus', ], 'type' => [ 'shape' => 'FreeTrialType', ], ], ], 'FreeTrialInfoError' => [ 'type' => 'structure', 'required' => [ 'accountId', 'code', 'message', ], 'members' => [ 'accountId' => [ 'shape' => 'MeteringAccountId', ], 'code' => [ 'shape' => 'FreeTrialInfoErrorCode', ], 'message' => [ 'shape' => 'String', ], ], ], 'FreeTrialInfoErrorCode' => [ 'type' => 'string', 'enum' => [ 'ACCESS_DENIED', 'INTERNAL_ERROR', ], ], 'FreeTrialInfoErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FreeTrialInfoError', ], ], 'FreeTrialInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FreeTrialInfo', ], ], 'FreeTrialStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', ], ], 'FreeTrialType' => [ 'type' => 'string', 'enum' => [ 'EC2', 'ECR', 'LAMBDA', 'LAMBDA_CODE', ], ], 'FunctionName' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9-_\\.]+(:(\\$LATEST|[a-zA-Z0-9-_]+))?$', ], 'GetCisScanReportRequest' => [ 'type' => 'structure', 'required' => [ 'scanArn', ], 'members' => [ 'reportFormat' => [ 'shape' => 'CisReportFormat', ], 'scanArn' => [ 'shape' => 'CisScanArn', ], 'targetAccounts' => [ 'shape' => 'ReportTargetAccounts', ], ], ], 'GetCisScanReportResponse' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'CisReportStatus', ], 'url' => [ 'shape' => 'String', ], ], ], 'GetCisScanResultDetailsMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'GetCisScanResultDetailsRequest' => [ 'type' => 'structure', 'required' => [ 'accountId', 'scanArn', 'targetResourceId', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'filterCriteria' => [ 'shape' => 'CisScanResultDetailsFilterCriteria', ], 'maxResults' => [ 'shape' => 'GetCisScanResultDetailsMaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'scanArn' => [ 'shape' => 'CisScanArn', ], 'sortBy' => [ 'shape' => 'CisScanResultDetailsSortBy', ], 'sortOrder' => [ 'shape' => 'CisSortOrder', ], 'targetResourceId' => [ 'shape' => 'ResourceId', ], ], ], 'GetCisScanResultDetailsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'scanResultDetails' => [ 'shape' => 'CisScanResultDetailsList', ], ], ], 'GetConfigurationRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'ec2Configuration' => [ 'shape' => 'Ec2ConfigurationState', ], 'ecrConfiguration' => [ 'shape' => 'EcrConfigurationState', ], ], ], 'GetDelegatedAdminAccountRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetDelegatedAdminAccountResponse' => [ 'type' => 'structure', 'members' => [ 'delegatedAdmin' => [ 'shape' => 'DelegatedAdmin', ], ], ], 'GetEc2DeepInspectionConfigurationRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetEc2DeepInspectionConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'errorMessage' => [ 'shape' => 'NonEmptyString', ], 'orgPackagePaths' => [ 'shape' => 'PathList', ], 'packagePaths' => [ 'shape' => 'PathList', ], 'status' => [ 'shape' => 'Ec2DeepInspectionStatus', ], ], ], 'GetEncryptionKeyRequest' => [ 'type' => 'structure', 'required' => [ 'resourceType', 'scanType', ], 'members' => [ 'resourceType' => [ 'shape' => 'ResourceType', 'location' => 'querystring', 'locationName' => 'resourceType', ], 'scanType' => [ 'shape' => 'ScanType', 'location' => 'querystring', 'locationName' => 'scanType', ], ], ], 'GetEncryptionKeyResponse' => [ 'type' => 'structure', 'required' => [ 'kmsKeyId', ], 'members' => [ 'kmsKeyId' => [ 'shape' => 'KmsKeyArn', ], ], ], 'GetFindingsReportStatusRequest' => [ 'type' => 'structure', 'members' => [ 'reportId' => [ 'shape' => 'ReportId', ], ], ], 'GetFindingsReportStatusResponse' => [ 'type' => 'structure', 'members' => [ 'destination' => [ 'shape' => 'Destination', ], 'errorCode' => [ 'shape' => 'ReportingErrorCode', ], 'errorMessage' => [ 'shape' => 'ErrorMessage', ], 'filterCriteria' => [ 'shape' => 'FilterCriteria', ], 'reportId' => [ 'shape' => 'ReportId', ], 'status' => [ 'shape' => 'ExternalReportStatus', ], ], ], 'GetMemberRequest' => [ 'type' => 'structure', 'required' => [ 'accountId', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], ], ], 'GetMemberResponse' => [ 'type' => 'structure', 'members' => [ 'member' => [ 'shape' => 'Member', ], ], ], 'GetSbomExportRequest' => [ 'type' => 'structure', 'required' => [ 'reportId', ], 'members' => [ 'reportId' => [ 'shape' => 'ReportId', ], ], ], 'GetSbomExportResponse' => [ 'type' => 'structure', 'members' => [ 'errorCode' => [ 'shape' => 'ReportingErrorCode', ], 'errorMessage' => [ 'shape' => 'NonEmptyString', ], 'filterCriteria' => [ 'shape' => 'ResourceFilterCriteria', ], 'format' => [ 'shape' => 'SbomReportFormat', ], 'reportId' => [ 'shape' => 'ReportId', ], 's3Destination' => [ 'shape' => 'Destination', ], 'status' => [ 'shape' => 'ExternalReportStatus', ], ], ], 'GroupKey' => [ 'type' => 'string', 'enum' => [ 'SCAN_STATUS_CODE', 'SCAN_STATUS_REASON', 'ACCOUNT_ID', 'RESOURCE_TYPE', 'ECR_REPOSITORY_NAME', ], ], 'ImageHash' => [ 'type' => 'string', 'max' => 71, 'min' => 71, 'pattern' => '^sha256:[a-z0-9]{64}$', ], 'ImageLayerAggregation' => [ 'type' => 'structure', 'members' => [ 'layerHashes' => [ 'shape' => 'StringFilterList', ], 'repositories' => [ 'shape' => 'StringFilterList', ], 'resourceIds' => [ 'shape' => 'StringFilterList', ], 'sortBy' => [ 'shape' => 'ImageLayerSortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'ImageLayerAggregationResponse' => [ 'type' => 'structure', 'required' => [ 'accountId', 'layerHash', 'repository', 'resourceId', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'layerHash' => [ 'shape' => 'NonEmptyString', ], 'repository' => [ 'shape' => 'NonEmptyString', ], 'resourceId' => [ 'shape' => 'NonEmptyString', ], 'severityCounts' => [ 'shape' => 'SeverityCounts', ], ], ], 'ImageLayerSortBy' => [ 'type' => 'string', 'enum' => [ 'CRITICAL', 'HIGH', 'ALL', ], ], 'ImageTagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'InspectorScoreDetails' => [ 'type' => 'structure', 'members' => [ 'adjustedCvss' => [ 'shape' => 'CvssScoreDetails', ], ], ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'IpV4Address' => [ 'type' => 'string', 'max' => 15, 'min' => 7, 'pattern' => '^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$', ], 'IpV4AddressList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpV4Address', ], ], 'IpV6Address' => [ 'type' => 'string', 'max' => 47, 'min' => 1, ], 'IpV6AddressList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpV6Address', ], ], 'KmsKeyArn' => [ 'type' => 'string', 'pattern' => '^arn:aws(-(us-gov|cn))?:kms:([a-z0-9][-.a-z0-9]{0,62})?:[0-9]{12}?:key/(([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})|(mrk-[0-9a-zA-Z]{32}))$', ], 'LambdaFunctionAggregation' => [ 'type' => 'structure', 'members' => [ 'functionNames' => [ 'shape' => 'StringFilterList', ], 'functionTags' => [ 'shape' => 'MapFilterList', ], 'resourceIds' => [ 'shape' => 'StringFilterList', ], 'runtimes' => [ 'shape' => 'StringFilterList', ], 'sortBy' => [ 'shape' => 'LambdaFunctionSortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'LambdaFunctionAggregationResponse' => [ 'type' => 'structure', 'required' => [ 'resourceId', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'functionName' => [ 'shape' => 'String', ], 'lambdaTags' => [ 'shape' => 'TagMap', ], 'lastModifiedAt' => [ 'shape' => 'DateTimeTimestamp', ], 'resourceId' => [ 'shape' => 'NonEmptyString', ], 'runtime' => [ 'shape' => 'String', ], 'severityCounts' => [ 'shape' => 'SeverityCounts', ], ], ], 'LambdaFunctionMetadata' => [ 'type' => 'structure', 'members' => [ 'functionName' => [ 'shape' => 'String', ], 'functionTags' => [ 'shape' => 'TagMap', ], 'layers' => [ 'shape' => 'LambdaLayerList', ], 'runtime' => [ 'shape' => 'Runtime', ], ], ], 'LambdaFunctionSortBy' => [ 'type' => 'string', 'enum' => [ 'CRITICAL', 'HIGH', 'ALL', ], ], 'LambdaLayerAggregation' => [ 'type' => 'structure', 'members' => [ 'functionNames' => [ 'shape' => 'StringFilterList', ], 'layerArns' => [ 'shape' => 'StringFilterList', ], 'resourceIds' => [ 'shape' => 'StringFilterList', ], 'sortBy' => [ 'shape' => 'LambdaLayerSortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'LambdaLayerAggregationResponse' => [ 'type' => 'structure', 'required' => [ 'accountId', 'functionName', 'layerArn', 'resourceId', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'functionName' => [ 'shape' => 'NonEmptyString', ], 'layerArn' => [ 'shape' => 'NonEmptyString', ], 'resourceId' => [ 'shape' => 'NonEmptyString', ], 'severityCounts' => [ 'shape' => 'SeverityCounts', ], ], ], 'LambdaLayerArn' => [ 'type' => 'string', 'pattern' => '^arn:[a-zA-Z0-9-]+:lambda:[a-zA-Z0-9-]+:\\d{12}:layer:[a-zA-Z0-9-_]+:[0-9]+$', ], 'LambdaLayerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 5, 'min' => 0, ], 'LambdaLayerSortBy' => [ 'type' => 'string', 'enum' => [ 'CRITICAL', 'HIGH', 'ALL', ], ], 'LambdaVpcConfig' => [ 'type' => 'structure', 'members' => [ 'securityGroupIds' => [ 'shape' => 'SecurityGroupIdList', ], 'subnetIds' => [ 'shape' => 'SubnetIdList', ], 'vpcId' => [ 'shape' => 'VpcId', ], ], ], 'LastSeen' => [ 'type' => 'timestamp', ], 'LayerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LambdaLayerArn', ], 'max' => 5, 'min' => 1, ], 'ListAccountPermissionsMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 1024, 'min' => 1, ], 'ListAccountPermissionsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListAccountPermissionsMaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'service' => [ 'shape' => 'Service', ], ], ], 'ListAccountPermissionsResponse' => [ 'type' => 'structure', 'required' => [ 'permissions', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'permissions' => [ 'shape' => 'Permissions', ], ], ], 'ListCisScanConfigurationsFilterCriteria' => [ 'type' => 'structure', 'members' => [ 'scanConfigurationArnFilters' => [ 'shape' => 'CisScanConfigurationArnFilterList', ], 'scanNameFilters' => [ 'shape' => 'CisScanNameFilterList', ], 'targetResourceTagFilters' => [ 'shape' => 'ResourceTagFilterList', ], ], ], 'ListCisScanConfigurationsMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListCisScanConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'filterCriteria' => [ 'shape' => 'ListCisScanConfigurationsFilterCriteria', ], 'maxResults' => [ 'shape' => 'ListCisScanConfigurationsMaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'sortBy' => [ 'shape' => 'CisScanConfigurationsSortBy', ], 'sortOrder' => [ 'shape' => 'CisSortOrder', ], ], ], 'ListCisScanConfigurationsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'scanConfigurations' => [ 'shape' => 'CisScanConfigurationList', ], ], ], 'ListCisScanResultsAggregatedByChecksRequest' => [ 'type' => 'structure', 'required' => [ 'scanArn', ], 'members' => [ 'filterCriteria' => [ 'shape' => 'CisScanResultsAggregatedByChecksFilterCriteria', ], 'maxResults' => [ 'shape' => 'CisScanResultsMaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'scanArn' => [ 'shape' => 'CisScanArn', ], 'sortBy' => [ 'shape' => 'CisScanResultsAggregatedByChecksSortBy', ], 'sortOrder' => [ 'shape' => 'CisSortOrder', ], ], ], 'ListCisScanResultsAggregatedByChecksResponse' => [ 'type' => 'structure', 'members' => [ 'checkAggregations' => [ 'shape' => 'CisCheckAggregationList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListCisScanResultsAggregatedByTargetResourceRequest' => [ 'type' => 'structure', 'required' => [ 'scanArn', ], 'members' => [ 'filterCriteria' => [ 'shape' => 'CisScanResultsAggregatedByTargetResourceFilterCriteria', ], 'maxResults' => [ 'shape' => 'CisScanResultsMaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'scanArn' => [ 'shape' => 'CisScanArn', ], 'sortBy' => [ 'shape' => 'CisScanResultsAggregatedByTargetResourceSortBy', ], 'sortOrder' => [ 'shape' => 'CisSortOrder', ], ], ], 'ListCisScanResultsAggregatedByTargetResourceResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'targetResourceAggregations' => [ 'shape' => 'CisTargetResourceAggregationList', ], ], ], 'ListCisScansDetailLevel' => [ 'type' => 'string', 'enum' => [ 'ORGANIZATION', 'MEMBER', ], ], 'ListCisScansFilterCriteria' => [ 'type' => 'structure', 'members' => [ 'failedChecksFilters' => [ 'shape' => 'CisNumberFilterList', ], 'scanArnFilters' => [ 'shape' => 'CisScanArnFilterList', ], 'scanAtFilters' => [ 'shape' => 'CisScanDateFilterList', ], 'scanConfigurationArnFilters' => [ 'shape' => 'CisScanConfigurationArnFilterList', ], 'scanNameFilters' => [ 'shape' => 'CisScanNameFilterList', ], 'scanStatusFilters' => [ 'shape' => 'CisScanStatusFilterList', ], 'scheduledByFilters' => [ 'shape' => 'CisScheduledByFilterList', ], 'targetAccountIdFilters' => [ 'shape' => 'AccountIdFilterList', ], 'targetResourceIdFilters' => [ 'shape' => 'ResourceIdFilterList', ], 'targetResourceTagFilters' => [ 'shape' => 'ResourceTagFilterList', ], ], ], 'ListCisScansMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListCisScansRequest' => [ 'type' => 'structure', 'members' => [ 'detailLevel' => [ 'shape' => 'ListCisScansDetailLevel', ], 'filterCriteria' => [ 'shape' => 'ListCisScansFilterCriteria', ], 'maxResults' => [ 'shape' => 'ListCisScansMaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'sortBy' => [ 'shape' => 'ListCisScansSortBy', ], 'sortOrder' => [ 'shape' => 'CisSortOrder', ], ], ], 'ListCisScansResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'scans' => [ 'shape' => 'CisScanList', ], ], ], 'ListCisScansSortBy' => [ 'type' => 'string', 'enum' => [ 'STATUS', 'SCHEDULED_BY', 'SCAN_START_DATE', 'FAILED_CHECKS', ], ], 'ListCoverageMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 200, 'min' => 1, ], 'ListCoverageRequest' => [ 'type' => 'structure', 'members' => [ 'filterCriteria' => [ 'shape' => 'CoverageFilterCriteria', ], 'maxResults' => [ 'shape' => 'ListCoverageMaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListCoverageResponse' => [ 'type' => 'structure', 'members' => [ 'coveredResources' => [ 'shape' => 'CoveredResources', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListCoverageStatisticsRequest' => [ 'type' => 'structure', 'members' => [ 'filterCriteria' => [ 'shape' => 'CoverageFilterCriteria', ], 'groupBy' => [ 'shape' => 'GroupKey', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListCoverageStatisticsResponse' => [ 'type' => 'structure', 'required' => [ 'totalCounts', ], 'members' => [ 'countsByGroup' => [ 'shape' => 'CountsList', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'totalCounts' => [ 'shape' => 'Long', ], ], ], 'ListDelegatedAdminAccountsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListDelegatedAdminMaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDelegatedAdminAccountsResponse' => [ 'type' => 'structure', 'members' => [ 'delegatedAdminAccounts' => [ 'shape' => 'DelegatedAdminAccountList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDelegatedAdminMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 5, 'min' => 1, ], 'ListFilterMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListFiltersRequest' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'FilterAction', ], 'arns' => [ 'shape' => 'FilterArnList', ], 'maxResults' => [ 'shape' => 'ListFilterMaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListFiltersResponse' => [ 'type' => 'structure', 'required' => [ 'filters', ], 'members' => [ 'filters' => [ 'shape' => 'FilterList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListFindingAggregationsMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListFindingAggregationsRequest' => [ 'type' => 'structure', 'required' => [ 'aggregationType', ], 'members' => [ 'accountIds' => [ 'shape' => 'StringFilterList', ], 'aggregationRequest' => [ 'shape' => 'AggregationRequest', ], 'aggregationType' => [ 'shape' => 'AggregationType', ], 'maxResults' => [ 'shape' => 'ListFindingAggregationsMaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListFindingAggregationsResponse' => [ 'type' => 'structure', 'required' => [ 'aggregationType', ], 'members' => [ 'aggregationType' => [ 'shape' => 'AggregationType', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'responses' => [ 'shape' => 'AggregationResponseList', ], ], ], 'ListFindingsMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListFindingsRequest' => [ 'type' => 'structure', 'members' => [ 'filterCriteria' => [ 'shape' => 'FilterCriteria', ], 'maxResults' => [ 'shape' => 'ListFindingsMaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'sortCriteria' => [ 'shape' => 'SortCriteria', ], ], ], 'ListFindingsResponse' => [ 'type' => 'structure', 'members' => [ 'findings' => [ 'shape' => 'FindingList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListMembersMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 1, ], 'ListMembersRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListMembersMaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'onlyAssociated' => [ 'shape' => 'Boolean', ], ], ], 'ListMembersResponse' => [ 'type' => 'structure', 'members' => [ 'members' => [ 'shape' => 'MemberList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ListUsageTotalsMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 500, 'min' => 1, ], 'ListUsageTotalsNextToken' => [ 'type' => 'string', 'min' => 1, ], 'ListUsageTotalsRequest' => [ 'type' => 'structure', 'members' => [ 'accountIds' => [ 'shape' => 'UsageAccountIdList', ], 'maxResults' => [ 'shape' => 'ListUsageTotalsMaxResults', ], 'nextToken' => [ 'shape' => 'ListUsageTotalsNextToken', ], ], ], 'ListUsageTotalsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'ListUsageTotalsNextToken', ], 'totals' => [ 'shape' => 'UsageTotalList', ], ], ], 'Long' => [ 'type' => 'long', 'box' => true, ], 'MapComparison' => [ 'type' => 'string', 'enum' => [ 'EQUALS', ], ], 'MapFilter' => [ 'type' => 'structure', 'required' => [ 'comparison', 'key', ], 'members' => [ 'comparison' => [ 'shape' => 'MapComparison', ], 'key' => [ 'shape' => 'MapKey', ], 'value' => [ 'shape' => 'MapValue', ], ], ], 'MapFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MapFilter', ], 'max' => 10, 'min' => 1, ], 'MapKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'MapValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'Member' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'delegatedAdminAccountId' => [ 'shape' => 'AccountId', ], 'relationshipStatus' => [ 'shape' => 'RelationshipStatus', ], 'updatedAt' => [ 'shape' => 'DateTimeTimestamp', ], ], ], 'MemberAccountEc2DeepInspectionStatus' => [ 'type' => 'structure', 'required' => [ 'accountId', 'activateDeepInspection', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'activateDeepInspection' => [ 'shape' => 'Boolean', ], ], ], 'MemberAccountEc2DeepInspectionStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MemberAccountEc2DeepInspectionStatus', ], 'max' => 100, 'min' => 0, ], 'MemberAccountEc2DeepInspectionStatusState' => [ 'type' => 'structure', 'required' => [ 'accountId', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'errorMessage' => [ 'shape' => 'NonEmptyString', ], 'status' => [ 'shape' => 'Ec2DeepInspectionStatus', ], ], ], 'MemberAccountEc2DeepInspectionStatusStateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MemberAccountEc2DeepInspectionStatusState', ], 'max' => 100, 'min' => 0, ], 'MemberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Member', ], 'max' => 50, 'min' => 0, ], 'MeteringAccountId' => [ 'type' => 'string', 'pattern' => '[0-9]{12}', ], 'MonthlyCostEstimate' => [ 'type' => 'double', 'min' => 0, ], 'MonthlySchedule' => [ 'type' => 'structure', 'required' => [ 'day', 'startTime', ], 'members' => [ 'day' => [ 'shape' => 'Day', ], 'startTime' => [ 'shape' => 'Time', ], ], ], 'NetworkPath' => [ 'type' => 'structure', 'members' => [ 'steps' => [ 'shape' => 'StepList', ], ], ], 'NetworkProtocol' => [ 'type' => 'string', 'enum' => [ 'TCP', 'UDP', ], ], 'NetworkReachabilityDetails' => [ 'type' => 'structure', 'required' => [ 'networkPath', 'openPortRange', 'protocol', ], 'members' => [ 'networkPath' => [ 'shape' => 'NetworkPath', ], 'openPortRange' => [ 'shape' => 'PortRange', ], 'protocol' => [ 'shape' => 'NetworkProtocol', ], ], ], 'NextToken' => [ 'type' => 'string', 'max' => 1000000, 'min' => 0, ], 'NonEmptyString' => [ 'type' => 'string', 'min' => 1, ], 'NonEmptyStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'NumberFilter' => [ 'type' => 'structure', 'members' => [ 'lowerInclusive' => [ 'shape' => 'Double', ], 'upperInclusive' => [ 'shape' => 'Double', ], ], ], 'NumberFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NumberFilter', ], 'max' => 10, 'min' => 1, ], 'OneAccountIdFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CisStringFilter', ], 'max' => 1, 'min' => 1, ], 'OneTimeSchedule' => [ 'type' => 'structure', 'members' => [], ], 'Operation' => [ 'type' => 'string', 'enum' => [ 'ENABLE_SCANNING', 'DISABLE_SCANNING', 'ENABLE_REPOSITORY', 'DISABLE_REPOSITORY', ], ], 'OwnerId' => [ 'type' => 'string', 'max' => 34, 'min' => 12, 'pattern' => '(^\\d{12}$)|(^o-[a-z0-9]{10,32}$)', ], 'PackageAggregation' => [ 'type' => 'structure', 'members' => [ 'packageNames' => [ 'shape' => 'StringFilterList', ], 'sortBy' => [ 'shape' => 'PackageSortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'PackageAggregationResponse' => [ 'type' => 'structure', 'required' => [ 'packageName', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'packageName' => [ 'shape' => 'NonEmptyString', ], 'severityCounts' => [ 'shape' => 'SeverityCounts', ], ], ], 'PackageArchitecture' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'PackageEpoch' => [ 'type' => 'integer', ], 'PackageFilter' => [ 'type' => 'structure', 'members' => [ 'architecture' => [ 'shape' => 'StringFilter', ], 'epoch' => [ 'shape' => 'NumberFilter', ], 'name' => [ 'shape' => 'StringFilter', ], 'release' => [ 'shape' => 'StringFilter', ], 'sourceLambdaLayerArn' => [ 'shape' => 'StringFilter', ], 'sourceLayerHash' => [ 'shape' => 'StringFilter', ], 'version' => [ 'shape' => 'StringFilter', ], ], ], 'PackageFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PackageFilter', ], 'max' => 10, 'min' => 1, ], 'PackageManager' => [ 'type' => 'string', 'enum' => [ 'BUNDLER', 'CARGO', 'COMPOSER', 'NPM', 'NUGET', 'PIPENV', 'POETRY', 'YARN', 'GOBINARY', 'GOMOD', 'JAR', 'OS', 'PIP', 'PYTHONPKG', 'NODEPKG', 'POM', 'GEMSPEC', ], ], 'PackageName' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'PackageRelease' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'PackageSortBy' => [ 'type' => 'string', 'enum' => [ 'CRITICAL', 'HIGH', 'ALL', ], ], 'PackageType' => [ 'type' => 'string', 'enum' => [ 'IMAGE', 'ZIP', ], ], 'PackageVersion' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'PackageVulnerabilityDetails' => [ 'type' => 'structure', 'required' => [ 'source', 'vulnerabilityId', ], 'members' => [ 'cvss' => [ 'shape' => 'CvssScoreList', ], 'referenceUrls' => [ 'shape' => 'NonEmptyStringList', ], 'relatedVulnerabilities' => [ 'shape' => 'VulnerabilityIdList', ], 'source' => [ 'shape' => 'NonEmptyString', ], 'sourceUrl' => [ 'shape' => 'NonEmptyString', ], 'vendorCreatedAt' => [ 'shape' => 'DateTimeTimestamp', ], 'vendorSeverity' => [ 'shape' => 'NonEmptyString', ], 'vendorUpdatedAt' => [ 'shape' => 'DateTimeTimestamp', ], 'vulnerabilityId' => [ 'shape' => 'VulnerabilityId', ], 'vulnerablePackages' => [ 'shape' => 'VulnerablePackageList', ], ], ], 'Path' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '^(?:/(?:\\.[-\\w]+|[-\\w]+(?:\\.[-\\w]+)?))+/?$', ], 'PathList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Path', ], 'max' => 5, 'min' => 0, ], 'Permission' => [ 'type' => 'structure', 'required' => [ 'operation', 'service', ], 'members' => [ 'operation' => [ 'shape' => 'Operation', ], 'service' => [ 'shape' => 'Service', ], ], ], 'Permissions' => [ 'type' => 'list', 'member' => [ 'shape' => 'Permission', ], 'max' => 1024, 'min' => 0, ], 'Platform' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'PlatformFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CisStringFilter', ], 'max' => 10, 'min' => 1, ], 'PlatformVersion' => [ 'type' => 'string', 'max' => 8, 'min' => 0, ], 'Port' => [ 'type' => 'integer', 'box' => true, 'max' => 65535, 'min' => 0, ], 'PortRange' => [ 'type' => 'structure', 'required' => [ 'begin', 'end', ], 'members' => [ 'begin' => [ 'shape' => 'Port', ], 'end' => [ 'shape' => 'Port', ], ], ], 'PortRangeFilter' => [ 'type' => 'structure', 'members' => [ 'beginInclusive' => [ 'shape' => 'Port', ], 'endInclusive' => [ 'shape' => 'Port', ], ], ], 'PortRangeFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PortRangeFilter', ], 'max' => 10, 'min' => 1, ], 'Product' => [ 'type' => 'string', 'max' => 32, 'min' => 0, ], 'Reason' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'Recommendation' => [ 'type' => 'structure', 'members' => [ 'Url' => [ 'shape' => 'NonEmptyString', ], 'text' => [ 'shape' => 'NonEmptyString', ], ], ], 'ReferenceUrls' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], 'max' => 10, 'min' => 1, ], 'RelatedVulnerabilities' => [ 'type' => 'list', 'member' => [ 'shape' => 'RelatedVulnerability', ], 'max' => 100, 'min' => 0, ], 'RelatedVulnerability' => [ 'type' => 'string', 'min' => 0, ], 'RelationshipStatus' => [ 'type' => 'string', 'enum' => [ 'CREATED', 'INVITED', 'DISABLED', 'ENABLED', 'REMOVED', 'RESIGNED', 'DELETED', 'EMAIL_VERIFICATION_IN_PROGRESS', 'EMAIL_VERIFICATION_FAILED', 'REGION_DISABLED', 'ACCOUNT_SUSPENDED', 'CANNOT_CREATE_DETECTOR_IN_ORG_MASTER', ], ], 'Remediation' => [ 'type' => 'structure', 'members' => [ 'recommendation' => [ 'shape' => 'Recommendation', ], ], ], 'ReportFormat' => [ 'type' => 'string', 'enum' => [ 'CSV', 'JSON', ], ], 'ReportId' => [ 'type' => 'string', 'pattern' => '\\b[a-f0-9]{8}\\b-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-\\b[a-f0-9]{12}\\b', ], 'ReportTargetAccounts' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountId', ], 'max' => 1, 'min' => 0, ], 'ReportingErrorCode' => [ 'type' => 'string', 'enum' => [ 'INTERNAL_ERROR', 'INVALID_PERMISSIONS', 'NO_FINDINGS_FOUND', 'BUCKET_NOT_FOUND', 'INCOMPATIBLE_BUCKET_REGION', 'MALFORMED_KMS_KEY', ], ], 'RepositoryAggregation' => [ 'type' => 'structure', 'members' => [ 'repositories' => [ 'shape' => 'StringFilterList', ], 'sortBy' => [ 'shape' => 'RepositorySortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'RepositoryAggregationResponse' => [ 'type' => 'structure', 'required' => [ 'repository', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'affectedImages' => [ 'shape' => 'Long', ], 'repository' => [ 'shape' => 'NonEmptyString', ], 'severityCounts' => [ 'shape' => 'SeverityCounts', ], ], ], 'RepositorySortBy' => [ 'type' => 'string', 'enum' => [ 'CRITICAL', 'HIGH', 'ALL', 'AFFECTED_IMAGES', ], ], 'ResetEncryptionKeyRequest' => [ 'type' => 'structure', 'required' => [ 'resourceType', 'scanType', ], 'members' => [ 'resourceType' => [ 'shape' => 'ResourceType', ], 'scanType' => [ 'shape' => 'ScanType', ], ], ], 'ResetEncryptionKeyResponse' => [ 'type' => 'structure', 'members' => [], ], 'Resource' => [ 'type' => 'structure', 'required' => [ 'id', 'type', ], 'members' => [ 'details' => [ 'shape' => 'ResourceDetails', ], 'id' => [ 'shape' => 'NonEmptyString', ], 'partition' => [ 'shape' => 'NonEmptyString', ], 'region' => [ 'shape' => 'NonEmptyString', ], 'tags' => [ 'shape' => 'TagMap', ], 'type' => [ 'shape' => 'ResourceType', ], ], ], 'ResourceDetails' => [ 'type' => 'structure', 'members' => [ 'awsEc2Instance' => [ 'shape' => 'AwsEc2InstanceDetails', ], 'awsEcrContainerImage' => [ 'shape' => 'AwsEcrContainerImageDetails', ], 'awsLambdaFunction' => [ 'shape' => 'AwsLambdaFunctionDetails', ], ], ], 'ResourceFilterCriteria' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => 'ResourceStringFilterList', ], 'ec2InstanceTags' => [ 'shape' => 'ResourceMapFilterList', ], 'ecrImageTags' => [ 'shape' => 'ResourceStringFilterList', ], 'ecrRepositoryName' => [ 'shape' => 'ResourceStringFilterList', ], 'lambdaFunctionName' => [ 'shape' => 'ResourceStringFilterList', ], 'lambdaFunctionTags' => [ 'shape' => 'ResourceMapFilterList', ], 'resourceId' => [ 'shape' => 'ResourceStringFilterList', ], 'resourceType' => [ 'shape' => 'ResourceStringFilterList', ], ], ], 'ResourceId' => [ 'type' => 'string', 'max' => 341, 'min' => 10, 'pattern' => '(^arn:.*:ecr:.*:\\d{12}:repository\\/(?:[a-z0-9]+(?:[._-][a-z0-9]+)*\\/)*[a-z0-9]+(?:[._-][a-z0-9]+)*(\\/sha256:[a-z0-9]{64})?$)|(^i-([a-z0-9]{8}|[a-z0-9]{17}|\\\\*)$|(^arn:(aws[a-zA-Z-]*)?:lambda:[a-z]{2}(-gov)?-[a-z]+-\\d{1}:\\d{12}:function:[a-zA-Z0-9-_\\.]+(:(\\$LATEST|[a-zA-Z0-9-_]+))?$))', ], 'ResourceIdFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CisStringFilter', ], 'max' => 10, 'min' => 1, ], 'ResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Resource', ], 'max' => 10, 'min' => 1, ], 'ResourceMapComparison' => [ 'type' => 'string', 'enum' => [ 'EQUALS', ], ], 'ResourceMapFilter' => [ 'type' => 'structure', 'required' => [ 'comparison', 'key', ], 'members' => [ 'comparison' => [ 'shape' => 'ResourceMapComparison', ], 'key' => [ 'shape' => 'NonEmptyString', ], 'value' => [ 'shape' => 'NonEmptyString', ], ], ], 'ResourceMapFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceMapFilter', ], 'max' => 10, 'min' => 1, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResourceScanMetadata' => [ 'type' => 'structure', 'members' => [ 'ec2' => [ 'shape' => 'Ec2Metadata', ], 'ecrImage' => [ 'shape' => 'EcrContainerImageMetadata', ], 'ecrRepository' => [ 'shape' => 'EcrRepositoryMetadata', ], 'lambdaFunction' => [ 'shape' => 'LambdaFunctionMetadata', ], ], ], 'ResourceScanType' => [ 'type' => 'string', 'enum' => [ 'EC2', 'ECR', 'LAMBDA', 'LAMBDA_CODE', ], ], 'ResourceState' => [ 'type' => 'structure', 'required' => [ 'ec2', 'ecr', ], 'members' => [ 'ec2' => [ 'shape' => 'State', ], 'ecr' => [ 'shape' => 'State', ], 'lambda' => [ 'shape' => 'State', ], 'lambdaCode' => [ 'shape' => 'State', ], ], ], 'ResourceStatus' => [ 'type' => 'structure', 'required' => [ 'ec2', 'ecr', ], 'members' => [ 'ec2' => [ 'shape' => 'Status', ], 'ecr' => [ 'shape' => 'Status', ], 'lambda' => [ 'shape' => 'Status', ], 'lambdaCode' => [ 'shape' => 'Status', ], ], ], 'ResourceStringComparison' => [ 'type' => 'string', 'enum' => [ 'EQUALS', 'NOT_EQUALS', ], ], 'ResourceStringFilter' => [ 'type' => 'structure', 'required' => [ 'comparison', 'value', ], 'members' => [ 'comparison' => [ 'shape' => 'ResourceStringComparison', ], 'value' => [ 'shape' => 'ResourceStringInput', ], ], ], 'ResourceStringFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceStringFilter', ], 'max' => 10, 'min' => 1, ], 'ResourceStringInput' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ResourceTagFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagFilter', ], 'max' => 10, 'min' => 1, ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'AWS_EC2_INSTANCE', 'AWS_ECR_CONTAINER_IMAGE', 'AWS_ECR_REPOSITORY', 'AWS_LAMBDA_FUNCTION', ], ], 'RiskScore' => [ 'type' => 'integer', 'box' => true, ], 'RuleId' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'Runtime' => [ 'type' => 'string', 'enum' => [ 'NODEJS', 'NODEJS_12_X', 'NODEJS_14_X', 'NODEJS_16_X', 'JAVA_8', 'JAVA_8_AL2', 'JAVA_11', 'PYTHON_3_7', 'PYTHON_3_8', 'PYTHON_3_9', 'UNSUPPORTED', 'NODEJS_18_X', 'GO_1_X', 'JAVA_17', 'PYTHON_3_10', ], ], 'SbomReportFormat' => [ 'type' => 'string', 'enum' => [ 'CYCLONEDX_1_4', 'SPDX_2_3', ], ], 'ScanMode' => [ 'type' => 'string', 'enum' => [ 'EC2_SSM_AGENT_BASED', 'EC2_AGENTLESS', ], ], 'ScanStatus' => [ 'type' => 'structure', 'required' => [ 'reason', 'statusCode', ], 'members' => [ 'reason' => [ 'shape' => 'ScanStatusReason', ], 'statusCode' => [ 'shape' => 'ScanStatusCode', ], ], ], 'ScanStatusCode' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', ], ], 'ScanStatusReason' => [ 'type' => 'string', 'enum' => [ 'PENDING_INITIAL_SCAN', 'ACCESS_DENIED', 'INTERNAL_ERROR', 'UNMANAGED_EC2_INSTANCE', 'UNSUPPORTED_OS', 'SCAN_ELIGIBILITY_EXPIRED', 'RESOURCE_TERMINATED', 'SUCCESSFUL', 'NO_RESOURCES_FOUND', 'IMAGE_SIZE_EXCEEDED', 'SCAN_FREQUENCY_MANUAL', 'SCAN_FREQUENCY_SCAN_ON_PUSH', 'EC2_INSTANCE_STOPPED', 'PENDING_DISABLE', 'NO_INVENTORY', 'STALE_INVENTORY', 'EXCLUDED_BY_TAG', 'UNSUPPORTED_RUNTIME', 'UNSUPPORTED_MEDIA_TYPE', 'UNSUPPORTED_CONFIG_FILE', 'DEEP_INSPECTION_PACKAGE_COLLECTION_LIMIT_EXCEEDED', 'DEEP_INSPECTION_DAILY_SSM_INVENTORY_LIMIT_EXCEEDED', 'DEEP_INSPECTION_COLLECTION_TIME_LIMIT_EXCEEDED', 'DEEP_INSPECTION_NO_INVENTORY', 'AGENTLESS_INSTANCE_STORAGE_LIMIT_EXCEEDED', 'AGENTLESS_INSTANCE_COLLECTION_TIME_LIMIT_EXCEEDED', ], ], 'ScanType' => [ 'type' => 'string', 'enum' => [ 'NETWORK', 'PACKAGE', 'CODE', ], ], 'Schedule' => [ 'type' => 'structure', 'members' => [ 'daily' => [ 'shape' => 'DailySchedule', ], 'monthly' => [ 'shape' => 'MonthlySchedule', ], 'oneTime' => [ 'shape' => 'OneTimeSchedule', ], 'weekly' => [ 'shape' => 'WeeklySchedule', ], ], 'union' => true, ], 'SearchVulnerabilitiesFilterCriteria' => [ 'type' => 'structure', 'required' => [ 'vulnerabilityIds', ], 'members' => [ 'vulnerabilityIds' => [ 'shape' => 'VulnIdList', ], ], ], 'SearchVulnerabilitiesRequest' => [ 'type' => 'structure', 'required' => [ 'filterCriteria', ], 'members' => [ 'filterCriteria' => [ 'shape' => 'SearchVulnerabilitiesFilterCriteria', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'SearchVulnerabilitiesResponse' => [ 'type' => 'structure', 'required' => [ 'vulnerabilities', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'vulnerabilities' => [ 'shape' => 'Vulnerabilities', ], ], ], 'SecurityGroupId' => [ 'type' => 'string', 'pattern' => '^sg-([a-z0-9]{8}|[a-z0-9]{17}|\\*)$', ], 'SecurityGroupIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupId', ], 'max' => 5, 'min' => 0, ], 'SendCisSessionHealthRequest' => [ 'type' => 'structure', 'required' => [ 'scanJobId', 'sessionToken', ], 'members' => [ 'scanJobId' => [ 'shape' => 'UUID', ], 'sessionToken' => [ 'shape' => 'UUID', ], ], ], 'SendCisSessionHealthResponse' => [ 'type' => 'structure', 'members' => [], ], 'SendCisSessionTelemetryRequest' => [ 'type' => 'structure', 'required' => [ 'messages', 'scanJobId', 'sessionToken', ], 'members' => [ 'messages' => [ 'shape' => 'CisSessionMessages', ], 'scanJobId' => [ 'shape' => 'UUID', ], 'sessionToken' => [ 'shape' => 'UUID', ], ], ], 'SendCisSessionTelemetryResponse' => [ 'type' => 'structure', 'members' => [], ], 'Service' => [ 'type' => 'string', 'enum' => [ 'EC2', 'ECR', 'LAMBDA', ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'Severity' => [ 'type' => 'string', 'enum' => [ 'INFORMATIONAL', 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL', 'UNTRIAGED', ], ], 'SeverityCounts' => [ 'type' => 'structure', 'members' => [ 'all' => [ 'shape' => 'Long', ], 'critical' => [ 'shape' => 'Long', ], 'high' => [ 'shape' => 'Long', ], 'medium' => [ 'shape' => 'Long', ], ], ], 'SortCriteria' => [ 'type' => 'structure', 'required' => [ 'field', 'sortOrder', ], 'members' => [ 'field' => [ 'shape' => 'SortField', ], 'sortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'SortField' => [ 'type' => 'string', 'enum' => [ 'AWS_ACCOUNT_ID', 'FINDING_TYPE', 'SEVERITY', 'FIRST_OBSERVED_AT', 'LAST_OBSERVED_AT', 'FINDING_STATUS', 'RESOURCE_TYPE', 'ECR_IMAGE_PUSHED_AT', 'ECR_IMAGE_REPOSITORY_NAME', 'ECR_IMAGE_REGISTRY', 'NETWORK_PROTOCOL', 'COMPONENT_TYPE', 'VULNERABILITY_ID', 'VULNERABILITY_SOURCE', 'INSPECTOR_SCORE', 'VENDOR_SEVERITY', 'EPSS_SCORE', ], ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'ASC', 'DESC', ], ], 'SourceLayerHash' => [ 'type' => 'string', 'max' => 71, 'min' => 71, 'pattern' => '^sha256:[a-z0-9]{64}$', ], 'StartCisSessionMessage' => [ 'type' => 'structure', 'required' => [ 'sessionToken', ], 'members' => [ 'sessionToken' => [ 'shape' => 'UUID', ], ], ], 'StartCisSessionRequest' => [ 'type' => 'structure', 'required' => [ 'message', 'scanJobId', ], 'members' => [ 'message' => [ 'shape' => 'StartCisSessionMessage', ], 'scanJobId' => [ 'shape' => 'UUID', ], ], ], 'StartCisSessionResponse' => [ 'type' => 'structure', 'members' => [], ], 'State' => [ 'type' => 'structure', 'required' => [ 'errorCode', 'errorMessage', 'status', ], 'members' => [ 'errorCode' => [ 'shape' => 'ErrorCode', ], 'errorMessage' => [ 'shape' => 'NonEmptyString', ], 'status' => [ 'shape' => 'Status', ], ], ], 'Status' => [ 'type' => 'string', 'enum' => [ 'ENABLING', 'ENABLED', 'DISABLING', 'DISABLED', 'SUSPENDING', 'SUSPENDED', ], ], 'StatusCounts' => [ 'type' => 'structure', 'members' => [ 'failed' => [ 'shape' => 'Integer', ], 'passed' => [ 'shape' => 'Integer', ], 'skipped' => [ 'shape' => 'Integer', ], ], ], 'Step' => [ 'type' => 'structure', 'required' => [ 'componentId', 'componentType', ], 'members' => [ 'componentId' => [ 'shape' => 'Component', ], 'componentType' => [ 'shape' => 'ComponentType', ], ], ], 'StepList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Step', ], 'max' => 30, 'min' => 1, ], 'StopCisMessageProgress' => [ 'type' => 'structure', 'members' => [ 'errorChecks' => [ 'shape' => 'CheckCount', ], 'failedChecks' => [ 'shape' => 'CheckCount', ], 'informationalChecks' => [ 'shape' => 'CheckCount', ], 'notApplicableChecks' => [ 'shape' => 'CheckCount', ], 'notEvaluatedChecks' => [ 'shape' => 'CheckCount', ], 'successfulChecks' => [ 'shape' => 'CheckCount', ], 'totalChecks' => [ 'shape' => 'CheckCount', ], 'unknownChecks' => [ 'shape' => 'CheckCount', ], ], ], 'StopCisSessionMessage' => [ 'type' => 'structure', 'required' => [ 'progress', 'status', ], 'members' => [ 'benchmarkProfile' => [ 'shape' => 'BenchmarkProfile', ], 'benchmarkVersion' => [ 'shape' => 'BenchmarkVersion', ], 'computePlatform' => [ 'shape' => 'ComputePlatform', ], 'progress' => [ 'shape' => 'StopCisMessageProgress', ], 'reason' => [ 'shape' => 'Reason', ], 'status' => [ 'shape' => 'StopCisSessionStatus', ], ], ], 'StopCisSessionRequest' => [ 'type' => 'structure', 'required' => [ 'message', 'scanJobId', 'sessionToken', ], 'members' => [ 'message' => [ 'shape' => 'StopCisSessionMessage', ], 'scanJobId' => [ 'shape' => 'UUID', ], 'sessionToken' => [ 'shape' => 'UUID', ], ], ], 'StopCisSessionResponse' => [ 'type' => 'structure', 'members' => [], ], 'StopCisSessionStatus' => [ 'type' => 'string', 'enum' => [ 'SUCCESS', 'FAILED', 'INTERRUPTED', 'UNSUPPORTED_OS', ], ], 'String' => [ 'type' => 'string', ], 'StringComparison' => [ 'type' => 'string', 'enum' => [ 'EQUALS', 'PREFIX', 'NOT_EQUALS', ], ], 'StringFilter' => [ 'type' => 'structure', 'required' => [ 'comparison', 'value', ], 'members' => [ 'comparison' => [ 'shape' => 'StringComparison', ], 'value' => [ 'shape' => 'StringInput', ], ], ], 'StringFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StringFilter', ], 'max' => 10, 'min' => 1, ], 'StringInput' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'SubnetId' => [ 'type' => 'string', 'pattern' => '^subnet-([a-z0-9]{8}|[a-z0-9]{17}|\\*)$', ], 'SubnetIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubnetId', ], 'max' => 16, 'min' => 0, ], 'SuggestedFix' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'SuggestedFixCodeString', ], 'description' => [ 'shape' => 'SuggestedFixDescriptionString', ], ], ], 'SuggestedFixCodeString' => [ 'type' => 'string', 'max' => 2500, 'min' => 1, ], 'SuggestedFixDescriptionString' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, ], 'SuggestedFixes' => [ 'type' => 'list', 'member' => [ 'shape' => 'SuggestedFix', ], 'max' => 5, 'min' => 1, ], 'TagComparison' => [ 'type' => 'string', 'enum' => [ 'EQUALS', ], ], 'TagFilter' => [ 'type' => 'structure', 'required' => [ 'comparison', 'key', 'value', ], 'members' => [ 'comparison' => [ 'shape' => 'TagComparison', ], 'key' => [ 'shape' => 'NonEmptyString', ], 'value' => [ 'shape' => 'NonEmptyString', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(?!aws:)[a-zA-Z+-=._:/]+$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 0, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'MapKey', ], 'value' => [ 'shape' => 'MapValue', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetResourceTagsValue', ], 'max' => 5, 'min' => 1, ], 'Target' => [ 'type' => 'string', 'max' => 50, 'min' => 0, ], 'TargetAccount' => [ 'type' => 'string', 'pattern' => '^\\d{12}|ALL_ACCOUNTS|SELF$', ], 'TargetAccountList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetAccount', ], 'max' => 10000, 'min' => 1, ], 'TargetResourceTags' => [ 'type' => 'map', 'key' => [ 'shape' => 'TargetResourceTagsKey', ], 'value' => [ 'shape' => 'TagValueList', ], 'max' => 5, 'min' => 1, ], 'TargetResourceTagsKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[\\p{L}\\p{Z}\\p{N}_.:/=\\-@]*$', ], 'TargetResourceTagsValue' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'TargetStatusFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CisTargetStatusFilter', ], 'max' => 10, 'min' => 1, ], 'TargetStatusReasonFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CisTargetStatusReasonFilter', ], 'max' => 10, 'min' => 1, ], 'Targets' => [ 'type' => 'list', 'member' => [ 'shape' => 'Target', ], 'min' => 0, ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'Time' => [ 'type' => 'structure', 'required' => [ 'timeOfDay', 'timezone', ], 'members' => [ 'timeOfDay' => [ 'shape' => 'TimeOfDay', ], 'timezone' => [ 'shape' => 'Timezone', ], ], ], 'TimeOfDay' => [ 'type' => 'string', 'pattern' => '^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$', ], 'Timestamp' => [ 'type' => 'timestamp', ], 'Timezone' => [ 'type' => 'string', 'max' => 50, 'min' => 1, ], 'TitleAggregation' => [ 'type' => 'structure', 'members' => [ 'findingType' => [ 'shape' => 'AggregationFindingType', ], 'resourceType' => [ 'shape' => 'AggregationResourceType', ], 'sortBy' => [ 'shape' => 'TitleSortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', ], 'titles' => [ 'shape' => 'StringFilterList', ], 'vulnerabilityIds' => [ 'shape' => 'StringFilterList', ], ], ], 'TitleAggregationResponse' => [ 'type' => 'structure', 'required' => [ 'title', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'severityCounts' => [ 'shape' => 'SeverityCounts', ], 'title' => [ 'shape' => 'NonEmptyString', ], 'vulnerabilityId' => [ 'shape' => 'String', ], ], ], 'TitleFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CisStringFilter', ], 'max' => 10, 'min' => 1, ], 'TitleSortBy' => [ 'type' => 'string', 'enum' => [ 'CRITICAL', 'HIGH', 'ALL', ], ], 'Tool' => [ 'type' => 'string', 'min' => 0, ], 'Tools' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tool', ], ], 'Ttp' => [ 'type' => 'string', 'max' => 30, 'min' => 0, ], 'Ttps' => [ 'type' => 'list', 'member' => [ 'shape' => 'Ttp', ], 'min' => 0, ], 'UUID' => [ 'type' => 'string', 'pattern' => '^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateCisScanConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'scanConfigurationArn', ], 'members' => [ 'scanConfigurationArn' => [ 'shape' => 'CisScanConfigurationArn', ], 'scanName' => [ 'shape' => 'CisScanName', ], 'schedule' => [ 'shape' => 'Schedule', ], 'securityLevel' => [ 'shape' => 'CisSecurityLevel', ], 'targets' => [ 'shape' => 'UpdateCisTargets', ], ], ], 'UpdateCisScanConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'scanConfigurationArn', ], 'members' => [ 'scanConfigurationArn' => [ 'shape' => 'CisScanConfigurationArn', ], ], ], 'UpdateCisTargets' => [ 'type' => 'structure', 'members' => [ 'accountIds' => [ 'shape' => 'TargetAccountList', ], 'targetResourceTags' => [ 'shape' => 'TargetResourceTags', ], ], ], 'UpdateConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'ec2Configuration' => [ 'shape' => 'Ec2Configuration', ], 'ecrConfiguration' => [ 'shape' => 'EcrConfiguration', ], ], ], 'UpdateConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateEc2DeepInspectionConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'activateDeepInspection' => [ 'shape' => 'Boolean', ], 'packagePaths' => [ 'shape' => 'PathList', ], ], ], 'UpdateEc2DeepInspectionConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'errorMessage' => [ 'shape' => 'NonEmptyString', ], 'orgPackagePaths' => [ 'shape' => 'PathList', ], 'packagePaths' => [ 'shape' => 'PathList', ], 'status' => [ 'shape' => 'Ec2DeepInspectionStatus', ], ], ], 'UpdateEncryptionKeyRequest' => [ 'type' => 'structure', 'required' => [ 'kmsKeyId', 'resourceType', 'scanType', ], 'members' => [ 'kmsKeyId' => [ 'shape' => 'KmsKeyArn', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'scanType' => [ 'shape' => 'ScanType', ], ], ], 'UpdateEncryptionKeyResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateFilterRequest' => [ 'type' => 'structure', 'required' => [ 'filterArn', ], 'members' => [ 'action' => [ 'shape' => 'FilterAction', ], 'description' => [ 'shape' => 'FilterDescription', ], 'filterArn' => [ 'shape' => 'FilterArn', ], 'filterCriteria' => [ 'shape' => 'FilterCriteria', ], 'name' => [ 'shape' => 'FilterName', ], 'reason' => [ 'shape' => 'FilterReason', ], ], ], 'UpdateFilterResponse' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'FilterArn', ], ], ], 'UpdateOrgEc2DeepInspectionConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'orgPackagePaths', ], 'members' => [ 'orgPackagePaths' => [ 'shape' => 'PathList', ], ], ], 'UpdateOrgEc2DeepInspectionConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateOrganizationConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'autoEnable', ], 'members' => [ 'autoEnable' => [ 'shape' => 'AutoEnable', ], ], ], 'UpdateOrganizationConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'autoEnable', ], 'members' => [ 'autoEnable' => [ 'shape' => 'AutoEnable', ], ], ], 'Usage' => [ 'type' => 'structure', 'members' => [ 'currency' => [ 'shape' => 'Currency', ], 'estimatedMonthlyCost' => [ 'shape' => 'MonthlyCostEstimate', ], 'total' => [ 'shape' => 'UsageValue', ], 'type' => [ 'shape' => 'UsageType', ], ], ], 'UsageAccountId' => [ 'type' => 'string', 'pattern' => '[0-9]{12}', ], 'UsageAccountIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UsageAccountId', ], 'max' => 7000, 'min' => 1, ], 'UsageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Usage', ], ], 'UsageTotal' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => 'MeteringAccountId', ], 'usage' => [ 'shape' => 'UsageList', ], ], ], 'UsageTotalList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UsageTotal', ], ], 'UsageType' => [ 'type' => 'string', 'enum' => [ 'EC2_INSTANCE_HOURS', 'ECR_INITIAL_SCAN', 'ECR_RESCAN', 'LAMBDA_FUNCTION_HOURS', 'LAMBDA_FUNCTION_CODE_HOURS', ], ], 'UsageValue' => [ 'type' => 'double', 'min' => 0, ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', 'reason', ], 'members' => [ 'fields' => [ 'shape' => 'ValidationExceptionFields', ], 'message' => [ 'shape' => 'String', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'message', 'name', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'CANNOT_PARSE', 'FIELD_VALIDATION_FAILED', 'OTHER', ], ], 'Vendor' => [ 'type' => 'string', 'max' => 16, 'min' => 0, ], 'VendorCreatedAt' => [ 'type' => 'timestamp', ], 'VendorSeverity' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'VendorUpdatedAt' => [ 'type' => 'timestamp', ], 'Version' => [ 'type' => 'string', 'pattern' => '^\\$LATEST|[0-9]+$', ], 'VpcId' => [ 'type' => 'string', 'pattern' => '^vpc-([a-z0-9]{8}|[a-z0-9]{17}|\\*)$', ], 'VulnId' => [ 'type' => 'string', 'pattern' => '^CVE-[12][0-9]{3}-[0-9]{1,10}$', ], 'VulnIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VulnId', ], 'max' => 1, 'min' => 1, ], 'Vulnerabilities' => [ 'type' => 'list', 'member' => [ 'shape' => 'Vulnerability', ], 'max' => 1, 'min' => 0, ], 'Vulnerability' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'atigData' => [ 'shape' => 'AtigData', ], 'cisaData' => [ 'shape' => 'CisaData', ], 'cvss2' => [ 'shape' => 'Cvss2', ], 'cvss3' => [ 'shape' => 'Cvss3', ], 'cwes' => [ 'shape' => 'Cwes', ], 'description' => [ 'shape' => 'VulnerabilityDescription', ], 'detectionPlatforms' => [ 'shape' => 'DetectionPlatforms', ], 'epss' => [ 'shape' => 'Epss', ], 'exploitObserved' => [ 'shape' => 'ExploitObserved', ], 'id' => [ 'shape' => 'NonEmptyString', ], 'referenceUrls' => [ 'shape' => 'VulnerabilityReferenceUrls', ], 'relatedVulnerabilities' => [ 'shape' => 'RelatedVulnerabilities', ], 'source' => [ 'shape' => 'VulnerabilitySource', ], 'sourceUrl' => [ 'shape' => 'VulnerabilitySourceUrl', ], 'vendorCreatedAt' => [ 'shape' => 'VendorCreatedAt', ], 'vendorSeverity' => [ 'shape' => 'VendorSeverity', ], 'vendorUpdatedAt' => [ 'shape' => 'VendorUpdatedAt', ], ], ], 'VulnerabilityDescription' => [ 'type' => 'string', ], 'VulnerabilityId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'VulnerabilityIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VulnerabilityId', ], ], 'VulnerabilityReferenceUrl' => [ 'type' => 'string', 'min' => 0, ], 'VulnerabilityReferenceUrls' => [ 'type' => 'list', 'member' => [ 'shape' => 'VulnerabilityReferenceUrl', ], 'max' => 100, 'min' => 0, ], 'VulnerabilitySource' => [ 'type' => 'string', 'enum' => [ 'NVD', ], ], 'VulnerabilitySourceUrl' => [ 'type' => 'string', 'min' => 0, ], 'VulnerablePackage' => [ 'type' => 'structure', 'required' => [ 'name', 'version', ], 'members' => [ 'arch' => [ 'shape' => 'PackageArchitecture', ], 'epoch' => [ 'shape' => 'PackageEpoch', ], 'filePath' => [ 'shape' => 'FilePath', ], 'fixedInVersion' => [ 'shape' => 'PackageVersion', ], 'name' => [ 'shape' => 'PackageName', ], 'packageManager' => [ 'shape' => 'PackageManager', ], 'release' => [ 'shape' => 'PackageRelease', ], 'remediation' => [ 'shape' => 'VulnerablePackageRemediation', ], 'sourceLambdaLayerArn' => [ 'shape' => 'LambdaLayerArn', ], 'sourceLayerHash' => [ 'shape' => 'SourceLayerHash', ], 'version' => [ 'shape' => 'PackageVersion', ], ], ], 'VulnerablePackageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VulnerablePackage', ], ], 'VulnerablePackageRemediation' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'WeeklySchedule' => [ 'type' => 'structure', 'required' => [ 'days', 'startTime', ], 'members' => [ 'days' => [ 'shape' => 'DaysList', ], 'startTime' => [ 'shape' => 'Time', ], ], ], ],];
