<?php
// This file was auto-generated from sdk-root/src/data/dynamodb/2012-08-10/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2012-08-10', 'endpointPrefix' => 'dynamodb', 'jsonVersion' => '1.0', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceAbbreviation' => 'DynamoDB', 'serviceFullName' => 'Amazon DynamoDB', 'serviceId' => 'DynamoDB', 'signatureVersion' => 'v4', 'targetPrefix' => 'DynamoDB_20120810', 'uid' => 'dynamodb-2012-08-10', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'BatchExecuteStatement' => [ 'name' => 'BatchExecuteStatement', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchExecuteStatementInput', ], 'output' => [ 'shape' => 'BatchExecuteStatementOutput', ], 'errors' => [ [ 'shape' => 'RequestLimitExceeded', ], [ 'shape' => 'InternalServerError', ], ], ], 'BatchGetItem' => [ 'name' => 'BatchGetItem', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetItemInput', ], 'output' => [ 'shape' => 'BatchGetItemOutput', ], 'errors' => [ [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'RequestLimitExceeded', ], [ 'shape' => 'InternalServerError', ], ], 'endpointdiscovery' => [], ], 'BatchWriteItem' => [ 'name' => 'BatchWriteItem', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchWriteItemInput', ], 'output' => [ 'shape' => 'BatchWriteItemOutput', ], 'errors' => [ [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ItemCollectionSizeLimitExceededException', ], [ 'shape' => 'RequestLimitExceeded', ], [ 'shape' => 'InternalServerError', ], ], 'endpointdiscovery' => [], ], 'CreateBackup' => [ 'name' => 'CreateBackup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateBackupInput', ], 'output' => [ 'shape' => 'CreateBackupOutput', ], 'errors' => [ [ 'shape' => 'TableNotFoundException', ], [ 'shape' => 'TableInUseException', ], [ 'shape' => 'ContinuousBackupsUnavailableException', ], [ 'shape' => 'BackupInUseException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalServerError', ], ], 'endpointdiscovery' => [], ], 'CreateGlobalTable' => [ 'name' => 'CreateGlobalTable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateGlobalTableInput', ], 'output' => [ 'shape' => 'CreateGlobalTableOutput', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'GlobalTableAlreadyExistsException', ], [ 'shape' => 'TableNotFoundException', ], ], 'endpointdiscovery' => [], ], 'CreateTable' => [ 'name' => 'CreateTable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateTableInput', ], 'output' => [ 'shape' => 'CreateTableOutput', ], 'errors' => [ [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalServerError', ], ], 'endpointdiscovery' => [], ], 'DeleteBackup' => [ 'name' => 'DeleteBackup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteBackupInput', ], 'output' => [ 'shape' => 'DeleteBackupOutput', ], 'errors' => [ [ 'shape' => 'BackupNotFoundException', ], [ 'shape' => 'BackupInUseException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalServerError', ], ], 'endpointdiscovery' => [], ], 'DeleteItem' => [ 'name' => 'DeleteItem', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteItemInput', ], 'output' => [ 'shape' => 'DeleteItemOutput', ], 'errors' => [ [ 'shape' => 'ConditionalCheckFailedException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ItemCollectionSizeLimitExceededException', ], [ 'shape' => 'TransactionConflictException', ], [ 'shape' => 'RequestLimitExceeded', ], [ 'shape' => 'InternalServerError', ], ], 'endpointdiscovery' => [], ], 'DeleteResourcePolicy' => [ 'name' => 'DeleteResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteResourcePolicyInput', ], 'output' => [ 'shape' => 'DeleteResourcePolicyOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'PolicyNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'LimitExceededException', ], ], 'endpointdiscovery' => [], ], 'DeleteTable' => [ 'name' => 'DeleteTable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTableInput', ], 'output' => [ 'shape' => 'DeleteTableOutput', ], 'errors' => [ [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalServerError', ], ], 'endpointdiscovery' => [], ], 'DescribeBackup' => [ 'name' => 'DescribeBackup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeBackupInput', ], 'output' => [ 'shape' => 'DescribeBackupOutput', ], 'errors' => [ [ 'shape' => 'BackupNotFoundException', ], [ 'shape' => 'InternalServerError', ], ], 'endpointdiscovery' => [], ], 'DescribeContinuousBackups' => [ 'name' => 'DescribeContinuousBackups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeContinuousBackupsInput', ], 'output' => [ 'shape' => 'DescribeContinuousBackupsOutput', ], 'errors' => [ [ 'shape' => 'TableNotFoundException', ], [ 'shape' => 'InternalServerError', ], ], 'endpointdiscovery' => [], ], 'DescribeContributorInsights' => [ 'name' => 'DescribeContributorInsights', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeContributorInsightsInput', ], 'output' => [ 'shape' => 'DescribeContributorInsightsOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeEndpoints' => [ 'name' => 'DescribeEndpoints', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEndpointsRequest', ], 'output' => [ 'shape' => 'DescribeEndpointsResponse', ], 'endpointoperation' => true, ], 'DescribeExport' => [ 'name' => 'DescribeExport', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeExportInput', ], 'output' => [ 'shape' => 'DescribeExportOutput', ], 'errors' => [ [ 'shape' => 'ExportNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeGlobalTable' => [ 'name' => 'DescribeGlobalTable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeGlobalTableInput', ], 'output' => [ 'shape' => 'DescribeGlobalTableOutput', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'GlobalTableNotFoundException', ], ], 'endpointdiscovery' => [], ], 'DescribeGlobalTableSettings' => [ 'name' => 'DescribeGlobalTableSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeGlobalTableSettingsInput', ], 'output' => [ 'shape' => 'DescribeGlobalTableSettingsOutput', ], 'errors' => [ [ 'shape' => 'GlobalTableNotFoundException', ], [ 'shape' => 'InternalServerError', ], ], 'endpointdiscovery' => [], ], 'DescribeImport' => [ 'name' => 'DescribeImport', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeImportInput', ], 'output' => [ 'shape' => 'DescribeImportOutput', ], 'errors' => [ [ 'shape' => 'ImportNotFoundException', ], ], ], 'DescribeKinesisStreamingDestination' => [ 'name' => 'DescribeKinesisStreamingDestination', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeKinesisStreamingDestinationInput', ], 'output' => [ 'shape' => 'DescribeKinesisStreamingDestinationOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerError', ], ], 'endpointdiscovery' => [], ], 'DescribeLimits' => [ 'name' => 'DescribeLimits', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeLimitsInput', ], 'output' => [ 'shape' => 'DescribeLimitsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], ], 'endpointdiscovery' => [], ], 'DescribeTable' => [ 'name' => 'DescribeTable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTableInput', ], 'output' => [ 'shape' => 'DescribeTableOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerError', ], ], 'endpointdiscovery' => [], ], 'DescribeTableReplicaAutoScaling' => [ 'name' => 'DescribeTableReplicaAutoScaling', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTableReplicaAutoScalingInput', ], 'output' => [ 'shape' => 'DescribeTableReplicaAutoScalingOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeTimeToLive' => [ 'name' => 'DescribeTimeToLive', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTimeToLiveInput', ], 'output' => [ 'shape' => 'DescribeTimeToLiveOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerError', ], ], 'endpointdiscovery' => [], ], 'DisableKinesisStreamingDestination' => [ 'name' => 'DisableKinesisStreamingDestination', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'KinesisStreamingDestinationInput', ], 'output' => [ 'shape' => 'KinesisStreamingDestinationOutput', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'endpointdiscovery' => [], ], 'EnableKinesisStreamingDestination' => [ 'name' => 'EnableKinesisStreamingDestination', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'KinesisStreamingDestinationInput', ], 'output' => [ 'shape' => 'KinesisStreamingDestinationOutput', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'endpointdiscovery' => [], ], 'ExecuteStatement' => [ 'name' => 'ExecuteStatement', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ExecuteStatementInput', ], 'output' => [ 'shape' => 'ExecuteStatementOutput', ], 'errors' => [ [ 'shape' => 'ConditionalCheckFailedException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ItemCollectionSizeLimitExceededException', ], [ 'shape' => 'TransactionConflictException', ], [ 'shape' => 'RequestLimitExceeded', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'DuplicateItemException', ], ], ], 'ExecuteTransaction' => [ 'name' => 'ExecuteTransaction', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ExecuteTransactionInput', ], 'output' => [ 'shape' => 'ExecuteTransactionOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TransactionCanceledException', ], [ 'shape' => 'TransactionInProgressException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'RequestLimitExceeded', ], [ 'shape' => 'InternalServerError', ], ], ], 'ExportTableToPointInTime' => [ 'name' => 'ExportTableToPointInTime', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ExportTableToPointInTimeInput', ], 'output' => [ 'shape' => 'ExportTableToPointInTimeOutput', ], 'errors' => [ [ 'shape' => 'TableNotFoundException', ], [ 'shape' => 'PointInTimeRecoveryUnavailableException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidExportTimeException', ], [ 'shape' => 'ExportConflictException', ], [ 'shape' => 'InternalServerError', ], ], ], 'GetItem' => [ 'name' => 'GetItem', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetItemInput', ], 'output' => [ 'shape' => 'GetItemOutput', ], 'errors' => [ [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'RequestLimitExceeded', ], [ 'shape' => 'InternalServerError', ], ], 'endpointdiscovery' => [], ], 'GetResourcePolicy' => [ 'name' => 'GetResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetResourcePolicyInput', ], 'output' => [ 'shape' => 'GetResourcePolicyOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'PolicyNotFoundException', ], ], 'endpointdiscovery' => [], ], 'ImportTable' => [ 'name' => 'ImportTable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ImportTableInput', ], 'output' => [ 'shape' => 'ImportTableOutput', ], 'errors' => [ [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ImportConflictException', ], ], ], 'ListBackups' => [ 'name' => 'ListBackups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListBackupsInput', ], 'output' => [ 'shape' => 'ListBackupsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], ], 'endpointdiscovery' => [], ], 'ListContributorInsights' => [ 'name' => 'ListContributorInsights', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListContributorInsightsInput', ], 'output' => [ 'shape' => 'ListContributorInsightsOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerError', ], ], ], 'ListExports' => [ 'name' => 'ListExports', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListExportsInput', ], 'output' => [ 'shape' => 'ListExportsOutput', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalServerError', ], ], ], 'ListGlobalTables' => [ 'name' => 'ListGlobalTables', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListGlobalTablesInput', ], 'output' => [ 'shape' => 'ListGlobalTablesOutput', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], ], 'endpointdiscovery' => [], ], 'ListImports' => [ 'name' => 'ListImports', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListImportsInput', ], 'output' => [ 'shape' => 'ListImportsOutput', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], ], ], 'ListTables' => [ 'name' => 'ListTables', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTablesInput', ], 'output' => [ 'shape' => 'ListTablesOutput', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], ], 'endpointdiscovery' => [], ], 'ListTagsOfResource' => [ 'name' => 'ListTagsOfResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsOfResourceInput', ], 'output' => [ 'shape' => 'ListTagsOfResourceOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerError', ], ], 'endpointdiscovery' => [], ], 'PutItem' => [ 'name' => 'PutItem', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutItemInput', ], 'output' => [ 'shape' => 'PutItemOutput', ], 'errors' => [ [ 'shape' => 'ConditionalCheckFailedException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ItemCollectionSizeLimitExceededException', ], [ 'shape' => 'TransactionConflictException', ], [ 'shape' => 'RequestLimitExceeded', ], [ 'shape' => 'InternalServerError', ], ], 'endpointdiscovery' => [], ], 'PutResourcePolicy' => [ 'name' => 'PutResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutResourcePolicyInput', ], 'output' => [ 'shape' => 'PutResourcePolicyOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'PolicyNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], 'endpointdiscovery' => [], ], 'Query' => [ 'name' => 'Query', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'QueryInput', ], 'output' => [ 'shape' => 'QueryOutput', ], 'errors' => [ [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'RequestLimitExceeded', ], [ 'shape' => 'InternalServerError', ], ], 'endpointdiscovery' => [], ], 'RestoreTableFromBackup' => [ 'name' => 'RestoreTableFromBackup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RestoreTableFromBackupInput', ], 'output' => [ 'shape' => 'RestoreTableFromBackupOutput', ], 'errors' => [ [ 'shape' => 'TableAlreadyExistsException', ], [ 'shape' => 'TableInUseException', ], [ 'shape' => 'BackupNotFoundException', ], [ 'shape' => 'BackupInUseException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalServerError', ], ], 'endpointdiscovery' => [], ], 'RestoreTableToPointInTime' => [ 'name' => 'RestoreTableToPointInTime', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RestoreTableToPointInTimeInput', ], 'output' => [ 'shape' => 'RestoreTableToPointInTimeOutput', ], 'errors' => [ [ 'shape' => 'TableAlreadyExistsException', ], [ 'shape' => 'TableNotFoundException', ], [ 'shape' => 'TableInUseException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidRestoreTimeException', ], [ 'shape' => 'PointInTimeRecoveryUnavailableException', ], [ 'shape' => 'InternalServerError', ], ], 'endpointdiscovery' => [], ], 'Scan' => [ 'name' => 'Scan', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ScanInput', ], 'output' => [ 'shape' => 'ScanOutput', ], 'errors' => [ [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'RequestLimitExceeded', ], [ 'shape' => 'InternalServerError', ], ], 'endpointdiscovery' => [], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceInput', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ResourceInUseException', ], ], 'endpointdiscovery' => [], ], 'TransactGetItems' => [ 'name' => 'TransactGetItems', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TransactGetItemsInput', ], 'output' => [ 'shape' => 'TransactGetItemsOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TransactionCanceledException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'RequestLimitExceeded', ], [ 'shape' => 'InternalServerError', ], ], 'endpointdiscovery' => [], ], 'TransactWriteItems' => [ 'name' => 'TransactWriteItems', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TransactWriteItemsInput', ], 'output' => [ 'shape' => 'TransactWriteItemsOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TransactionCanceledException', ], [ 'shape' => 'TransactionInProgressException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'RequestLimitExceeded', ], [ 'shape' => 'InternalServerError', ], ], 'endpointdiscovery' => [], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceInput', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ResourceInUseException', ], ], 'endpointdiscovery' => [], ], 'UpdateContinuousBackups' => [ 'name' => 'UpdateContinuousBackups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateContinuousBackupsInput', ], 'output' => [ 'shape' => 'UpdateContinuousBackupsOutput', ], 'errors' => [ [ 'shape' => 'TableNotFoundException', ], [ 'shape' => 'ContinuousBackupsUnavailableException', ], [ 'shape' => 'InternalServerError', ], ], 'endpointdiscovery' => [], ], 'UpdateContributorInsights' => [ 'name' => 'UpdateContributorInsights', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateContributorInsightsInput', ], 'output' => [ 'shape' => 'UpdateContributorInsightsOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerError', ], ], ], 'UpdateGlobalTable' => [ 'name' => 'UpdateGlobalTable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateGlobalTableInput', ], 'output' => [ 'shape' => 'UpdateGlobalTableOutput', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'GlobalTableNotFoundException', ], [ 'shape' => 'ReplicaAlreadyExistsException', ], [ 'shape' => 'ReplicaNotFoundException', ], [ 'shape' => 'TableNotFoundException', ], ], 'endpointdiscovery' => [], ], 'UpdateGlobalTableSettings' => [ 'name' => 'UpdateGlobalTableSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateGlobalTableSettingsInput', ], 'output' => [ 'shape' => 'UpdateGlobalTableSettingsOutput', ], 'errors' => [ [ 'shape' => 'GlobalTableNotFoundException', ], [ 'shape' => 'ReplicaNotFoundException', ], [ 'shape' => 'IndexNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'InternalServerError', ], ], 'endpointdiscovery' => [], ], 'UpdateItem' => [ 'name' => 'UpdateItem', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateItemInput', ], 'output' => [ 'shape' => 'UpdateItemOutput', ], 'errors' => [ [ 'shape' => 'ConditionalCheckFailedException', ], [ 'shape' => 'ProvisionedThroughputExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ItemCollectionSizeLimitExceededException', ], [ 'shape' => 'TransactionConflictException', ], [ 'shape' => 'RequestLimitExceeded', ], [ 'shape' => 'InternalServerError', ], ], 'endpointdiscovery' => [], ], 'UpdateKinesisStreamingDestination' => [ 'name' => 'UpdateKinesisStreamingDestination', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateKinesisStreamingDestinationInput', ], 'output' => [ 'shape' => 'UpdateKinesisStreamingDestinationOutput', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'endpointdiscovery' => [], ], 'UpdateTable' => [ 'name' => 'UpdateTable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateTableInput', ], 'output' => [ 'shape' => 'UpdateTableOutput', ], 'errors' => [ [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalServerError', ], ], 'endpointdiscovery' => [], ], 'UpdateTableReplicaAutoScaling' => [ 'name' => 'UpdateTableReplicaAutoScaling', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateTableReplicaAutoScalingInput', ], 'output' => [ 'shape' => 'UpdateTableReplicaAutoScalingOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalServerError', ], ], ], 'UpdateTimeToLive' => [ 'name' => 'UpdateTimeToLive', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateTimeToLiveInput', ], 'output' => [ 'shape' => 'UpdateTimeToLiveOutput', ], 'errors' => [ [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalServerError', ], ], 'endpointdiscovery' => [], ], ], 'shapes' => [ 'ApproximateCreationDateTimePrecision' => [ 'type' => 'string', 'enum' => [ 'MILLISECOND', 'MICROSECOND', ], ], 'ArchivalReason' => [ 'type' => 'string', ], 'ArchivalSummary' => [ 'type' => 'structure', 'members' => [ 'ArchivalDateTime' => [ 'shape' => 'Date', ], 'ArchivalReason' => [ 'shape' => 'ArchivalReason', ], 'ArchivalBackupArn' => [ 'shape' => 'BackupArn', ], ], ], 'AttributeAction' => [ 'type' => 'string', 'enum' => [ 'ADD', 'PUT', 'DELETE', ], ], 'AttributeDefinition' => [ 'type' => 'structure', 'required' => [ 'AttributeName', 'AttributeType', ], 'members' => [ 'AttributeName' => [ 'shape' => 'KeySchemaAttributeName', ], 'AttributeType' => [ 'shape' => 'ScalarAttributeType', ], ], ], 'AttributeDefinitions' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttributeDefinition', ], ], 'AttributeMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'AttributeName', ], 'value' => [ 'shape' => 'AttributeValue', ], ], 'AttributeName' => [ 'type' => 'string', 'max' => 65535, ], 'AttributeNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttributeName', ], 'min' => 1, ], 'AttributeUpdates' => [ 'type' => 'map', 'key' => [ 'shape' => 'AttributeName', ], 'value' => [ 'shape' => 'AttributeValueUpdate', ], ], 'AttributeValue' => [ 'type' => 'structure', 'members' => [ 'S' => [ 'shape' => 'StringAttributeValue', ], 'N' => [ 'shape' => 'NumberAttributeValue', ], 'B' => [ 'shape' => 'BinaryAttributeValue', ], 'SS' => [ 'shape' => 'StringSetAttributeValue', ], 'NS' => [ 'shape' => 'NumberSetAttributeValue', ], 'BS' => [ 'shape' => 'BinarySetAttributeValue', ], 'M' => [ 'shape' => 'MapAttributeValue', ], 'L' => [ 'shape' => 'ListAttributeValue', ], 'NULL' => [ 'shape' => 'NullAttributeValue', ], 'BOOL' => [ 'shape' => 'BooleanAttributeValue', ], ], ], 'AttributeValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttributeValue', ], ], 'AttributeValueUpdate' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'AttributeValue', ], 'Action' => [ 'shape' => 'AttributeAction', ], ], ], 'AutoScalingPolicyDescription' => [ 'type' => 'structure', 'members' => [ 'PolicyName' => [ 'shape' => 'AutoScalingPolicyName', ], 'TargetTrackingScalingPolicyConfiguration' => [ 'shape' => 'AutoScalingTargetTrackingScalingPolicyConfigurationDescription', ], ], ], 'AutoScalingPolicyDescriptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutoScalingPolicyDescription', ], ], 'AutoScalingPolicyName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '\\p{Print}+', ], 'AutoScalingPolicyUpdate' => [ 'type' => 'structure', 'required' => [ 'TargetTrackingScalingPolicyConfiguration', ], 'members' => [ 'PolicyName' => [ 'shape' => 'AutoScalingPolicyName', ], 'TargetTrackingScalingPolicyConfiguration' => [ 'shape' => 'AutoScalingTargetTrackingScalingPolicyConfigurationUpdate', ], ], ], 'AutoScalingRoleArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'AutoScalingSettingsDescription' => [ 'type' => 'structure', 'members' => [ 'MinimumUnits' => [ 'shape' => 'PositiveLongObject', ], 'MaximumUnits' => [ 'shape' => 'PositiveLongObject', ], 'AutoScalingDisabled' => [ 'shape' => 'BooleanObject', ], 'AutoScalingRoleArn' => [ 'shape' => 'String', ], 'ScalingPolicies' => [ 'shape' => 'AutoScalingPolicyDescriptionList', ], ], ], 'AutoScalingSettingsUpdate' => [ 'type' => 'structure', 'members' => [ 'MinimumUnits' => [ 'shape' => 'PositiveLongObject', ], 'MaximumUnits' => [ 'shape' => 'PositiveLongObject', ], 'AutoScalingDisabled' => [ 'shape' => 'BooleanObject', ], 'AutoScalingRoleArn' => [ 'shape' => 'AutoScalingRoleArn', ], 'ScalingPolicyUpdate' => [ 'shape' => 'AutoScalingPolicyUpdate', ], ], ], 'AutoScalingTargetTrackingScalingPolicyConfigurationDescription' => [ 'type' => 'structure', 'required' => [ 'TargetValue', ], 'members' => [ 'DisableScaleIn' => [ 'shape' => 'BooleanObject', ], 'ScaleInCooldown' => [ 'shape' => 'IntegerObject', ], 'ScaleOutCooldown' => [ 'shape' => 'IntegerObject', ], 'TargetValue' => [ 'shape' => 'DoubleObject', ], ], ], 'AutoScalingTargetTrackingScalingPolicyConfigurationUpdate' => [ 'type' => 'structure', 'required' => [ 'TargetValue', ], 'members' => [ 'DisableScaleIn' => [ 'shape' => 'BooleanObject', ], 'ScaleInCooldown' => [ 'shape' => 'IntegerObject', ], 'ScaleOutCooldown' => [ 'shape' => 'IntegerObject', ], 'TargetValue' => [ 'shape' => 'DoubleObject', ], ], ], 'Backfilling' => [ 'type' => 'boolean', ], 'BackupArn' => [ 'type' => 'string', 'max' => 1024, 'min' => 37, ], 'BackupCreationDateTime' => [ 'type' => 'timestamp', ], 'BackupDescription' => [ 'type' => 'structure', 'members' => [ 'BackupDetails' => [ 'shape' => 'BackupDetails', ], 'SourceTableDetails' => [ 'shape' => 'SourceTableDetails', ], 'SourceTableFeatureDetails' => [ 'shape' => 'SourceTableFeatureDetails', ], ], ], 'BackupDetails' => [ 'type' => 'structure', 'required' => [ 'BackupArn', 'BackupName', 'BackupStatus', 'BackupType', 'BackupCreationDateTime', ], 'members' => [ 'BackupArn' => [ 'shape' => 'BackupArn', ], 'BackupName' => [ 'shape' => 'BackupName', ], 'BackupSizeBytes' => [ 'shape' => 'BackupSizeBytes', ], 'BackupStatus' => [ 'shape' => 'BackupStatus', ], 'BackupType' => [ 'shape' => 'BackupType', ], 'BackupCreationDateTime' => [ 'shape' => 'BackupCreationDateTime', ], 'BackupExpiryDateTime' => [ 'shape' => 'Date', ], ], ], 'BackupInUseException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'BackupName' => [ 'type' => 'string', 'max' => 255, 'min' => 3, 'pattern' => '[a-zA-Z0-9_.-]+', ], 'BackupNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'BackupSizeBytes' => [ 'type' => 'long', 'min' => 0, ], 'BackupStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'DELETED', 'AVAILABLE', ], ], 'BackupSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BackupSummary', ], ], 'BackupSummary' => [ 'type' => 'structure', 'members' => [ 'TableName' => [ 'shape' => 'TableName', ], 'TableId' => [ 'shape' => 'TableId', ], 'TableArn' => [ 'shape' => 'TableArn', ], 'BackupArn' => [ 'shape' => 'BackupArn', ], 'BackupName' => [ 'shape' => 'BackupName', ], 'BackupCreationDateTime' => [ 'shape' => 'BackupCreationDateTime', ], 'BackupExpiryDateTime' => [ 'shape' => 'Date', ], 'BackupStatus' => [ 'shape' => 'BackupStatus', ], 'BackupType' => [ 'shape' => 'BackupType', ], 'BackupSizeBytes' => [ 'shape' => 'BackupSizeBytes', ], ], ], 'BackupType' => [ 'type' => 'string', 'enum' => [ 'USER', 'SYSTEM', 'AWS_BACKUP', ], ], 'BackupTypeFilter' => [ 'type' => 'string', 'enum' => [ 'USER', 'SYSTEM', 'AWS_BACKUP', 'ALL', ], ], 'BackupsInputLimit' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'BatchExecuteStatementInput' => [ 'type' => 'structure', 'required' => [ 'Statements', ], 'members' => [ 'Statements' => [ 'shape' => 'PartiQLBatchRequest', ], 'ReturnConsumedCapacity' => [ 'shape' => 'ReturnConsumedCapacity', ], ], ], 'BatchExecuteStatementOutput' => [ 'type' => 'structure', 'members' => [ 'Responses' => [ 'shape' => 'PartiQLBatchResponse', ], 'ConsumedCapacity' => [ 'shape' => 'ConsumedCapacityMultiple', ], ], ], 'BatchGetItemInput' => [ 'type' => 'structure', 'required' => [ 'RequestItems', ], 'members' => [ 'RequestItems' => [ 'shape' => 'BatchGetRequestMap', ], 'ReturnConsumedCapacity' => [ 'shape' => 'ReturnConsumedCapacity', ], ], ], 'BatchGetItemOutput' => [ 'type' => 'structure', 'members' => [ 'Responses' => [ 'shape' => 'BatchGetResponseMap', ], 'UnprocessedKeys' => [ 'shape' => 'BatchGetRequestMap', ], 'ConsumedCapacity' => [ 'shape' => 'ConsumedCapacityMultiple', ], ], ], 'BatchGetRequestMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TableArn', ], 'value' => [ 'shape' => 'KeysAndAttributes', ], 'max' => 100, 'min' => 1, ], 'BatchGetResponseMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TableArn', ], 'value' => [ 'shape' => 'ItemList', ], ], 'BatchStatementError' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'BatchStatementErrorCodeEnum', ], 'Message' => [ 'shape' => 'String', ], 'Item' => [ 'shape' => 'AttributeMap', ], ], ], 'BatchStatementErrorCodeEnum' => [ 'type' => 'string', 'enum' => [ 'ConditionalCheckFailed', 'ItemCollectionSizeLimitExceeded', 'RequestLimitExceeded', 'ValidationError', 'ProvisionedThroughputExceeded', 'TransactionConflict', 'ThrottlingError', 'InternalServerError', 'ResourceNotFound', 'AccessDenied', 'DuplicateItem', ], ], 'BatchStatementRequest' => [ 'type' => 'structure', 'required' => [ 'Statement', ], 'members' => [ 'Statement' => [ 'shape' => 'PartiQLStatement', ], 'Parameters' => [ 'shape' => 'PreparedStatementParameters', ], 'ConsistentRead' => [ 'shape' => 'ConsistentRead', ], 'ReturnValuesOnConditionCheckFailure' => [ 'shape' => 'ReturnValuesOnConditionCheckFailure', ], ], ], 'BatchStatementResponse' => [ 'type' => 'structure', 'members' => [ 'Error' => [ 'shape' => 'BatchStatementError', ], 'TableName' => [ 'shape' => 'TableName', ], 'Item' => [ 'shape' => 'AttributeMap', ], ], ], 'BatchWriteItemInput' => [ 'type' => 'structure', 'required' => [ 'RequestItems', ], 'members' => [ 'RequestItems' => [ 'shape' => 'BatchWriteItemRequestMap', ], 'ReturnConsumedCapacity' => [ 'shape' => 'ReturnConsumedCapacity', ], 'ReturnItemCollectionMetrics' => [ 'shape' => 'ReturnItemCollectionMetrics', ], ], ], 'BatchWriteItemOutput' => [ 'type' => 'structure', 'members' => [ 'UnprocessedItems' => [ 'shape' => 'BatchWriteItemRequestMap', ], 'ItemCollectionMetrics' => [ 'shape' => 'ItemCollectionMetricsPerTable', ], 'ConsumedCapacity' => [ 'shape' => 'ConsumedCapacityMultiple', ], ], ], 'BatchWriteItemRequestMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TableArn', ], 'value' => [ 'shape' => 'WriteRequests', ], 'max' => 25, 'min' => 1, ], 'BilledSizeBytes' => [ 'type' => 'long', 'min' => 0, ], 'BillingMode' => [ 'type' => 'string', 'enum' => [ 'PROVISIONED', 'PAY_PER_REQUEST', ], ], 'BillingModeSummary' => [ 'type' => 'structure', 'members' => [ 'BillingMode' => [ 'shape' => 'BillingMode', ], 'LastUpdateToPayPerRequestDateTime' => [ 'shape' => 'Date', ], ], ], 'BinaryAttributeValue' => [ 'type' => 'blob', ], 'BinarySetAttributeValue' => [ 'type' => 'list', 'member' => [ 'shape' => 'BinaryAttributeValue', ], ], 'BooleanAttributeValue' => [ 'type' => 'boolean', ], 'BooleanObject' => [ 'type' => 'boolean', ], 'CancellationReason' => [ 'type' => 'structure', 'members' => [ 'Item' => [ 'shape' => 'AttributeMap', ], 'Code' => [ 'shape' => 'Code', ], 'Message' => [ 'shape' => 'ErrorMessage', ], ], ], 'CancellationReasonList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CancellationReason', ], 'max' => 100, 'min' => 1, ], 'Capacity' => [ 'type' => 'structure', 'members' => [ 'ReadCapacityUnits' => [ 'shape' => 'ConsumedCapacityUnits', ], 'WriteCapacityUnits' => [ 'shape' => 'ConsumedCapacityUnits', ], 'CapacityUnits' => [ 'shape' => 'ConsumedCapacityUnits', ], ], ], 'ClientRequestToken' => [ 'type' => 'string', 'max' => 36, 'min' => 1, ], 'ClientToken' => [ 'type' => 'string', 'pattern' => '^[^\\$]+$', ], 'CloudWatchLogGroupArn' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'Code' => [ 'type' => 'string', ], 'ComparisonOperator' => [ 'type' => 'string', 'enum' => [ 'EQ', 'NE', 'IN', 'LE', 'LT', 'GE', 'GT', 'BETWEEN', 'NOT_NULL', 'NULL', 'CONTAINS', 'NOT_CONTAINS', 'BEGINS_WITH', ], ], 'Condition' => [ 'type' => 'structure', 'required' => [ 'ComparisonOperator', ], 'members' => [ 'AttributeValueList' => [ 'shape' => 'AttributeValueList', ], 'ComparisonOperator' => [ 'shape' => 'ComparisonOperator', ], ], ], 'ConditionCheck' => [ 'type' => 'structure', 'required' => [ 'Key', 'TableName', 'ConditionExpression', ], 'members' => [ 'Key' => [ 'shape' => 'Key', ], 'TableName' => [ 'shape' => 'TableArn', ], 'ConditionExpression' => [ 'shape' => 'ConditionExpression', ], 'ExpressionAttributeNames' => [ 'shape' => 'ExpressionAttributeNameMap', ], 'ExpressionAttributeValues' => [ 'shape' => 'ExpressionAttributeValueMap', ], 'ReturnValuesOnConditionCheckFailure' => [ 'shape' => 'ReturnValuesOnConditionCheckFailure', ], ], ], 'ConditionExpression' => [ 'type' => 'string', ], 'ConditionalCheckFailedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'Item' => [ 'shape' => 'AttributeMap', ], ], 'exception' => true, ], 'ConditionalOperator' => [ 'type' => 'string', 'enum' => [ 'AND', 'OR', ], ], 'ConfirmRemoveSelfResourceAccess' => [ 'type' => 'boolean', ], 'ConsistentRead' => [ 'type' => 'boolean', ], 'ConsumedCapacity' => [ 'type' => 'structure', 'members' => [ 'TableName' => [ 'shape' => 'TableArn', ], 'CapacityUnits' => [ 'shape' => 'ConsumedCapacityUnits', ], 'ReadCapacityUnits' => [ 'shape' => 'ConsumedCapacityUnits', ], 'WriteCapacityUnits' => [ 'shape' => 'ConsumedCapacityUnits', ], 'Table' => [ 'shape' => 'Capacity', ], 'LocalSecondaryIndexes' => [ 'shape' => 'SecondaryIndexesCapacityMap', ], 'GlobalSecondaryIndexes' => [ 'shape' => 'SecondaryIndexesCapacityMap', ], ], ], 'ConsumedCapacityMultiple' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConsumedCapacity', ], ], 'ConsumedCapacityUnits' => [ 'type' => 'double', ], 'ContinuousBackupsDescription' => [ 'type' => 'structure', 'required' => [ 'ContinuousBackupsStatus', ], 'members' => [ 'ContinuousBackupsStatus' => [ 'shape' => 'ContinuousBackupsStatus', ], 'PointInTimeRecoveryDescription' => [ 'shape' => 'PointInTimeRecoveryDescription', ], ], ], 'ContinuousBackupsStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'ContinuousBackupsUnavailableException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ContributorInsightsAction' => [ 'type' => 'string', 'enum' => [ 'ENABLE', 'DISABLE', ], ], 'ContributorInsightsRule' => [ 'type' => 'string', 'pattern' => '[A-Za-z0-9][A-Za-z0-9\\-\\_\\.]{0,126}[A-Za-z0-9]', ], 'ContributorInsightsRuleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContributorInsightsRule', ], ], 'ContributorInsightsStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLING', 'ENABLED', 'DISABLING', 'DISABLED', 'FAILED', ], ], 'ContributorInsightsSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContributorInsightsSummary', ], ], 'ContributorInsightsSummary' => [ 'type' => 'structure', 'members' => [ 'TableName' => [ 'shape' => 'TableName', ], 'IndexName' => [ 'shape' => 'IndexName', ], 'ContributorInsightsStatus' => [ 'shape' => 'ContributorInsightsStatus', ], ], ], 'CreateBackupInput' => [ 'type' => 'structure', 'required' => [ 'TableName', 'BackupName', ], 'members' => [ 'TableName' => [ 'shape' => 'TableArn', ], 'BackupName' => [ 'shape' => 'BackupName', ], ], ], 'CreateBackupOutput' => [ 'type' => 'structure', 'members' => [ 'BackupDetails' => [ 'shape' => 'BackupDetails', ], ], ], 'CreateGlobalSecondaryIndexAction' => [ 'type' => 'structure', 'required' => [ 'IndexName', 'KeySchema', 'Projection', ], 'members' => [ 'IndexName' => [ 'shape' => 'IndexName', ], 'KeySchema' => [ 'shape' => 'KeySchema', ], 'Projection' => [ 'shape' => 'Projection', ], 'ProvisionedThroughput' => [ 'shape' => 'ProvisionedThroughput', ], 'OnDemandThroughput' => [ 'shape' => 'OnDemandThroughput', ], ], ], 'CreateGlobalTableInput' => [ 'type' => 'structure', 'required' => [ 'GlobalTableName', 'ReplicationGroup', ], 'members' => [ 'GlobalTableName' => [ 'shape' => 'TableName', ], 'ReplicationGroup' => [ 'shape' => 'ReplicaList', ], ], ], 'CreateGlobalTableOutput' => [ 'type' => 'structure', 'members' => [ 'GlobalTableDescription' => [ 'shape' => 'GlobalTableDescription', ], ], ], 'CreateReplicaAction' => [ 'type' => 'structure', 'required' => [ 'RegionName', ], 'members' => [ 'RegionName' => [ 'shape' => 'RegionName', ], ], ], 'CreateReplicationGroupMemberAction' => [ 'type' => 'structure', 'required' => [ 'RegionName', ], 'members' => [ 'RegionName' => [ 'shape' => 'RegionName', ], 'KMSMasterKeyId' => [ 'shape' => 'KMSMasterKeyId', ], 'ProvisionedThroughputOverride' => [ 'shape' => 'ProvisionedThroughputOverride', ], 'OnDemandThroughputOverride' => [ 'shape' => 'OnDemandThroughputOverride', ], 'GlobalSecondaryIndexes' => [ 'shape' => 'ReplicaGlobalSecondaryIndexList', ], 'TableClassOverride' => [ 'shape' => 'TableClass', ], ], ], 'CreateTableInput' => [ 'type' => 'structure', 'required' => [ 'AttributeDefinitions', 'TableName', 'KeySchema', ], 'members' => [ 'AttributeDefinitions' => [ 'shape' => 'AttributeDefinitions', ], 'TableName' => [ 'shape' => 'TableArn', ], 'KeySchema' => [ 'shape' => 'KeySchema', ], 'LocalSecondaryIndexes' => [ 'shape' => 'LocalSecondaryIndexList', ], 'GlobalSecondaryIndexes' => [ 'shape' => 'GlobalSecondaryIndexList', ], 'BillingMode' => [ 'shape' => 'BillingMode', ], 'ProvisionedThroughput' => [ 'shape' => 'ProvisionedThroughput', ], 'StreamSpecification' => [ 'shape' => 'StreamSpecification', ], 'SSESpecification' => [ 'shape' => 'SSESpecification', ], 'Tags' => [ 'shape' => 'TagList', ], 'TableClass' => [ 'shape' => 'TableClass', ], 'DeletionProtectionEnabled' => [ 'shape' => 'DeletionProtectionEnabled', ], 'ResourcePolicy' => [ 'shape' => 'ResourcePolicy', ], 'OnDemandThroughput' => [ 'shape' => 'OnDemandThroughput', ], ], ], 'CreateTableOutput' => [ 'type' => 'structure', 'members' => [ 'TableDescription' => [ 'shape' => 'TableDescription', ], ], ], 'CsvDelimiter' => [ 'type' => 'string', 'max' => 1, 'min' => 1, 'pattern' => '[,;:|\\t ]', ], 'CsvHeader' => [ 'type' => 'string', 'max' => 65536, 'min' => 1, 'pattern' => '[\\x20-\\x21\\x23-\\x2B\\x2D-\\x7E]*', ], 'CsvHeaderList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CsvHeader', ], 'max' => 255, 'min' => 1, ], 'CsvOptions' => [ 'type' => 'structure', 'members' => [ 'Delimiter' => [ 'shape' => 'CsvDelimiter', ], 'HeaderList' => [ 'shape' => 'CsvHeaderList', ], ], ], 'Date' => [ 'type' => 'timestamp', ], 'Delete' => [ 'type' => 'structure', 'required' => [ 'Key', 'TableName', ], 'members' => [ 'Key' => [ 'shape' => 'Key', ], 'TableName' => [ 'shape' => 'TableArn', ], 'ConditionExpression' => [ 'shape' => 'ConditionExpression', ], 'ExpressionAttributeNames' => [ 'shape' => 'ExpressionAttributeNameMap', ], 'ExpressionAttributeValues' => [ 'shape' => 'ExpressionAttributeValueMap', ], 'ReturnValuesOnConditionCheckFailure' => [ 'shape' => 'ReturnValuesOnConditionCheckFailure', ], ], ], 'DeleteBackupInput' => [ 'type' => 'structure', 'required' => [ 'BackupArn', ], 'members' => [ 'BackupArn' => [ 'shape' => 'BackupArn', ], ], ], 'DeleteBackupOutput' => [ 'type' => 'structure', 'members' => [ 'BackupDescription' => [ 'shape' => 'BackupDescription', ], ], ], 'DeleteGlobalSecondaryIndexAction' => [ 'type' => 'structure', 'required' => [ 'IndexName', ], 'members' => [ 'IndexName' => [ 'shape' => 'IndexName', ], ], ], 'DeleteItemInput' => [ 'type' => 'structure', 'required' => [ 'TableName', 'Key', ], 'members' => [ 'TableName' => [ 'shape' => 'TableArn', ], 'Key' => [ 'shape' => 'Key', ], 'Expected' => [ 'shape' => 'ExpectedAttributeMap', ], 'ConditionalOperator' => [ 'shape' => 'ConditionalOperator', ], 'ReturnValues' => [ 'shape' => 'ReturnValue', ], 'ReturnConsumedCapacity' => [ 'shape' => 'ReturnConsumedCapacity', ], 'ReturnItemCollectionMetrics' => [ 'shape' => 'ReturnItemCollectionMetrics', ], 'ConditionExpression' => [ 'shape' => 'ConditionExpression', ], 'ExpressionAttributeNames' => [ 'shape' => 'ExpressionAttributeNameMap', ], 'ExpressionAttributeValues' => [ 'shape' => 'ExpressionAttributeValueMap', ], 'ReturnValuesOnConditionCheckFailure' => [ 'shape' => 'ReturnValuesOnConditionCheckFailure', ], ], ], 'DeleteItemOutput' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'AttributeMap', ], 'ConsumedCapacity' => [ 'shape' => 'ConsumedCapacity', ], 'ItemCollectionMetrics' => [ 'shape' => 'ItemCollectionMetrics', ], ], ], 'DeleteReplicaAction' => [ 'type' => 'structure', 'required' => [ 'RegionName', ], 'members' => [ 'RegionName' => [ 'shape' => 'RegionName', ], ], ], 'DeleteReplicationGroupMemberAction' => [ 'type' => 'structure', 'required' => [ 'RegionName', ], 'members' => [ 'RegionName' => [ 'shape' => 'RegionName', ], ], ], 'DeleteRequest' => [ 'type' => 'structure', 'required' => [ 'Key', ], 'members' => [ 'Key' => [ 'shape' => 'Key', ], ], ], 'DeleteResourcePolicyInput' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArnString', ], 'ExpectedRevisionId' => [ 'shape' => 'PolicyRevisionId', ], ], ], 'DeleteResourcePolicyOutput' => [ 'type' => 'structure', 'members' => [ 'RevisionId' => [ 'shape' => 'PolicyRevisionId', ], ], ], 'DeleteTableInput' => [ 'type' => 'structure', 'required' => [ 'TableName', ], 'members' => [ 'TableName' => [ 'shape' => 'TableArn', ], ], ], 'DeleteTableOutput' => [ 'type' => 'structure', 'members' => [ 'TableDescription' => [ 'shape' => 'TableDescription', ], ], ], 'DeletionProtectionEnabled' => [ 'type' => 'boolean', ], 'DescribeBackupInput' => [ 'type' => 'structure', 'required' => [ 'BackupArn', ], 'members' => [ 'BackupArn' => [ 'shape' => 'BackupArn', ], ], ], 'DescribeBackupOutput' => [ 'type' => 'structure', 'members' => [ 'BackupDescription' => [ 'shape' => 'BackupDescription', ], ], ], 'DescribeContinuousBackupsInput' => [ 'type' => 'structure', 'required' => [ 'TableName', ], 'members' => [ 'TableName' => [ 'shape' => 'TableArn', ], ], ], 'DescribeContinuousBackupsOutput' => [ 'type' => 'structure', 'members' => [ 'ContinuousBackupsDescription' => [ 'shape' => 'ContinuousBackupsDescription', ], ], ], 'DescribeContributorInsightsInput' => [ 'type' => 'structure', 'required' => [ 'TableName', ], 'members' => [ 'TableName' => [ 'shape' => 'TableArn', ], 'IndexName' => [ 'shape' => 'IndexName', ], ], ], 'DescribeContributorInsightsOutput' => [ 'type' => 'structure', 'members' => [ 'TableName' => [ 'shape' => 'TableName', ], 'IndexName' => [ 'shape' => 'IndexName', ], 'ContributorInsightsRuleList' => [ 'shape' => 'ContributorInsightsRuleList', ], 'ContributorInsightsStatus' => [ 'shape' => 'ContributorInsightsStatus', ], 'LastUpdateDateTime' => [ 'shape' => 'LastUpdateDateTime', ], 'FailureException' => [ 'shape' => 'FailureException', ], ], ], 'DescribeEndpointsRequest' => [ 'type' => 'structure', 'members' => [], ], 'DescribeEndpointsResponse' => [ 'type' => 'structure', 'required' => [ 'Endpoints', ], 'members' => [ 'Endpoints' => [ 'shape' => 'Endpoints', ], ], ], 'DescribeExportInput' => [ 'type' => 'structure', 'required' => [ 'ExportArn', ], 'members' => [ 'ExportArn' => [ 'shape' => 'ExportArn', ], ], ], 'DescribeExportOutput' => [ 'type' => 'structure', 'members' => [ 'ExportDescription' => [ 'shape' => 'ExportDescription', ], ], ], 'DescribeGlobalTableInput' => [ 'type' => 'structure', 'required' => [ 'GlobalTableName', ], 'members' => [ 'GlobalTableName' => [ 'shape' => 'TableName', ], ], ], 'DescribeGlobalTableOutput' => [ 'type' => 'structure', 'members' => [ 'GlobalTableDescription' => [ 'shape' => 'GlobalTableDescription', ], ], ], 'DescribeGlobalTableSettingsInput' => [ 'type' => 'structure', 'required' => [ 'GlobalTableName', ], 'members' => [ 'GlobalTableName' => [ 'shape' => 'TableName', ], ], ], 'DescribeGlobalTableSettingsOutput' => [ 'type' => 'structure', 'members' => [ 'GlobalTableName' => [ 'shape' => 'TableName', ], 'ReplicaSettings' => [ 'shape' => 'ReplicaSettingsDescriptionList', ], ], ], 'DescribeImportInput' => [ 'type' => 'structure', 'required' => [ 'ImportArn', ], 'members' => [ 'ImportArn' => [ 'shape' => 'ImportArn', ], ], ], 'DescribeImportOutput' => [ 'type' => 'structure', 'required' => [ 'ImportTableDescription', ], 'members' => [ 'ImportTableDescription' => [ 'shape' => 'ImportTableDescription', ], ], ], 'DescribeKinesisStreamingDestinationInput' => [ 'type' => 'structure', 'required' => [ 'TableName', ], 'members' => [ 'TableName' => [ 'shape' => 'TableArn', ], ], ], 'DescribeKinesisStreamingDestinationOutput' => [ 'type' => 'structure', 'members' => [ 'TableName' => [ 'shape' => 'TableName', ], 'KinesisDataStreamDestinations' => [ 'shape' => 'KinesisDataStreamDestinations', ], ], ], 'DescribeLimitsInput' => [ 'type' => 'structure', 'members' => [], ], 'DescribeLimitsOutput' => [ 'type' => 'structure', 'members' => [ 'AccountMaxReadCapacityUnits' => [ 'shape' => 'PositiveLongObject', ], 'AccountMaxWriteCapacityUnits' => [ 'shape' => 'PositiveLongObject', ], 'TableMaxReadCapacityUnits' => [ 'shape' => 'PositiveLongObject', ], 'TableMaxWriteCapacityUnits' => [ 'shape' => 'PositiveLongObject', ], ], ], 'DescribeTableInput' => [ 'type' => 'structure', 'required' => [ 'TableName', ], 'members' => [ 'TableName' => [ 'shape' => 'TableArn', ], ], ], 'DescribeTableOutput' => [ 'type' => 'structure', 'members' => [ 'Table' => [ 'shape' => 'TableDescription', ], ], ], 'DescribeTableReplicaAutoScalingInput' => [ 'type' => 'structure', 'required' => [ 'TableName', ], 'members' => [ 'TableName' => [ 'shape' => 'TableArn', ], ], ], 'DescribeTableReplicaAutoScalingOutput' => [ 'type' => 'structure', 'members' => [ 'TableAutoScalingDescription' => [ 'shape' => 'TableAutoScalingDescription', ], ], ], 'DescribeTimeToLiveInput' => [ 'type' => 'structure', 'required' => [ 'TableName', ], 'members' => [ 'TableName' => [ 'shape' => 'TableArn', ], ], ], 'DescribeTimeToLiveOutput' => [ 'type' => 'structure', 'members' => [ 'TimeToLiveDescription' => [ 'shape' => 'TimeToLiveDescription', ], ], ], 'DestinationStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLING', 'ACTIVE', 'DISABLING', 'DISABLED', 'ENABLE_FAILED', 'UPDATING', ], ], 'DoubleObject' => [ 'type' => 'double', ], 'DuplicateItemException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'EnableKinesisStreamingConfiguration' => [ 'type' => 'structure', 'members' => [ 'ApproximateCreationDateTimePrecision' => [ 'shape' => 'ApproximateCreationDateTimePrecision', ], ], ], 'Endpoint' => [ 'type' => 'structure', 'required' => [ 'Address', 'CachePeriodInMinutes', ], 'members' => [ 'Address' => [ 'shape' => 'String', ], 'CachePeriodInMinutes' => [ 'shape' => 'Long', ], ], ], 'Endpoints' => [ 'type' => 'list', 'member' => [ 'shape' => 'Endpoint', ], ], 'ErrorCount' => [ 'type' => 'long', 'min' => 0, ], 'ErrorMessage' => [ 'type' => 'string', ], 'ExceptionDescription' => [ 'type' => 'string', ], 'ExceptionName' => [ 'type' => 'string', ], 'ExecuteStatementInput' => [ 'type' => 'structure', 'required' => [ 'Statement', ], 'members' => [ 'Statement' => [ 'shape' => 'PartiQLStatement', ], 'Parameters' => [ 'shape' => 'PreparedStatementParameters', ], 'ConsistentRead' => [ 'shape' => 'ConsistentRead', ], 'NextToken' => [ 'shape' => 'PartiQLNextToken', ], 'ReturnConsumedCapacity' => [ 'shape' => 'ReturnConsumedCapacity', ], 'Limit' => [ 'shape' => 'PositiveIntegerObject', ], 'ReturnValuesOnConditionCheckFailure' => [ 'shape' => 'ReturnValuesOnConditionCheckFailure', ], ], ], 'ExecuteStatementOutput' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'ItemList', ], 'NextToken' => [ 'shape' => 'PartiQLNextToken', ], 'ConsumedCapacity' => [ 'shape' => 'ConsumedCapacity', ], 'LastEvaluatedKey' => [ 'shape' => 'Key', ], ], ], 'ExecuteTransactionInput' => [ 'type' => 'structure', 'required' => [ 'TransactStatements', ], 'members' => [ 'TransactStatements' => [ 'shape' => 'ParameterizedStatements', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'ReturnConsumedCapacity' => [ 'shape' => 'ReturnConsumedCapacity', ], ], ], 'ExecuteTransactionOutput' => [ 'type' => 'structure', 'members' => [ 'Responses' => [ 'shape' => 'ItemResponseList', ], 'ConsumedCapacity' => [ 'shape' => 'ConsumedCapacityMultiple', ], ], ], 'ExpectedAttributeMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'AttributeName', ], 'value' => [ 'shape' => 'ExpectedAttributeValue', ], ], 'ExpectedAttributeValue' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'AttributeValue', ], 'Exists' => [ 'shape' => 'BooleanObject', ], 'ComparisonOperator' => [ 'shape' => 'ComparisonOperator', ], 'AttributeValueList' => [ 'shape' => 'AttributeValueList', ], ], ], 'ExportArn' => [ 'type' => 'string', 'max' => 1024, 'min' => 37, ], 'ExportConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ExportDescription' => [ 'type' => 'structure', 'members' => [ 'ExportArn' => [ 'shape' => 'ExportArn', ], 'ExportStatus' => [ 'shape' => 'ExportStatus', ], 'StartTime' => [ 'shape' => 'ExportStartTime', ], 'EndTime' => [ 'shape' => 'ExportEndTime', ], 'ExportManifest' => [ 'shape' => 'ExportManifest', ], 'TableArn' => [ 'shape' => 'TableArn', ], 'TableId' => [ 'shape' => 'TableId', ], 'ExportTime' => [ 'shape' => 'ExportTime', ], 'ClientToken' => [ 'shape' => 'ClientToken', ], 'S3Bucket' => [ 'shape' => 'S3Bucket', ], 'S3BucketOwner' => [ 'shape' => 'S3BucketOwner', ], 'S3Prefix' => [ 'shape' => 'S3Prefix', ], 'S3SseAlgorithm' => [ 'shape' => 'S3SseAlgorithm', ], 'S3SseKmsKeyId' => [ 'shape' => 'S3SseKmsKeyId', ], 'FailureCode' => [ 'shape' => 'FailureCode', ], 'FailureMessage' => [ 'shape' => 'FailureMessage', ], 'ExportFormat' => [ 'shape' => 'ExportFormat', ], 'BilledSizeBytes' => [ 'shape' => 'BilledSizeBytes', ], 'ItemCount' => [ 'shape' => 'ItemCount', ], 'ExportType' => [ 'shape' => 'ExportType', ], 'IncrementalExportSpecification' => [ 'shape' => 'IncrementalExportSpecification', ], ], ], 'ExportEndTime' => [ 'type' => 'timestamp', ], 'ExportFormat' => [ 'type' => 'string', 'enum' => [ 'DYNAMODB_JSON', 'ION', ], ], 'ExportFromTime' => [ 'type' => 'timestamp', ], 'ExportManifest' => [ 'type' => 'string', ], 'ExportNextToken' => [ 'type' => 'string', ], 'ExportNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ExportStartTime' => [ 'type' => 'timestamp', ], 'ExportStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'COMPLETED', 'FAILED', ], ], 'ExportSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExportSummary', ], ], 'ExportSummary' => [ 'type' => 'structure', 'members' => [ 'ExportArn' => [ 'shape' => 'ExportArn', ], 'ExportStatus' => [ 'shape' => 'ExportStatus', ], 'ExportType' => [ 'shape' => 'ExportType', ], ], ], 'ExportTableToPointInTimeInput' => [ 'type' => 'structure', 'required' => [ 'TableArn', 'S3Bucket', ], 'members' => [ 'TableArn' => [ 'shape' => 'TableArn', ], 'ExportTime' => [ 'shape' => 'ExportTime', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'S3Bucket' => [ 'shape' => 'S3Bucket', ], 'S3BucketOwner' => [ 'shape' => 'S3BucketOwner', ], 'S3Prefix' => [ 'shape' => 'S3Prefix', ], 'S3SseAlgorithm' => [ 'shape' => 'S3SseAlgorithm', ], 'S3SseKmsKeyId' => [ 'shape' => 'S3SseKmsKeyId', ], 'ExportFormat' => [ 'shape' => 'ExportFormat', ], 'ExportType' => [ 'shape' => 'ExportType', ], 'IncrementalExportSpecification' => [ 'shape' => 'IncrementalExportSpecification', ], ], ], 'ExportTableToPointInTimeOutput' => [ 'type' => 'structure', 'members' => [ 'ExportDescription' => [ 'shape' => 'ExportDescription', ], ], ], 'ExportTime' => [ 'type' => 'timestamp', ], 'ExportToTime' => [ 'type' => 'timestamp', ], 'ExportType' => [ 'type' => 'string', 'enum' => [ 'FULL_EXPORT', 'INCREMENTAL_EXPORT', ], ], 'ExportViewType' => [ 'type' => 'string', 'enum' => [ 'NEW_IMAGE', 'NEW_AND_OLD_IMAGES', ], ], 'ExpressionAttributeNameMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ExpressionAttributeNameVariable', ], 'value' => [ 'shape' => 'AttributeName', ], ], 'ExpressionAttributeNameVariable' => [ 'type' => 'string', ], 'ExpressionAttributeValueMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ExpressionAttributeValueVariable', ], 'value' => [ 'shape' => 'AttributeValue', ], ], 'ExpressionAttributeValueVariable' => [ 'type' => 'string', ], 'FailureCode' => [ 'type' => 'string', ], 'FailureException' => [ 'type' => 'structure', 'members' => [ 'ExceptionName' => [ 'shape' => 'ExceptionName', ], 'ExceptionDescription' => [ 'shape' => 'ExceptionDescription', ], ], ], 'FailureMessage' => [ 'type' => 'string', ], 'FilterConditionMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'AttributeName', ], 'value' => [ 'shape' => 'Condition', ], ], 'Get' => [ 'type' => 'structure', 'required' => [ 'Key', 'TableName', ], 'members' => [ 'Key' => [ 'shape' => 'Key', ], 'TableName' => [ 'shape' => 'TableArn', ], 'ProjectionExpression' => [ 'shape' => 'ProjectionExpression', ], 'ExpressionAttributeNames' => [ 'shape' => 'ExpressionAttributeNameMap', ], ], ], 'GetItemInput' => [ 'type' => 'structure', 'required' => [ 'TableName', 'Key', ], 'members' => [ 'TableName' => [ 'shape' => 'TableArn', ], 'Key' => [ 'shape' => 'Key', ], 'AttributesToGet' => [ 'shape' => 'AttributeNameList', ], 'ConsistentRead' => [ 'shape' => 'ConsistentRead', ], 'ReturnConsumedCapacity' => [ 'shape' => 'ReturnConsumedCapacity', ], 'ProjectionExpression' => [ 'shape' => 'ProjectionExpression', ], 'ExpressionAttributeNames' => [ 'shape' => 'ExpressionAttributeNameMap', ], ], ], 'GetItemOutput' => [ 'type' => 'structure', 'members' => [ 'Item' => [ 'shape' => 'AttributeMap', ], 'ConsumedCapacity' => [ 'shape' => 'ConsumedCapacity', ], ], ], 'GetResourcePolicyInput' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArnString', ], ], ], 'GetResourcePolicyOutput' => [ 'type' => 'structure', 'members' => [ 'Policy' => [ 'shape' => 'ResourcePolicy', ], 'RevisionId' => [ 'shape' => 'PolicyRevisionId', ], ], ], 'GlobalSecondaryIndex' => [ 'type' => 'structure', 'required' => [ 'IndexName', 'KeySchema', 'Projection', ], 'members' => [ 'IndexName' => [ 'shape' => 'IndexName', ], 'KeySchema' => [ 'shape' => 'KeySchema', ], 'Projection' => [ 'shape' => 'Projection', ], 'ProvisionedThroughput' => [ 'shape' => 'ProvisionedThroughput', ], 'OnDemandThroughput' => [ 'shape' => 'OnDemandThroughput', ], ], ], 'GlobalSecondaryIndexAutoScalingUpdate' => [ 'type' => 'structure', 'members' => [ 'IndexName' => [ 'shape' => 'IndexName', ], 'ProvisionedWriteCapacityAutoScalingUpdate' => [ 'shape' => 'AutoScalingSettingsUpdate', ], ], ], 'GlobalSecondaryIndexAutoScalingUpdateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GlobalSecondaryIndexAutoScalingUpdate', ], 'min' => 1, ], 'GlobalSecondaryIndexDescription' => [ 'type' => 'structure', 'members' => [ 'IndexName' => [ 'shape' => 'IndexName', ], 'KeySchema' => [ 'shape' => 'KeySchema', ], 'Projection' => [ 'shape' => 'Projection', ], 'IndexStatus' => [ 'shape' => 'IndexStatus', ], 'Backfilling' => [ 'shape' => 'Backfilling', ], 'ProvisionedThroughput' => [ 'shape' => 'ProvisionedThroughputDescription', ], 'IndexSizeBytes' => [ 'shape' => 'LongObject', ], 'ItemCount' => [ 'shape' => 'LongObject', ], 'IndexArn' => [ 'shape' => 'String', ], 'OnDemandThroughput' => [ 'shape' => 'OnDemandThroughput', ], ], ], 'GlobalSecondaryIndexDescriptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GlobalSecondaryIndexDescription', ], ], 'GlobalSecondaryIndexInfo' => [ 'type' => 'structure', 'members' => [ 'IndexName' => [ 'shape' => 'IndexName', ], 'KeySchema' => [ 'shape' => 'KeySchema', ], 'Projection' => [ 'shape' => 'Projection', ], 'ProvisionedThroughput' => [ 'shape' => 'ProvisionedThroughput', ], 'OnDemandThroughput' => [ 'shape' => 'OnDemandThroughput', ], ], ], 'GlobalSecondaryIndexList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GlobalSecondaryIndex', ], ], 'GlobalSecondaryIndexUpdate' => [ 'type' => 'structure', 'members' => [ 'Update' => [ 'shape' => 'UpdateGlobalSecondaryIndexAction', ], 'Create' => [ 'shape' => 'CreateGlobalSecondaryIndexAction', ], 'Delete' => [ 'shape' => 'DeleteGlobalSecondaryIndexAction', ], ], ], 'GlobalSecondaryIndexUpdateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GlobalSecondaryIndexUpdate', ], ], 'GlobalSecondaryIndexes' => [ 'type' => 'list', 'member' => [ 'shape' => 'GlobalSecondaryIndexInfo', ], ], 'GlobalTable' => [ 'type' => 'structure', 'members' => [ 'GlobalTableName' => [ 'shape' => 'TableName', ], 'ReplicationGroup' => [ 'shape' => 'ReplicaList', ], ], ], 'GlobalTableAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'GlobalTableArnString' => [ 'type' => 'string', ], 'GlobalTableDescription' => [ 'type' => 'structure', 'members' => [ 'ReplicationGroup' => [ 'shape' => 'ReplicaDescriptionList', ], 'GlobalTableArn' => [ 'shape' => 'GlobalTableArnString', ], 'CreationDateTime' => [ 'shape' => 'Date', ], 'GlobalTableStatus' => [ 'shape' => 'GlobalTableStatus', ], 'GlobalTableName' => [ 'shape' => 'TableName', ], ], ], 'GlobalTableGlobalSecondaryIndexSettingsUpdate' => [ 'type' => 'structure', 'required' => [ 'IndexName', ], 'members' => [ 'IndexName' => [ 'shape' => 'IndexName', ], 'ProvisionedWriteCapacityUnits' => [ 'shape' => 'PositiveLongObject', ], 'ProvisionedWriteCapacityAutoScalingSettingsUpdate' => [ 'shape' => 'AutoScalingSettingsUpdate', ], ], ], 'GlobalTableGlobalSecondaryIndexSettingsUpdateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GlobalTableGlobalSecondaryIndexSettingsUpdate', ], 'max' => 20, 'min' => 1, ], 'GlobalTableList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GlobalTable', ], ], 'GlobalTableNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'GlobalTableStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'DELETING', 'UPDATING', ], ], 'IdempotentParameterMismatchException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ImportArn' => [ 'type' => 'string', 'max' => 1024, 'min' => 37, ], 'ImportConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ImportEndTime' => [ 'type' => 'timestamp', ], 'ImportNextToken' => [ 'type' => 'string', 'max' => 1024, 'min' => 112, 'pattern' => '([0-9a-f]{16})+', ], 'ImportNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ImportStartTime' => [ 'type' => 'timestamp', ], 'ImportStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'COMPLETED', 'CANCELLING', 'CANCELLED', 'FAILED', ], ], 'ImportSummary' => [ 'type' => 'structure', 'members' => [ 'ImportArn' => [ 'shape' => 'ImportArn', ], 'ImportStatus' => [ 'shape' => 'ImportStatus', ], 'TableArn' => [ 'shape' => 'TableArn', ], 'S3BucketSource' => [ 'shape' => 'S3BucketSource', ], 'CloudWatchLogGroupArn' => [ 'shape' => 'CloudWatchLogGroupArn', ], 'InputFormat' => [ 'shape' => 'InputFormat', ], 'StartTime' => [ 'shape' => 'ImportStartTime', ], 'EndTime' => [ 'shape' => 'ImportEndTime', ], ], ], 'ImportSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImportSummary', ], ], 'ImportTableDescription' => [ 'type' => 'structure', 'members' => [ 'ImportArn' => [ 'shape' => 'ImportArn', ], 'ImportStatus' => [ 'shape' => 'ImportStatus', ], 'TableArn' => [ 'shape' => 'TableArn', ], 'TableId' => [ 'shape' => 'TableId', ], 'ClientToken' => [ 'shape' => 'ClientToken', ], 'S3BucketSource' => [ 'shape' => 'S3BucketSource', ], 'ErrorCount' => [ 'shape' => 'ErrorCount', ], 'CloudWatchLogGroupArn' => [ 'shape' => 'CloudWatchLogGroupArn', ], 'InputFormat' => [ 'shape' => 'InputFormat', ], 'InputFormatOptions' => [ 'shape' => 'InputFormatOptions', ], 'InputCompressionType' => [ 'shape' => 'InputCompressionType', ], 'TableCreationParameters' => [ 'shape' => 'TableCreationParameters', ], 'StartTime' => [ 'shape' => 'ImportStartTime', ], 'EndTime' => [ 'shape' => 'ImportEndTime', ], 'ProcessedSizeBytes' => [ 'shape' => 'LongObject', ], 'ProcessedItemCount' => [ 'shape' => 'ProcessedItemCount', ], 'ImportedItemCount' => [ 'shape' => 'ImportedItemCount', ], 'FailureCode' => [ 'shape' => 'FailureCode', ], 'FailureMessage' => [ 'shape' => 'FailureMessage', ], ], ], 'ImportTableInput' => [ 'type' => 'structure', 'required' => [ 'S3BucketSource', 'InputFormat', 'TableCreationParameters', ], 'members' => [ 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'S3BucketSource' => [ 'shape' => 'S3BucketSource', ], 'InputFormat' => [ 'shape' => 'InputFormat', ], 'InputFormatOptions' => [ 'shape' => 'InputFormatOptions', ], 'InputCompressionType' => [ 'shape' => 'InputCompressionType', ], 'TableCreationParameters' => [ 'shape' => 'TableCreationParameters', ], ], ], 'ImportTableOutput' => [ 'type' => 'structure', 'required' => [ 'ImportTableDescription', ], 'members' => [ 'ImportTableDescription' => [ 'shape' => 'ImportTableDescription', ], ], ], 'ImportedItemCount' => [ 'type' => 'long', 'min' => 0, ], 'IncrementalExportSpecification' => [ 'type' => 'structure', 'members' => [ 'ExportFromTime' => [ 'shape' => 'ExportFromTime', ], 'ExportToTime' => [ 'shape' => 'ExportToTime', ], 'ExportViewType' => [ 'shape' => 'ExportViewType', ], ], ], 'IndexName' => [ 'type' => 'string', 'max' => 255, 'min' => 3, 'pattern' => '[a-zA-Z0-9_.-]+', ], 'IndexNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'IndexStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'UPDATING', 'DELETING', 'ACTIVE', ], ], 'InputCompressionType' => [ 'type' => 'string', 'enum' => [ 'GZIP', 'ZSTD', 'NONE', ], ], 'InputFormat' => [ 'type' => 'string', 'enum' => [ 'DYNAMODB_JSON', 'ION', 'CSV', ], ], 'InputFormatOptions' => [ 'type' => 'structure', 'members' => [ 'Csv' => [ 'shape' => 'CsvOptions', ], ], ], 'Integer' => [ 'type' => 'integer', ], 'IntegerObject' => [ 'type' => 'integer', ], 'InternalServerError' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, 'fault' => true, ], 'InvalidExportTimeException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'InvalidRestoreTimeException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ItemCollectionKeyAttributeMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'AttributeName', ], 'value' => [ 'shape' => 'AttributeValue', ], ], 'ItemCollectionMetrics' => [ 'type' => 'structure', 'members' => [ 'ItemCollectionKey' => [ 'shape' => 'ItemCollectionKeyAttributeMap', ], 'SizeEstimateRangeGB' => [ 'shape' => 'ItemCollectionSizeEstimateRange', ], ], ], 'ItemCollectionMetricsMultiple' => [ 'type' => 'list', 'member' => [ 'shape' => 'ItemCollectionMetrics', ], ], 'ItemCollectionMetricsPerTable' => [ 'type' => 'map', 'key' => [ 'shape' => 'TableArn', ], 'value' => [ 'shape' => 'ItemCollectionMetricsMultiple', ], ], 'ItemCollectionSizeEstimateBound' => [ 'type' => 'double', ], 'ItemCollectionSizeEstimateRange' => [ 'type' => 'list', 'member' => [ 'shape' => 'ItemCollectionSizeEstimateBound', ], ], 'ItemCollectionSizeLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ItemCount' => [ 'type' => 'long', 'min' => 0, ], 'ItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttributeMap', ], ], 'ItemResponse' => [ 'type' => 'structure', 'members' => [ 'Item' => [ 'shape' => 'AttributeMap', ], ], ], 'ItemResponseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ItemResponse', ], 'max' => 100, 'min' => 1, ], 'KMSMasterKeyArn' => [ 'type' => 'string', ], 'KMSMasterKeyId' => [ 'type' => 'string', ], 'Key' => [ 'type' => 'map', 'key' => [ 'shape' => 'AttributeName', ], 'value' => [ 'shape' => 'AttributeValue', ], ], 'KeyConditions' => [ 'type' => 'map', 'key' => [ 'shape' => 'AttributeName', ], 'value' => [ 'shape' => 'Condition', ], ], 'KeyExpression' => [ 'type' => 'string', ], 'KeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Key', ], 'max' => 100, 'min' => 1, ], 'KeySchema' => [ 'type' => 'list', 'member' => [ 'shape' => 'KeySchemaElement', ], 'max' => 2, 'min' => 1, ], 'KeySchemaAttributeName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'KeySchemaElement' => [ 'type' => 'structure', 'required' => [ 'AttributeName', 'KeyType', ], 'members' => [ 'AttributeName' => [ 'shape' => 'KeySchemaAttributeName', ], 'KeyType' => [ 'shape' => 'KeyType', ], ], ], 'KeyType' => [ 'type' => 'string', 'enum' => [ 'HASH', 'RANGE', ], ], 'KeysAndAttributes' => [ 'type' => 'structure', 'required' => [ 'Keys', ], 'members' => [ 'Keys' => [ 'shape' => 'KeyList', ], 'AttributesToGet' => [ 'shape' => 'AttributeNameList', ], 'ConsistentRead' => [ 'shape' => 'ConsistentRead', ], 'ProjectionExpression' => [ 'shape' => 'ProjectionExpression', ], 'ExpressionAttributeNames' => [ 'shape' => 'ExpressionAttributeNameMap', ], ], ], 'KinesisDataStreamDestination' => [ 'type' => 'structure', 'members' => [ 'StreamArn' => [ 'shape' => 'StreamArn', ], 'DestinationStatus' => [ 'shape' => 'DestinationStatus', ], 'DestinationStatusDescription' => [ 'shape' => 'String', ], 'ApproximateCreationDateTimePrecision' => [ 'shape' => 'ApproximateCreationDateTimePrecision', ], ], ], 'KinesisDataStreamDestinations' => [ 'type' => 'list', 'member' => [ 'shape' => 'KinesisDataStreamDestination', ], ], 'KinesisStreamingDestinationInput' => [ 'type' => 'structure', 'required' => [ 'TableName', 'StreamArn', ], 'members' => [ 'TableName' => [ 'shape' => 'TableArn', ], 'StreamArn' => [ 'shape' => 'StreamArn', ], 'EnableKinesisStreamingConfiguration' => [ 'shape' => 'EnableKinesisStreamingConfiguration', ], ], ], 'KinesisStreamingDestinationOutput' => [ 'type' => 'structure', 'members' => [ 'TableName' => [ 'shape' => 'TableName', ], 'StreamArn' => [ 'shape' => 'StreamArn', ], 'DestinationStatus' => [ 'shape' => 'DestinationStatus', ], 'EnableKinesisStreamingConfiguration' => [ 'shape' => 'EnableKinesisStreamingConfiguration', ], ], ], 'LastUpdateDateTime' => [ 'type' => 'timestamp', ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ListAttributeValue' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttributeValue', ], ], 'ListBackupsInput' => [ 'type' => 'structure', 'members' => [ 'TableName' => [ 'shape' => 'TableArn', ], 'Limit' => [ 'shape' => 'BackupsInputLimit', ], 'TimeRangeLowerBound' => [ 'shape' => 'TimeRangeLowerBound', ], 'TimeRangeUpperBound' => [ 'shape' => 'TimeRangeUpperBound', ], 'ExclusiveStartBackupArn' => [ 'shape' => 'BackupArn', ], 'BackupType' => [ 'shape' => 'BackupTypeFilter', ], ], ], 'ListBackupsOutput' => [ 'type' => 'structure', 'members' => [ 'BackupSummaries' => [ 'shape' => 'BackupSummaries', ], 'LastEvaluatedBackupArn' => [ 'shape' => 'BackupArn', ], ], ], 'ListContributorInsightsInput' => [ 'type' => 'structure', 'members' => [ 'TableName' => [ 'shape' => 'TableArn', ], 'NextToken' => [ 'shape' => 'NextTokenString', ], 'MaxResults' => [ 'shape' => 'ListContributorInsightsLimit', ], ], ], 'ListContributorInsightsLimit' => [ 'type' => 'integer', 'max' => 100, ], 'ListContributorInsightsOutput' => [ 'type' => 'structure', 'members' => [ 'ContributorInsightsSummaries' => [ 'shape' => 'ContributorInsightsSummaries', ], 'NextToken' => [ 'shape' => 'NextTokenString', ], ], ], 'ListExportsInput' => [ 'type' => 'structure', 'members' => [ 'TableArn' => [ 'shape' => 'TableArn', ], 'MaxResults' => [ 'shape' => 'ListExportsMaxLimit', ], 'NextToken' => [ 'shape' => 'ExportNextToken', ], ], ], 'ListExportsMaxLimit' => [ 'type' => 'integer', 'max' => 25, 'min' => 1, ], 'ListExportsOutput' => [ 'type' => 'structure', 'members' => [ 'ExportSummaries' => [ 'shape' => 'ExportSummaries', ], 'NextToken' => [ 'shape' => 'ExportNextToken', ], ], ], 'ListGlobalTablesInput' => [ 'type' => 'structure', 'members' => [ 'ExclusiveStartGlobalTableName' => [ 'shape' => 'TableName', ], 'Limit' => [ 'shape' => 'PositiveIntegerObject', ], 'RegionName' => [ 'shape' => 'RegionName', ], ], ], 'ListGlobalTablesOutput' => [ 'type' => 'structure', 'members' => [ 'GlobalTables' => [ 'shape' => 'GlobalTableList', ], 'LastEvaluatedGlobalTableName' => [ 'shape' => 'TableName', ], ], ], 'ListImportsInput' => [ 'type' => 'structure', 'members' => [ 'TableArn' => [ 'shape' => 'TableArn', ], 'PageSize' => [ 'shape' => 'ListImportsMaxLimit', ], 'NextToken' => [ 'shape' => 'ImportNextToken', ], ], ], 'ListImportsMaxLimit' => [ 'type' => 'integer', 'max' => 25, 'min' => 1, ], 'ListImportsOutput' => [ 'type' => 'structure', 'members' => [ 'ImportSummaryList' => [ 'shape' => 'ImportSummaryList', ], 'NextToken' => [ 'shape' => 'ImportNextToken', ], ], ], 'ListTablesInput' => [ 'type' => 'structure', 'members' => [ 'ExclusiveStartTableName' => [ 'shape' => 'TableName', ], 'Limit' => [ 'shape' => 'ListTablesInputLimit', ], ], ], 'ListTablesInputLimit' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'ListTablesOutput' => [ 'type' => 'structure', 'members' => [ 'TableNames' => [ 'shape' => 'TableNameList', ], 'LastEvaluatedTableName' => [ 'shape' => 'TableName', ], ], ], 'ListTagsOfResourceInput' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArnString', ], 'NextToken' => [ 'shape' => 'NextTokenString', ], ], ], 'ListTagsOfResourceOutput' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], 'NextToken' => [ 'shape' => 'NextTokenString', ], ], ], 'LocalSecondaryIndex' => [ 'type' => 'structure', 'required' => [ 'IndexName', 'KeySchema', 'Projection', ], 'members' => [ 'IndexName' => [ 'shape' => 'IndexName', ], 'KeySchema' => [ 'shape' => 'KeySchema', ], 'Projection' => [ 'shape' => 'Projection', ], ], ], 'LocalSecondaryIndexDescription' => [ 'type' => 'structure', 'members' => [ 'IndexName' => [ 'shape' => 'IndexName', ], 'KeySchema' => [ 'shape' => 'KeySchema', ], 'Projection' => [ 'shape' => 'Projection', ], 'IndexSizeBytes' => [ 'shape' => 'LongObject', ], 'ItemCount' => [ 'shape' => 'LongObject', ], 'IndexArn' => [ 'shape' => 'String', ], ], ], 'LocalSecondaryIndexDescriptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LocalSecondaryIndexDescription', ], ], 'LocalSecondaryIndexInfo' => [ 'type' => 'structure', 'members' => [ 'IndexName' => [ 'shape' => 'IndexName', ], 'KeySchema' => [ 'shape' => 'KeySchema', ], 'Projection' => [ 'shape' => 'Projection', ], ], ], 'LocalSecondaryIndexList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LocalSecondaryIndex', ], ], 'LocalSecondaryIndexes' => [ 'type' => 'list', 'member' => [ 'shape' => 'LocalSecondaryIndexInfo', ], ], 'Long' => [ 'type' => 'long', ], 'LongObject' => [ 'type' => 'long', ], 'MapAttributeValue' => [ 'type' => 'map', 'key' => [ 'shape' => 'AttributeName', ], 'value' => [ 'shape' => 'AttributeValue', ], ], 'NextTokenString' => [ 'type' => 'string', ], 'NonKeyAttributeName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'NonKeyAttributeNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonKeyAttributeName', ], 'max' => 20, 'min' => 1, ], 'NonNegativeLongObject' => [ 'type' => 'long', 'min' => 0, ], 'NullAttributeValue' => [ 'type' => 'boolean', ], 'NumberAttributeValue' => [ 'type' => 'string', ], 'NumberSetAttributeValue' => [ 'type' => 'list', 'member' => [ 'shape' => 'NumberAttributeValue', ], ], 'OnDemandThroughput' => [ 'type' => 'structure', 'members' => [ 'MaxReadRequestUnits' => [ 'shape' => 'LongObject', ], 'MaxWriteRequestUnits' => [ 'shape' => 'LongObject', ], ], ], 'OnDemandThroughputOverride' => [ 'type' => 'structure', 'members' => [ 'MaxReadRequestUnits' => [ 'shape' => 'LongObject', ], ], ], 'ParameterizedStatement' => [ 'type' => 'structure', 'required' => [ 'Statement', ], 'members' => [ 'Statement' => [ 'shape' => 'PartiQLStatement', ], 'Parameters' => [ 'shape' => 'PreparedStatementParameters', ], 'ReturnValuesOnConditionCheckFailure' => [ 'shape' => 'ReturnValuesOnConditionCheckFailure', ], ], ], 'ParameterizedStatements' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParameterizedStatement', ], 'max' => 100, 'min' => 1, ], 'PartiQLBatchRequest' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchStatementRequest', ], 'max' => 25, 'min' => 1, ], 'PartiQLBatchResponse' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchStatementResponse', ], ], 'PartiQLNextToken' => [ 'type' => 'string', 'max' => 32768, 'min' => 1, ], 'PartiQLStatement' => [ 'type' => 'string', 'max' => 8192, 'min' => 1, ], 'PointInTimeRecoveryDescription' => [ 'type' => 'structure', 'members' => [ 'PointInTimeRecoveryStatus' => [ 'shape' => 'PointInTimeRecoveryStatus', ], 'EarliestRestorableDateTime' => [ 'shape' => 'Date', ], 'LatestRestorableDateTime' => [ 'shape' => 'Date', ], ], ], 'PointInTimeRecoverySpecification' => [ 'type' => 'structure', 'required' => [ 'PointInTimeRecoveryEnabled', ], 'members' => [ 'PointInTimeRecoveryEnabled' => [ 'shape' => 'BooleanObject', ], ], ], 'PointInTimeRecoveryStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'PointInTimeRecoveryUnavailableException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'PolicyNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'PolicyRevisionId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'PositiveIntegerObject' => [ 'type' => 'integer', 'min' => 1, ], 'PositiveLongObject' => [ 'type' => 'long', 'min' => 1, ], 'PreparedStatementParameters' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttributeValue', ], 'min' => 1, ], 'ProcessedItemCount' => [ 'type' => 'long', 'min' => 0, ], 'Projection' => [ 'type' => 'structure', 'members' => [ 'ProjectionType' => [ 'shape' => 'ProjectionType', ], 'NonKeyAttributes' => [ 'shape' => 'NonKeyAttributeNameList', ], ], ], 'ProjectionExpression' => [ 'type' => 'string', ], 'ProjectionType' => [ 'type' => 'string', 'enum' => [ 'ALL', 'KEYS_ONLY', 'INCLUDE', ], ], 'ProvisionedThroughput' => [ 'type' => 'structure', 'required' => [ 'ReadCapacityUnits', 'WriteCapacityUnits', ], 'members' => [ 'ReadCapacityUnits' => [ 'shape' => 'PositiveLongObject', ], 'WriteCapacityUnits' => [ 'shape' => 'PositiveLongObject', ], ], ], 'ProvisionedThroughputDescription' => [ 'type' => 'structure', 'members' => [ 'LastIncreaseDateTime' => [ 'shape' => 'Date', ], 'LastDecreaseDateTime' => [ 'shape' => 'Date', ], 'NumberOfDecreasesToday' => [ 'shape' => 'PositiveLongObject', ], 'ReadCapacityUnits' => [ 'shape' => 'NonNegativeLongObject', ], 'WriteCapacityUnits' => [ 'shape' => 'NonNegativeLongObject', ], ], ], 'ProvisionedThroughputExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ProvisionedThroughputOverride' => [ 'type' => 'structure', 'members' => [ 'ReadCapacityUnits' => [ 'shape' => 'PositiveLongObject', ], ], ], 'Put' => [ 'type' => 'structure', 'required' => [ 'Item', 'TableName', ], 'members' => [ 'Item' => [ 'shape' => 'PutItemInputAttributeMap', ], 'TableName' => [ 'shape' => 'TableArn', ], 'ConditionExpression' => [ 'shape' => 'ConditionExpression', ], 'ExpressionAttributeNames' => [ 'shape' => 'ExpressionAttributeNameMap', ], 'ExpressionAttributeValues' => [ 'shape' => 'ExpressionAttributeValueMap', ], 'ReturnValuesOnConditionCheckFailure' => [ 'shape' => 'ReturnValuesOnConditionCheckFailure', ], ], ], 'PutItemInput' => [ 'type' => 'structure', 'required' => [ 'TableName', 'Item', ], 'members' => [ 'TableName' => [ 'shape' => 'TableArn', ], 'Item' => [ 'shape' => 'PutItemInputAttributeMap', ], 'Expected' => [ 'shape' => 'ExpectedAttributeMap', ], 'ReturnValues' => [ 'shape' => 'ReturnValue', ], 'ReturnConsumedCapacity' => [ 'shape' => 'ReturnConsumedCapacity', ], 'ReturnItemCollectionMetrics' => [ 'shape' => 'ReturnItemCollectionMetrics', ], 'ConditionalOperator' => [ 'shape' => 'ConditionalOperator', ], 'ConditionExpression' => [ 'shape' => 'ConditionExpression', ], 'ExpressionAttributeNames' => [ 'shape' => 'ExpressionAttributeNameMap', ], 'ExpressionAttributeValues' => [ 'shape' => 'ExpressionAttributeValueMap', ], 'ReturnValuesOnConditionCheckFailure' => [ 'shape' => 'ReturnValuesOnConditionCheckFailure', ], ], ], 'PutItemInputAttributeMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'AttributeName', ], 'value' => [ 'shape' => 'AttributeValue', ], ], 'PutItemOutput' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'AttributeMap', ], 'ConsumedCapacity' => [ 'shape' => 'ConsumedCapacity', ], 'ItemCollectionMetrics' => [ 'shape' => 'ItemCollectionMetrics', ], ], ], 'PutRequest' => [ 'type' => 'structure', 'required' => [ 'Item', ], 'members' => [ 'Item' => [ 'shape' => 'PutItemInputAttributeMap', ], ], ], 'PutResourcePolicyInput' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Policy', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArnString', ], 'Policy' => [ 'shape' => 'ResourcePolicy', ], 'ExpectedRevisionId' => [ 'shape' => 'PolicyRevisionId', ], 'ConfirmRemoveSelfResourceAccess' => [ 'shape' => 'ConfirmRemoveSelfResourceAccess', ], ], ], 'PutResourcePolicyOutput' => [ 'type' => 'structure', 'members' => [ 'RevisionId' => [ 'shape' => 'PolicyRevisionId', ], ], ], 'QueryInput' => [ 'type' => 'structure', 'required' => [ 'TableName', ], 'members' => [ 'TableName' => [ 'shape' => 'TableArn', ], 'IndexName' => [ 'shape' => 'IndexName', ], 'Select' => [ 'shape' => 'Select', ], 'AttributesToGet' => [ 'shape' => 'AttributeNameList', ], 'Limit' => [ 'shape' => 'PositiveIntegerObject', ], 'ConsistentRead' => [ 'shape' => 'ConsistentRead', ], 'KeyConditions' => [ 'shape' => 'KeyConditions', ], 'QueryFilter' => [ 'shape' => 'FilterConditionMap', ], 'ConditionalOperator' => [ 'shape' => 'ConditionalOperator', ], 'ScanIndexForward' => [ 'shape' => 'BooleanObject', ], 'ExclusiveStartKey' => [ 'shape' => 'Key', ], 'ReturnConsumedCapacity' => [ 'shape' => 'ReturnConsumedCapacity', ], 'ProjectionExpression' => [ 'shape' => 'ProjectionExpression', ], 'FilterExpression' => [ 'shape' => 'ConditionExpression', ], 'KeyConditionExpression' => [ 'shape' => 'KeyExpression', ], 'ExpressionAttributeNames' => [ 'shape' => 'ExpressionAttributeNameMap', ], 'ExpressionAttributeValues' => [ 'shape' => 'ExpressionAttributeValueMap', ], ], ], 'QueryOutput' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'ItemList', ], 'Count' => [ 'shape' => 'Integer', ], 'ScannedCount' => [ 'shape' => 'Integer', ], 'LastEvaluatedKey' => [ 'shape' => 'Key', ], 'ConsumedCapacity' => [ 'shape' => 'ConsumedCapacity', ], ], ], 'RegionName' => [ 'type' => 'string', ], 'Replica' => [ 'type' => 'structure', 'members' => [ 'RegionName' => [ 'shape' => 'RegionName', ], ], ], 'ReplicaAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ReplicaAutoScalingDescription' => [ 'type' => 'structure', 'members' => [ 'RegionName' => [ 'shape' => 'RegionName', ], 'GlobalSecondaryIndexes' => [ 'shape' => 'ReplicaGlobalSecondaryIndexAutoScalingDescriptionList', ], 'ReplicaProvisionedReadCapacityAutoScalingSettings' => [ 'shape' => 'AutoScalingSettingsDescription', ], 'ReplicaProvisionedWriteCapacityAutoScalingSettings' => [ 'shape' => 'AutoScalingSettingsDescription', ], 'ReplicaStatus' => [ 'shape' => 'ReplicaStatus', ], ], ], 'ReplicaAutoScalingDescriptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicaAutoScalingDescription', ], ], 'ReplicaAutoScalingUpdate' => [ 'type' => 'structure', 'required' => [ 'RegionName', ], 'members' => [ 'RegionName' => [ 'shape' => 'RegionName', ], 'ReplicaGlobalSecondaryIndexUpdates' => [ 'shape' => 'ReplicaGlobalSecondaryIndexAutoScalingUpdateList', ], 'ReplicaProvisionedReadCapacityAutoScalingUpdate' => [ 'shape' => 'AutoScalingSettingsUpdate', ], ], ], 'ReplicaAutoScalingUpdateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicaAutoScalingUpdate', ], 'min' => 1, ], 'ReplicaDescription' => [ 'type' => 'structure', 'members' => [ 'RegionName' => [ 'shape' => 'RegionName', ], 'ReplicaStatus' => [ 'shape' => 'ReplicaStatus', ], 'ReplicaStatusDescription' => [ 'shape' => 'ReplicaStatusDescription', ], 'ReplicaStatusPercentProgress' => [ 'shape' => 'ReplicaStatusPercentProgress', ], 'KMSMasterKeyId' => [ 'shape' => 'KMSMasterKeyId', ], 'ProvisionedThroughputOverride' => [ 'shape' => 'ProvisionedThroughputOverride', ], 'OnDemandThroughputOverride' => [ 'shape' => 'OnDemandThroughputOverride', ], 'GlobalSecondaryIndexes' => [ 'shape' => 'ReplicaGlobalSecondaryIndexDescriptionList', ], 'ReplicaInaccessibleDateTime' => [ 'shape' => 'Date', ], 'ReplicaTableClassSummary' => [ 'shape' => 'TableClassSummary', ], ], ], 'ReplicaDescriptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicaDescription', ], ], 'ReplicaGlobalSecondaryIndex' => [ 'type' => 'structure', 'required' => [ 'IndexName', ], 'members' => [ 'IndexName' => [ 'shape' => 'IndexName', ], 'ProvisionedThroughputOverride' => [ 'shape' => 'ProvisionedThroughputOverride', ], 'OnDemandThroughputOverride' => [ 'shape' => 'OnDemandThroughputOverride', ], ], ], 'ReplicaGlobalSecondaryIndexAutoScalingDescription' => [ 'type' => 'structure', 'members' => [ 'IndexName' => [ 'shape' => 'IndexName', ], 'IndexStatus' => [ 'shape' => 'IndexStatus', ], 'ProvisionedReadCapacityAutoScalingSettings' => [ 'shape' => 'AutoScalingSettingsDescription', ], 'ProvisionedWriteCapacityAutoScalingSettings' => [ 'shape' => 'AutoScalingSettingsDescription', ], ], ], 'ReplicaGlobalSecondaryIndexAutoScalingDescriptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicaGlobalSecondaryIndexAutoScalingDescription', ], ], 'ReplicaGlobalSecondaryIndexAutoScalingUpdate' => [ 'type' => 'structure', 'members' => [ 'IndexName' => [ 'shape' => 'IndexName', ], 'ProvisionedReadCapacityAutoScalingUpdate' => [ 'shape' => 'AutoScalingSettingsUpdate', ], ], ], 'ReplicaGlobalSecondaryIndexAutoScalingUpdateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicaGlobalSecondaryIndexAutoScalingUpdate', ], ], 'ReplicaGlobalSecondaryIndexDescription' => [ 'type' => 'structure', 'members' => [ 'IndexName' => [ 'shape' => 'IndexName', ], 'ProvisionedThroughputOverride' => [ 'shape' => 'ProvisionedThroughputOverride', ], 'OnDemandThroughputOverride' => [ 'shape' => 'OnDemandThroughputOverride', ], ], ], 'ReplicaGlobalSecondaryIndexDescriptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicaGlobalSecondaryIndexDescription', ], ], 'ReplicaGlobalSecondaryIndexList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicaGlobalSecondaryIndex', ], 'min' => 1, ], 'ReplicaGlobalSecondaryIndexSettingsDescription' => [ 'type' => 'structure', 'required' => [ 'IndexName', ], 'members' => [ 'IndexName' => [ 'shape' => 'IndexName', ], 'IndexStatus' => [ 'shape' => 'IndexStatus', ], 'ProvisionedReadCapacityUnits' => [ 'shape' => 'PositiveLongObject', ], 'ProvisionedReadCapacityAutoScalingSettings' => [ 'shape' => 'AutoScalingSettingsDescription', ], 'ProvisionedWriteCapacityUnits' => [ 'shape' => 'PositiveLongObject', ], 'ProvisionedWriteCapacityAutoScalingSettings' => [ 'shape' => 'AutoScalingSettingsDescription', ], ], ], 'ReplicaGlobalSecondaryIndexSettingsDescriptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicaGlobalSecondaryIndexSettingsDescription', ], ], 'ReplicaGlobalSecondaryIndexSettingsUpdate' => [ 'type' => 'structure', 'required' => [ 'IndexName', ], 'members' => [ 'IndexName' => [ 'shape' => 'IndexName', ], 'ProvisionedReadCapacityUnits' => [ 'shape' => 'PositiveLongObject', ], 'ProvisionedReadCapacityAutoScalingSettingsUpdate' => [ 'shape' => 'AutoScalingSettingsUpdate', ], ], ], 'ReplicaGlobalSecondaryIndexSettingsUpdateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicaGlobalSecondaryIndexSettingsUpdate', ], 'max' => 20, 'min' => 1, ], 'ReplicaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Replica', ], ], 'ReplicaNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ReplicaSettingsDescription' => [ 'type' => 'structure', 'required' => [ 'RegionName', ], 'members' => [ 'RegionName' => [ 'shape' => 'RegionName', ], 'ReplicaStatus' => [ 'shape' => 'ReplicaStatus', ], 'ReplicaBillingModeSummary' => [ 'shape' => 'BillingModeSummary', ], 'ReplicaProvisionedReadCapacityUnits' => [ 'shape' => 'NonNegativeLongObject', ], 'ReplicaProvisionedReadCapacityAutoScalingSettings' => [ 'shape' => 'AutoScalingSettingsDescription', ], 'ReplicaProvisionedWriteCapacityUnits' => [ 'shape' => 'NonNegativeLongObject', ], 'ReplicaProvisionedWriteCapacityAutoScalingSettings' => [ 'shape' => 'AutoScalingSettingsDescription', ], 'ReplicaGlobalSecondaryIndexSettings' => [ 'shape' => 'ReplicaGlobalSecondaryIndexSettingsDescriptionList', ], 'ReplicaTableClassSummary' => [ 'shape' => 'TableClassSummary', ], ], ], 'ReplicaSettingsDescriptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicaSettingsDescription', ], ], 'ReplicaSettingsUpdate' => [ 'type' => 'structure', 'required' => [ 'RegionName', ], 'members' => [ 'RegionName' => [ 'shape' => 'RegionName', ], 'ReplicaProvisionedReadCapacityUnits' => [ 'shape' => 'PositiveLongObject', ], 'ReplicaProvisionedReadCapacityAutoScalingSettingsUpdate' => [ 'shape' => 'AutoScalingSettingsUpdate', ], 'ReplicaGlobalSecondaryIndexSettingsUpdate' => [ 'shape' => 'ReplicaGlobalSecondaryIndexSettingsUpdateList', ], 'ReplicaTableClass' => [ 'shape' => 'TableClass', ], ], ], 'ReplicaSettingsUpdateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicaSettingsUpdate', ], 'max' => 50, 'min' => 1, ], 'ReplicaStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'CREATION_FAILED', 'UPDATING', 'DELETING', 'ACTIVE', 'REGION_DISABLED', 'INACCESSIBLE_ENCRYPTION_CREDENTIALS', ], ], 'ReplicaStatusDescription' => [ 'type' => 'string', ], 'ReplicaStatusPercentProgress' => [ 'type' => 'string', ], 'ReplicaUpdate' => [ 'type' => 'structure', 'members' => [ 'Create' => [ 'shape' => 'CreateReplicaAction', ], 'Delete' => [ 'shape' => 'DeleteReplicaAction', ], ], ], 'ReplicaUpdateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicaUpdate', ], ], 'ReplicationGroupUpdate' => [ 'type' => 'structure', 'members' => [ 'Create' => [ 'shape' => 'CreateReplicationGroupMemberAction', ], 'Update' => [ 'shape' => 'UpdateReplicationGroupMemberAction', ], 'Delete' => [ 'shape' => 'DeleteReplicationGroupMemberAction', ], ], ], 'ReplicationGroupUpdateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationGroupUpdate', ], 'min' => 1, ], 'RequestLimitExceeded' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ResourceArnString' => [ 'type' => 'string', 'max' => 1283, 'min' => 1, ], 'ResourceInUseException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ResourcePolicy' => [ 'type' => 'string', ], 'RestoreInProgress' => [ 'type' => 'boolean', ], 'RestoreSummary' => [ 'type' => 'structure', 'required' => [ 'RestoreDateTime', 'RestoreInProgress', ], 'members' => [ 'SourceBackupArn' => [ 'shape' => 'BackupArn', ], 'SourceTableArn' => [ 'shape' => 'TableArn', ], 'RestoreDateTime' => [ 'shape' => 'Date', ], 'RestoreInProgress' => [ 'shape' => 'RestoreInProgress', ], ], ], 'RestoreTableFromBackupInput' => [ 'type' => 'structure', 'required' => [ 'TargetTableName', 'BackupArn', ], 'members' => [ 'TargetTableName' => [ 'shape' => 'TableName', ], 'BackupArn' => [ 'shape' => 'BackupArn', ], 'BillingModeOverride' => [ 'shape' => 'BillingMode', ], 'GlobalSecondaryIndexOverride' => [ 'shape' => 'GlobalSecondaryIndexList', ], 'LocalSecondaryIndexOverride' => [ 'shape' => 'LocalSecondaryIndexList', ], 'ProvisionedThroughputOverride' => [ 'shape' => 'ProvisionedThroughput', ], 'OnDemandThroughputOverride' => [ 'shape' => 'OnDemandThroughput', ], 'SSESpecificationOverride' => [ 'shape' => 'SSESpecification', ], ], ], 'RestoreTableFromBackupOutput' => [ 'type' => 'structure', 'members' => [ 'TableDescription' => [ 'shape' => 'TableDescription', ], ], ], 'RestoreTableToPointInTimeInput' => [ 'type' => 'structure', 'required' => [ 'TargetTableName', ], 'members' => [ 'SourceTableArn' => [ 'shape' => 'TableArn', ], 'SourceTableName' => [ 'shape' => 'TableName', ], 'TargetTableName' => [ 'shape' => 'TableName', ], 'UseLatestRestorableTime' => [ 'shape' => 'BooleanObject', ], 'RestoreDateTime' => [ 'shape' => 'Date', ], 'BillingModeOverride' => [ 'shape' => 'BillingMode', ], 'GlobalSecondaryIndexOverride' => [ 'shape' => 'GlobalSecondaryIndexList', ], 'LocalSecondaryIndexOverride' => [ 'shape' => 'LocalSecondaryIndexList', ], 'ProvisionedThroughputOverride' => [ 'shape' => 'ProvisionedThroughput', ], 'OnDemandThroughputOverride' => [ 'shape' => 'OnDemandThroughput', ], 'SSESpecificationOverride' => [ 'shape' => 'SSESpecification', ], ], ], 'RestoreTableToPointInTimeOutput' => [ 'type' => 'structure', 'members' => [ 'TableDescription' => [ 'shape' => 'TableDescription', ], ], ], 'ReturnConsumedCapacity' => [ 'type' => 'string', 'enum' => [ 'INDEXES', 'TOTAL', 'NONE', ], ], 'ReturnItemCollectionMetrics' => [ 'type' => 'string', 'enum' => [ 'SIZE', 'NONE', ], ], 'ReturnValue' => [ 'type' => 'string', 'enum' => [ 'NONE', 'ALL_OLD', 'UPDATED_OLD', 'ALL_NEW', 'UPDATED_NEW', ], ], 'ReturnValuesOnConditionCheckFailure' => [ 'type' => 'string', 'enum' => [ 'ALL_OLD', 'NONE', ], ], 'S3Bucket' => [ 'type' => 'string', 'max' => 255, 'pattern' => '^[a-z0-9A-Z]+[\\.\\-\\w]*[a-z0-9A-Z]+$', ], 'S3BucketOwner' => [ 'type' => 'string', 'pattern' => '[0-9]{12}', ], 'S3BucketSource' => [ 'type' => 'structure', 'required' => [ 'S3Bucket', ], 'members' => [ 'S3BucketOwner' => [ 'shape' => 'S3BucketOwner', ], 'S3Bucket' => [ 'shape' => 'S3Bucket', ], 'S3KeyPrefix' => [ 'shape' => 'S3Prefix', ], ], ], 'S3Prefix' => [ 'type' => 'string', 'max' => 1024, ], 'S3SseAlgorithm' => [ 'type' => 'string', 'enum' => [ 'AES256', 'KMS', ], ], 'S3SseKmsKeyId' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'SSEDescription' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'SSEStatus', ], 'SSEType' => [ 'shape' => 'SSEType', ], 'KMSMasterKeyArn' => [ 'shape' => 'KMSMasterKeyArn', ], 'InaccessibleEncryptionDateTime' => [ 'shape' => 'Date', ], ], ], 'SSEEnabled' => [ 'type' => 'boolean', ], 'SSESpecification' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'SSEEnabled', ], 'SSEType' => [ 'shape' => 'SSEType', ], 'KMSMasterKeyId' => [ 'shape' => 'KMSMasterKeyId', ], ], ], 'SSEStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLING', 'ENABLED', 'DISABLING', 'DISABLED', 'UPDATING', ], ], 'SSEType' => [ 'type' => 'string', 'enum' => [ 'AES256', 'KMS', ], ], 'ScalarAttributeType' => [ 'type' => 'string', 'enum' => [ 'S', 'N', 'B', ], ], 'ScanInput' => [ 'type' => 'structure', 'required' => [ 'TableName', ], 'members' => [ 'TableName' => [ 'shape' => 'TableArn', ], 'IndexName' => [ 'shape' => 'IndexName', ], 'AttributesToGet' => [ 'shape' => 'AttributeNameList', ], 'Limit' => [ 'shape' => 'PositiveIntegerObject', ], 'Select' => [ 'shape' => 'Select', ], 'ScanFilter' => [ 'shape' => 'FilterConditionMap', ], 'ConditionalOperator' => [ 'shape' => 'ConditionalOperator', ], 'ExclusiveStartKey' => [ 'shape' => 'Key', ], 'ReturnConsumedCapacity' => [ 'shape' => 'ReturnConsumedCapacity', ], 'TotalSegments' => [ 'shape' => 'ScanTotalSegments', ], 'Segment' => [ 'shape' => 'ScanSegment', ], 'ProjectionExpression' => [ 'shape' => 'ProjectionExpression', ], 'FilterExpression' => [ 'shape' => 'ConditionExpression', ], 'ExpressionAttributeNames' => [ 'shape' => 'ExpressionAttributeNameMap', ], 'ExpressionAttributeValues' => [ 'shape' => 'ExpressionAttributeValueMap', ], 'ConsistentRead' => [ 'shape' => 'ConsistentRead', ], ], ], 'ScanOutput' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'ItemList', ], 'Count' => [ 'shape' => 'Integer', ], 'ScannedCount' => [ 'shape' => 'Integer', ], 'LastEvaluatedKey' => [ 'shape' => 'Key', ], 'ConsumedCapacity' => [ 'shape' => 'ConsumedCapacity', ], ], ], 'ScanSegment' => [ 'type' => 'integer', 'max' => 999999, 'min' => 0, ], 'ScanTotalSegments' => [ 'type' => 'integer', 'max' => 1000000, 'min' => 1, ], 'SecondaryIndexesCapacityMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'IndexName', ], 'value' => [ 'shape' => 'Capacity', ], ], 'Select' => [ 'type' => 'string', 'enum' => [ 'ALL_ATTRIBUTES', 'ALL_PROJECTED_ATTRIBUTES', 'SPECIFIC_ATTRIBUTES', 'COUNT', ], ], 'SourceTableDetails' => [ 'type' => 'structure', 'required' => [ 'TableName', 'TableId', 'KeySchema', 'TableCreationDateTime', 'ProvisionedThroughput', ], 'members' => [ 'TableName' => [ 'shape' => 'TableName', ], 'TableId' => [ 'shape' => 'TableId', ], 'TableArn' => [ 'shape' => 'TableArn', ], 'TableSizeBytes' => [ 'shape' => 'LongObject', ], 'KeySchema' => [ 'shape' => 'KeySchema', ], 'TableCreationDateTime' => [ 'shape' => 'TableCreationDateTime', ], 'ProvisionedThroughput' => [ 'shape' => 'ProvisionedThroughput', ], 'OnDemandThroughput' => [ 'shape' => 'OnDemandThroughput', ], 'ItemCount' => [ 'shape' => 'ItemCount', ], 'BillingMode' => [ 'shape' => 'BillingMode', ], ], ], 'SourceTableFeatureDetails' => [ 'type' => 'structure', 'members' => [ 'LocalSecondaryIndexes' => [ 'shape' => 'LocalSecondaryIndexes', ], 'GlobalSecondaryIndexes' => [ 'shape' => 'GlobalSecondaryIndexes', ], 'StreamDescription' => [ 'shape' => 'StreamSpecification', ], 'TimeToLiveDescription' => [ 'shape' => 'TimeToLiveDescription', ], 'SSEDescription' => [ 'shape' => 'SSEDescription', ], ], ], 'StreamArn' => [ 'type' => 'string', 'max' => 1024, 'min' => 37, ], 'StreamEnabled' => [ 'type' => 'boolean', ], 'StreamSpecification' => [ 'type' => 'structure', 'required' => [ 'StreamEnabled', ], 'members' => [ 'StreamEnabled' => [ 'shape' => 'StreamEnabled', ], 'StreamViewType' => [ 'shape' => 'StreamViewType', ], ], ], 'StreamViewType' => [ 'type' => 'string', 'enum' => [ 'NEW_IMAGE', 'OLD_IMAGE', 'NEW_AND_OLD_IMAGES', 'KEYS_ONLY', ], ], 'String' => [ 'type' => 'string', ], 'StringAttributeValue' => [ 'type' => 'string', ], 'StringSetAttributeValue' => [ 'type' => 'list', 'member' => [ 'shape' => 'StringAttributeValue', ], ], 'TableAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'TableArn' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'TableAutoScalingDescription' => [ 'type' => 'structure', 'members' => [ 'TableName' => [ 'shape' => 'TableName', ], 'TableStatus' => [ 'shape' => 'TableStatus', ], 'Replicas' => [ 'shape' => 'ReplicaAutoScalingDescriptionList', ], ], ], 'TableClass' => [ 'type' => 'string', 'enum' => [ 'STANDARD', 'STANDARD_INFREQUENT_ACCESS', ], ], 'TableClassSummary' => [ 'type' => 'structure', 'members' => [ 'TableClass' => [ 'shape' => 'TableClass', ], 'LastUpdateDateTime' => [ 'shape' => 'Date', ], ], ], 'TableCreationDateTime' => [ 'type' => 'timestamp', ], 'TableCreationParameters' => [ 'type' => 'structure', 'required' => [ 'TableName', 'AttributeDefinitions', 'KeySchema', ], 'members' => [ 'TableName' => [ 'shape' => 'TableName', ], 'AttributeDefinitions' => [ 'shape' => 'AttributeDefinitions', ], 'KeySchema' => [ 'shape' => 'KeySchema', ], 'BillingMode' => [ 'shape' => 'BillingMode', ], 'ProvisionedThroughput' => [ 'shape' => 'ProvisionedThroughput', ], 'OnDemandThroughput' => [ 'shape' => 'OnDemandThroughput', ], 'SSESpecification' => [ 'shape' => 'SSESpecification', ], 'GlobalSecondaryIndexes' => [ 'shape' => 'GlobalSecondaryIndexList', ], ], ], 'TableDescription' => [ 'type' => 'structure', 'members' => [ 'AttributeDefinitions' => [ 'shape' => 'AttributeDefinitions', ], 'TableName' => [ 'shape' => 'TableName', ], 'KeySchema' => [ 'shape' => 'KeySchema', ], 'TableStatus' => [ 'shape' => 'TableStatus', ], 'CreationDateTime' => [ 'shape' => 'Date', ], 'ProvisionedThroughput' => [ 'shape' => 'ProvisionedThroughputDescription', ], 'TableSizeBytes' => [ 'shape' => 'LongObject', ], 'ItemCount' => [ 'shape' => 'LongObject', ], 'TableArn' => [ 'shape' => 'String', ], 'TableId' => [ 'shape' => 'TableId', ], 'BillingModeSummary' => [ 'shape' => 'BillingModeSummary', ], 'LocalSecondaryIndexes' => [ 'shape' => 'LocalSecondaryIndexDescriptionList', ], 'GlobalSecondaryIndexes' => [ 'shape' => 'GlobalSecondaryIndexDescriptionList', ], 'StreamSpecification' => [ 'shape' => 'StreamSpecification', ], 'LatestStreamLabel' => [ 'shape' => 'String', ], 'LatestStreamArn' => [ 'shape' => 'StreamArn', ], 'GlobalTableVersion' => [ 'shape' => 'String', ], 'Replicas' => [ 'shape' => 'ReplicaDescriptionList', ], 'RestoreSummary' => [ 'shape' => 'RestoreSummary', ], 'SSEDescription' => [ 'shape' => 'SSEDescription', ], 'ArchivalSummary' => [ 'shape' => 'ArchivalSummary', ], 'TableClassSummary' => [ 'shape' => 'TableClassSummary', ], 'DeletionProtectionEnabled' => [ 'shape' => 'DeletionProtectionEnabled', ], 'OnDemandThroughput' => [ 'shape' => 'OnDemandThroughput', ], ], ], 'TableId' => [ 'type' => 'string', 'pattern' => '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', ], 'TableInUseException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'TableName' => [ 'type' => 'string', 'max' => 255, 'min' => 3, 'pattern' => '[a-zA-Z0-9_.-]+', ], 'TableNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TableName', ], ], 'TableNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'TableStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'UPDATING', 'DELETING', 'ACTIVE', 'INACCESSIBLE_ENCRYPTION_CREDENTIALS', 'ARCHIVING', 'ARCHIVED', ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKeyString', ], 'Value' => [ 'shape' => 'TagValueString', ], ], ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKeyString', ], ], 'TagKeyString' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'TagResourceInput' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArnString', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagValueString' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TimeRangeLowerBound' => [ 'type' => 'timestamp', ], 'TimeRangeUpperBound' => [ 'type' => 'timestamp', ], 'TimeToLiveAttributeName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'TimeToLiveDescription' => [ 'type' => 'structure', 'members' => [ 'TimeToLiveStatus' => [ 'shape' => 'TimeToLiveStatus', ], 'AttributeName' => [ 'shape' => 'TimeToLiveAttributeName', ], ], ], 'TimeToLiveEnabled' => [ 'type' => 'boolean', ], 'TimeToLiveSpecification' => [ 'type' => 'structure', 'required' => [ 'Enabled', 'AttributeName', ], 'members' => [ 'Enabled' => [ 'shape' => 'TimeToLiveEnabled', ], 'AttributeName' => [ 'shape' => 'TimeToLiveAttributeName', ], ], ], 'TimeToLiveStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLING', 'DISABLING', 'ENABLED', 'DISABLED', ], ], 'TransactGetItem' => [ 'type' => 'structure', 'required' => [ 'Get', ], 'members' => [ 'Get' => [ 'shape' => 'Get', ], ], ], 'TransactGetItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TransactGetItem', ], 'max' => 100, 'min' => 1, ], 'TransactGetItemsInput' => [ 'type' => 'structure', 'required' => [ 'TransactItems', ], 'members' => [ 'TransactItems' => [ 'shape' => 'TransactGetItemList', ], 'ReturnConsumedCapacity' => [ 'shape' => 'ReturnConsumedCapacity', ], ], ], 'TransactGetItemsOutput' => [ 'type' => 'structure', 'members' => [ 'ConsumedCapacity' => [ 'shape' => 'ConsumedCapacityMultiple', ], 'Responses' => [ 'shape' => 'ItemResponseList', ], ], ], 'TransactWriteItem' => [ 'type' => 'structure', 'members' => [ 'ConditionCheck' => [ 'shape' => 'ConditionCheck', ], 'Put' => [ 'shape' => 'Put', ], 'Delete' => [ 'shape' => 'Delete', ], 'Update' => [ 'shape' => 'Update', ], ], ], 'TransactWriteItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TransactWriteItem', ], 'max' => 100, 'min' => 1, ], 'TransactWriteItemsInput' => [ 'type' => 'structure', 'required' => [ 'TransactItems', ], 'members' => [ 'TransactItems' => [ 'shape' => 'TransactWriteItemList', ], 'ReturnConsumedCapacity' => [ 'shape' => 'ReturnConsumedCapacity', ], 'ReturnItemCollectionMetrics' => [ 'shape' => 'ReturnItemCollectionMetrics', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'TransactWriteItemsOutput' => [ 'type' => 'structure', 'members' => [ 'ConsumedCapacity' => [ 'shape' => 'ConsumedCapacityMultiple', ], 'ItemCollectionMetrics' => [ 'shape' => 'ItemCollectionMetricsPerTable', ], ], ], 'TransactionCanceledException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], 'CancellationReasons' => [ 'shape' => 'CancellationReasonList', ], ], 'exception' => true, ], 'TransactionConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'TransactionInProgressException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'UntagResourceInput' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArnString', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'Update' => [ 'type' => 'structure', 'required' => [ 'Key', 'UpdateExpression', 'TableName', ], 'members' => [ 'Key' => [ 'shape' => 'Key', ], 'UpdateExpression' => [ 'shape' => 'UpdateExpression', ], 'TableName' => [ 'shape' => 'TableArn', ], 'ConditionExpression' => [ 'shape' => 'ConditionExpression', ], 'ExpressionAttributeNames' => [ 'shape' => 'ExpressionAttributeNameMap', ], 'ExpressionAttributeValues' => [ 'shape' => 'ExpressionAttributeValueMap', ], 'ReturnValuesOnConditionCheckFailure' => [ 'shape' => 'ReturnValuesOnConditionCheckFailure', ], ], ], 'UpdateContinuousBackupsInput' => [ 'type' => 'structure', 'required' => [ 'TableName', 'PointInTimeRecoverySpecification', ], 'members' => [ 'TableName' => [ 'shape' => 'TableArn', ], 'PointInTimeRecoverySpecification' => [ 'shape' => 'PointInTimeRecoverySpecification', ], ], ], 'UpdateContinuousBackupsOutput' => [ 'type' => 'structure', 'members' => [ 'ContinuousBackupsDescription' => [ 'shape' => 'ContinuousBackupsDescription', ], ], ], 'UpdateContributorInsightsInput' => [ 'type' => 'structure', 'required' => [ 'TableName', 'ContributorInsightsAction', ], 'members' => [ 'TableName' => [ 'shape' => 'TableArn', ], 'IndexName' => [ 'shape' => 'IndexName', ], 'ContributorInsightsAction' => [ 'shape' => 'ContributorInsightsAction', ], ], ], 'UpdateContributorInsightsOutput' => [ 'type' => 'structure', 'members' => [ 'TableName' => [ 'shape' => 'TableName', ], 'IndexName' => [ 'shape' => 'IndexName', ], 'ContributorInsightsStatus' => [ 'shape' => 'ContributorInsightsStatus', ], ], ], 'UpdateExpression' => [ 'type' => 'string', ], 'UpdateGlobalSecondaryIndexAction' => [ 'type' => 'structure', 'required' => [ 'IndexName', ], 'members' => [ 'IndexName' => [ 'shape' => 'IndexName', ], 'ProvisionedThroughput' => [ 'shape' => 'ProvisionedThroughput', ], 'OnDemandThroughput' => [ 'shape' => 'OnDemandThroughput', ], ], ], 'UpdateGlobalTableInput' => [ 'type' => 'structure', 'required' => [ 'GlobalTableName', 'ReplicaUpdates', ], 'members' => [ 'GlobalTableName' => [ 'shape' => 'TableName', ], 'ReplicaUpdates' => [ 'shape' => 'ReplicaUpdateList', ], ], ], 'UpdateGlobalTableOutput' => [ 'type' => 'structure', 'members' => [ 'GlobalTableDescription' => [ 'shape' => 'GlobalTableDescription', ], ], ], 'UpdateGlobalTableSettingsInput' => [ 'type' => 'structure', 'required' => [ 'GlobalTableName', ], 'members' => [ 'GlobalTableName' => [ 'shape' => 'TableName', ], 'GlobalTableBillingMode' => [ 'shape' => 'BillingMode', ], 'GlobalTableProvisionedWriteCapacityUnits' => [ 'shape' => 'PositiveLongObject', ], 'GlobalTableProvisionedWriteCapacityAutoScalingSettingsUpdate' => [ 'shape' => 'AutoScalingSettingsUpdate', ], 'GlobalTableGlobalSecondaryIndexSettingsUpdate' => [ 'shape' => 'GlobalTableGlobalSecondaryIndexSettingsUpdateList', ], 'ReplicaSettingsUpdate' => [ 'shape' => 'ReplicaSettingsUpdateList', ], ], ], 'UpdateGlobalTableSettingsOutput' => [ 'type' => 'structure', 'members' => [ 'GlobalTableName' => [ 'shape' => 'TableName', ], 'ReplicaSettings' => [ 'shape' => 'ReplicaSettingsDescriptionList', ], ], ], 'UpdateItemInput' => [ 'type' => 'structure', 'required' => [ 'TableName', 'Key', ], 'members' => [ 'TableName' => [ 'shape' => 'TableArn', ], 'Key' => [ 'shape' => 'Key', ], 'AttributeUpdates' => [ 'shape' => 'AttributeUpdates', ], 'Expected' => [ 'shape' => 'ExpectedAttributeMap', ], 'ConditionalOperator' => [ 'shape' => 'ConditionalOperator', ], 'ReturnValues' => [ 'shape' => 'ReturnValue', ], 'ReturnConsumedCapacity' => [ 'shape' => 'ReturnConsumedCapacity', ], 'ReturnItemCollectionMetrics' => [ 'shape' => 'ReturnItemCollectionMetrics', ], 'UpdateExpression' => [ 'shape' => 'UpdateExpression', ], 'ConditionExpression' => [ 'shape' => 'ConditionExpression', ], 'ExpressionAttributeNames' => [ 'shape' => 'ExpressionAttributeNameMap', ], 'ExpressionAttributeValues' => [ 'shape' => 'ExpressionAttributeValueMap', ], 'ReturnValuesOnConditionCheckFailure' => [ 'shape' => 'ReturnValuesOnConditionCheckFailure', ], ], ], 'UpdateItemOutput' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'AttributeMap', ], 'ConsumedCapacity' => [ 'shape' => 'ConsumedCapacity', ], 'ItemCollectionMetrics' => [ 'shape' => 'ItemCollectionMetrics', ], ], ], 'UpdateKinesisStreamingConfiguration' => [ 'type' => 'structure', 'members' => [ 'ApproximateCreationDateTimePrecision' => [ 'shape' => 'ApproximateCreationDateTimePrecision', ], ], ], 'UpdateKinesisStreamingDestinationInput' => [ 'type' => 'structure', 'required' => [ 'TableName', 'StreamArn', ], 'members' => [ 'TableName' => [ 'shape' => 'TableArn', ], 'StreamArn' => [ 'shape' => 'StreamArn', ], 'UpdateKinesisStreamingConfiguration' => [ 'shape' => 'UpdateKinesisStreamingConfiguration', ], ], ], 'UpdateKinesisStreamingDestinationOutput' => [ 'type' => 'structure', 'members' => [ 'TableName' => [ 'shape' => 'TableName', ], 'StreamArn' => [ 'shape' => 'StreamArn', ], 'DestinationStatus' => [ 'shape' => 'DestinationStatus', ], 'UpdateKinesisStreamingConfiguration' => [ 'shape' => 'UpdateKinesisStreamingConfiguration', ], ], ], 'UpdateReplicationGroupMemberAction' => [ 'type' => 'structure', 'required' => [ 'RegionName', ], 'members' => [ 'RegionName' => [ 'shape' => 'RegionName', ], 'KMSMasterKeyId' => [ 'shape' => 'KMSMasterKeyId', ], 'ProvisionedThroughputOverride' => [ 'shape' => 'ProvisionedThroughputOverride', ], 'OnDemandThroughputOverride' => [ 'shape' => 'OnDemandThroughputOverride', ], 'GlobalSecondaryIndexes' => [ 'shape' => 'ReplicaGlobalSecondaryIndexList', ], 'TableClassOverride' => [ 'shape' => 'TableClass', ], ], ], 'UpdateTableInput' => [ 'type' => 'structure', 'required' => [ 'TableName', ], 'members' => [ 'AttributeDefinitions' => [ 'shape' => 'AttributeDefinitions', ], 'TableName' => [ 'shape' => 'TableArn', ], 'BillingMode' => [ 'shape' => 'BillingMode', ], 'ProvisionedThroughput' => [ 'shape' => 'ProvisionedThroughput', ], 'GlobalSecondaryIndexUpdates' => [ 'shape' => 'GlobalSecondaryIndexUpdateList', ], 'StreamSpecification' => [ 'shape' => 'StreamSpecification', ], 'SSESpecification' => [ 'shape' => 'SSESpecification', ], 'ReplicaUpdates' => [ 'shape' => 'ReplicationGroupUpdateList', ], 'TableClass' => [ 'shape' => 'TableClass', ], 'DeletionProtectionEnabled' => [ 'shape' => 'DeletionProtectionEnabled', ], 'OnDemandThroughput' => [ 'shape' => 'OnDemandThroughput', ], ], ], 'UpdateTableOutput' => [ 'type' => 'structure', 'members' => [ 'TableDescription' => [ 'shape' => 'TableDescription', ], ], ], 'UpdateTableReplicaAutoScalingInput' => [ 'type' => 'structure', 'required' => [ 'TableName', ], 'members' => [ 'GlobalSecondaryIndexUpdates' => [ 'shape' => 'GlobalSecondaryIndexAutoScalingUpdateList', ], 'TableName' => [ 'shape' => 'TableArn', ], 'ProvisionedWriteCapacityAutoScalingUpdate' => [ 'shape' => 'AutoScalingSettingsUpdate', ], 'ReplicaUpdates' => [ 'shape' => 'ReplicaAutoScalingUpdateList', ], ], ], 'UpdateTableReplicaAutoScalingOutput' => [ 'type' => 'structure', 'members' => [ 'TableAutoScalingDescription' => [ 'shape' => 'TableAutoScalingDescription', ], ], ], 'UpdateTimeToLiveInput' => [ 'type' => 'structure', 'required' => [ 'TableName', 'TimeToLiveSpecification', ], 'members' => [ 'TableName' => [ 'shape' => 'TableArn', ], 'TimeToLiveSpecification' => [ 'shape' => 'TimeToLiveSpecification', ], ], ], 'UpdateTimeToLiveOutput' => [ 'type' => 'structure', 'members' => [ 'TimeToLiveSpecification' => [ 'shape' => 'TimeToLiveSpecification', ], ], ], 'WriteRequest' => [ 'type' => 'structure', 'members' => [ 'PutRequest' => [ 'shape' => 'PutRequest', ], 'DeleteRequest' => [ 'shape' => 'DeleteRequest', ], ], ], 'WriteRequests' => [ 'type' => 'list', 'member' => [ 'shape' => 'WriteRequest', ], 'max' => 25, 'min' => 1, ], ],];
