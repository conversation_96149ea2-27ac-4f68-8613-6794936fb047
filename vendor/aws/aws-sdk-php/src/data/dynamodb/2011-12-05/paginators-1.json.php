<?php
// This file was auto-generated from sdk-root/src/data/dynamodb/2011-12-05/paginators-1.json
return [ 'pagination' => [ 'BatchGetItem' => [ 'input_token' => 'RequestItems', 'output_token' => 'UnprocessedKeys', ], 'ListTables' => [ 'input_token' => 'ExclusiveStartTableName', 'limit_key' => 'Limit', 'output_token' => 'LastEvaluatedTableName', 'result_key' => 'TableNames', ], 'Query' => [ 'input_token' => 'ExclusiveStartKey', 'limit_key' => 'Limit', 'output_token' => 'LastEvaluatedKey', 'result_key' => 'Items', ], 'Scan' => [ 'input_token' => 'ExclusiveStartKey', 'limit_key' => 'Limit', 'output_token' => 'LastEvaluatedKey', 'result_key' => 'Items', ], ],];
