<?php
// This file was auto-generated from sdk-root/src/data/sesv2/2019-09-27/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2019-09-27', 'endpointPrefix' => 'email', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceAbbreviation' => 'Amazon SES V2', 'serviceFullName' => 'Amazon Simple Email Service', 'serviceId' => 'SESv2', 'signatureVersion' => 'v4', 'signingName' => 'ses', 'uid' => 'sesv2-2019-09-27', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'BatchGetMetricData' => [ 'name' => 'BatchGetMetricData', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/email/metrics/batch', ], 'input' => [ 'shape' => 'BatchGetMetricDataRequest', ], 'output' => [ 'shape' => 'BatchGetMetricDataResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotFoundException', ], ], ], 'CancelExportJob' => [ 'name' => 'CancelExportJob', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/email/export-jobs/{JobId}/cancel', ], 'input' => [ 'shape' => 'CancelExportJobRequest', ], 'output' => [ 'shape' => 'CancelExportJobResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'CreateConfigurationSet' => [ 'name' => 'CreateConfigurationSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/email/configuration-sets', ], 'input' => [ 'shape' => 'CreateConfigurationSetRequest', ], 'output' => [ 'shape' => 'CreateConfigurationSetResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'CreateConfigurationSetEventDestination' => [ 'name' => 'CreateConfigurationSetEventDestination', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/email/configuration-sets/{ConfigurationSetName}/event-destinations', ], 'input' => [ 'shape' => 'CreateConfigurationSetEventDestinationRequest', ], 'output' => [ 'shape' => 'CreateConfigurationSetEventDestinationResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'CreateContact' => [ 'name' => 'CreateContact', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/email/contact-lists/{ContactListName}/contacts', ], 'input' => [ 'shape' => 'CreateContactRequest', ], 'output' => [ 'shape' => 'CreateContactResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AlreadyExistsException', ], ], ], 'CreateContactList' => [ 'name' => 'CreateContactList', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/email/contact-lists', ], 'input' => [ 'shape' => 'CreateContactListRequest', ], 'output' => [ 'shape' => 'CreateContactListResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateCustomVerificationEmailTemplate' => [ 'name' => 'CreateCustomVerificationEmailTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/email/custom-verification-email-templates', ], 'input' => [ 'shape' => 'CreateCustomVerificationEmailTemplateRequest', ], 'output' => [ 'shape' => 'CreateCustomVerificationEmailTemplateResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateDedicatedIpPool' => [ 'name' => 'CreateDedicatedIpPool', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/email/dedicated-ip-pools', ], 'input' => [ 'shape' => 'CreateDedicatedIpPoolRequest', ], 'output' => [ 'shape' => 'CreateDedicatedIpPoolResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'CreateDeliverabilityTestReport' => [ 'name' => 'CreateDeliverabilityTestReport', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/email/deliverability-dashboard/test', ], 'input' => [ 'shape' => 'CreateDeliverabilityTestReportRequest', ], 'output' => [ 'shape' => 'CreateDeliverabilityTestReportResponse', ], 'errors' => [ [ 'shape' => 'AccountSuspendedException', ], [ 'shape' => 'SendingPausedException', ], [ 'shape' => 'MessageRejected', ], [ 'shape' => 'MailFromDomainNotVerifiedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'CreateEmailIdentity' => [ 'name' => 'CreateEmailIdentity', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/email/identities', ], 'input' => [ 'shape' => 'CreateEmailIdentityRequest', ], 'output' => [ 'shape' => 'CreateEmailIdentityResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], ], ], 'CreateEmailIdentityPolicy' => [ 'name' => 'CreateEmailIdentityPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/email/identities/{EmailIdentity}/policies/{PolicyName}', ], 'input' => [ 'shape' => 'CreateEmailIdentityPolicyRequest', ], 'output' => [ 'shape' => 'CreateEmailIdentityPolicyResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'BadRequestException', ], ], ], 'CreateEmailTemplate' => [ 'name' => 'CreateEmailTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/email/templates', ], 'input' => [ 'shape' => 'CreateEmailTemplateRequest', ], 'output' => [ 'shape' => 'CreateEmailTemplateResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateExportJob' => [ 'name' => 'CreateExportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/email/export-jobs', ], 'input' => [ 'shape' => 'CreateExportJobRequest', ], 'output' => [ 'shape' => 'CreateExportJobResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateImportJob' => [ 'name' => 'CreateImportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/email/import-jobs', ], 'input' => [ 'shape' => 'CreateImportJobRequest', ], 'output' => [ 'shape' => 'CreateImportJobResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DeleteConfigurationSet' => [ 'name' => 'DeleteConfigurationSet', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v2/email/configuration-sets/{ConfigurationSetName}', ], 'input' => [ 'shape' => 'DeleteConfigurationSetRequest', ], 'output' => [ 'shape' => 'DeleteConfigurationSetResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'DeleteConfigurationSetEventDestination' => [ 'name' => 'DeleteConfigurationSetEventDestination', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v2/email/configuration-sets/{ConfigurationSetName}/event-destinations/{EventDestinationName}', ], 'input' => [ 'shape' => 'DeleteConfigurationSetEventDestinationRequest', ], 'output' => [ 'shape' => 'DeleteConfigurationSetEventDestinationResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'DeleteContact' => [ 'name' => 'DeleteContact', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v2/email/contact-lists/{ContactListName}/contacts/{EmailAddress}', ], 'input' => [ 'shape' => 'DeleteContactRequest', ], 'output' => [ 'shape' => 'DeleteContactResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotFoundException', ], ], ], 'DeleteContactList' => [ 'name' => 'DeleteContactList', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v2/email/contact-lists/{ContactListName}', ], 'input' => [ 'shape' => 'DeleteContactListRequest', ], 'output' => [ 'shape' => 'DeleteContactListResponse', ], 'errors' => [ [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'DeleteCustomVerificationEmailTemplate' => [ 'name' => 'DeleteCustomVerificationEmailTemplate', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v2/email/custom-verification-email-templates/{TemplateName}', ], 'input' => [ 'shape' => 'DeleteCustomVerificationEmailTemplateRequest', ], 'output' => [ 'shape' => 'DeleteCustomVerificationEmailTemplateResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'DeleteDedicatedIpPool' => [ 'name' => 'DeleteDedicatedIpPool', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v2/email/dedicated-ip-pools/{PoolName}', ], 'input' => [ 'shape' => 'DeleteDedicatedIpPoolRequest', ], 'output' => [ 'shape' => 'DeleteDedicatedIpPoolResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'DeleteEmailIdentity' => [ 'name' => 'DeleteEmailIdentity', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v2/email/identities/{EmailIdentity}', ], 'input' => [ 'shape' => 'DeleteEmailIdentityRequest', ], 'output' => [ 'shape' => 'DeleteEmailIdentityResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'DeleteEmailIdentityPolicy' => [ 'name' => 'DeleteEmailIdentityPolicy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v2/email/identities/{EmailIdentity}/policies/{PolicyName}', ], 'input' => [ 'shape' => 'DeleteEmailIdentityPolicyRequest', ], 'output' => [ 'shape' => 'DeleteEmailIdentityPolicyResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'DeleteEmailTemplate' => [ 'name' => 'DeleteEmailTemplate', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v2/email/templates/{TemplateName}', ], 'input' => [ 'shape' => 'DeleteEmailTemplateRequest', ], 'output' => [ 'shape' => 'DeleteEmailTemplateResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'DeleteSuppressedDestination' => [ 'name' => 'DeleteSuppressedDestination', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v2/email/suppression/addresses/{EmailAddress}', ], 'input' => [ 'shape' => 'DeleteSuppressedDestinationRequest', ], 'output' => [ 'shape' => 'DeleteSuppressedDestinationResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetAccount' => [ 'name' => 'GetAccount', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/email/account', ], 'input' => [ 'shape' => 'GetAccountRequest', ], 'output' => [ 'shape' => 'GetAccountResponse', ], 'errors' => [ [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetBlacklistReports' => [ 'name' => 'GetBlacklistReports', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/email/deliverability-dashboard/blacklist-report', ], 'input' => [ 'shape' => 'GetBlacklistReportsRequest', ], 'output' => [ 'shape' => 'GetBlacklistReportsResponse', ], 'errors' => [ [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetConfigurationSet' => [ 'name' => 'GetConfigurationSet', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/email/configuration-sets/{ConfigurationSetName}', ], 'input' => [ 'shape' => 'GetConfigurationSetRequest', ], 'output' => [ 'shape' => 'GetConfigurationSetResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetConfigurationSetEventDestinations' => [ 'name' => 'GetConfigurationSetEventDestinations', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/email/configuration-sets/{ConfigurationSetName}/event-destinations', ], 'input' => [ 'shape' => 'GetConfigurationSetEventDestinationsRequest', ], 'output' => [ 'shape' => 'GetConfigurationSetEventDestinationsResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetContact' => [ 'name' => 'GetContact', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/email/contact-lists/{ContactListName}/contacts/{EmailAddress}', ], 'input' => [ 'shape' => 'GetContactRequest', ], 'output' => [ 'shape' => 'GetContactResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotFoundException', ], ], ], 'GetContactList' => [ 'name' => 'GetContactList', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/email/contact-lists/{ContactListName}', ], 'input' => [ 'shape' => 'GetContactListRequest', ], 'output' => [ 'shape' => 'GetContactListResponse', ], 'errors' => [ [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetCustomVerificationEmailTemplate' => [ 'name' => 'GetCustomVerificationEmailTemplate', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/email/custom-verification-email-templates/{TemplateName}', ], 'input' => [ 'shape' => 'GetCustomVerificationEmailTemplateRequest', ], 'output' => [ 'shape' => 'GetCustomVerificationEmailTemplateResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetDedicatedIp' => [ 'name' => 'GetDedicatedIp', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/email/dedicated-ips/{IP}', ], 'input' => [ 'shape' => 'GetDedicatedIpRequest', ], 'output' => [ 'shape' => 'GetDedicatedIpResponse', ], 'errors' => [ [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetDedicatedIpPool' => [ 'name' => 'GetDedicatedIpPool', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/email/dedicated-ip-pools/{PoolName}', ], 'input' => [ 'shape' => 'GetDedicatedIpPoolRequest', ], 'output' => [ 'shape' => 'GetDedicatedIpPoolResponse', ], 'errors' => [ [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetDedicatedIps' => [ 'name' => 'GetDedicatedIps', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/email/dedicated-ips', ], 'input' => [ 'shape' => 'GetDedicatedIpsRequest', ], 'output' => [ 'shape' => 'GetDedicatedIpsResponse', ], 'errors' => [ [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetDeliverabilityDashboardOptions' => [ 'name' => 'GetDeliverabilityDashboardOptions', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/email/deliverability-dashboard', ], 'input' => [ 'shape' => 'GetDeliverabilityDashboardOptionsRequest', ], 'output' => [ 'shape' => 'GetDeliverabilityDashboardOptionsResponse', ], 'errors' => [ [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetDeliverabilityTestReport' => [ 'name' => 'GetDeliverabilityTestReport', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/email/deliverability-dashboard/test-reports/{ReportId}', ], 'input' => [ 'shape' => 'GetDeliverabilityTestReportRequest', ], 'output' => [ 'shape' => 'GetDeliverabilityTestReportResponse', ], 'errors' => [ [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetDomainDeliverabilityCampaign' => [ 'name' => 'GetDomainDeliverabilityCampaign', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/email/deliverability-dashboard/campaigns/{CampaignId}', ], 'input' => [ 'shape' => 'GetDomainDeliverabilityCampaignRequest', ], 'output' => [ 'shape' => 'GetDomainDeliverabilityCampaignResponse', ], 'errors' => [ [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], ], ], 'GetDomainStatisticsReport' => [ 'name' => 'GetDomainStatisticsReport', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/email/deliverability-dashboard/statistics-report/{Domain}', ], 'input' => [ 'shape' => 'GetDomainStatisticsReportRequest', ], 'output' => [ 'shape' => 'GetDomainStatisticsReportResponse', ], 'errors' => [ [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetEmailIdentity' => [ 'name' => 'GetEmailIdentity', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/email/identities/{EmailIdentity}', ], 'input' => [ 'shape' => 'GetEmailIdentityRequest', ], 'output' => [ 'shape' => 'GetEmailIdentityResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetEmailIdentityPolicies' => [ 'name' => 'GetEmailIdentityPolicies', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/email/identities/{EmailIdentity}/policies', ], 'input' => [ 'shape' => 'GetEmailIdentityPoliciesRequest', ], 'output' => [ 'shape' => 'GetEmailIdentityPoliciesResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetEmailTemplate' => [ 'name' => 'GetEmailTemplate', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/email/templates/{TemplateName}', ], 'input' => [ 'shape' => 'GetEmailTemplateRequest', ], 'output' => [ 'shape' => 'GetEmailTemplateResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetExportJob' => [ 'name' => 'GetExportJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/email/export-jobs/{JobId}', ], 'input' => [ 'shape' => 'GetExportJobRequest', ], 'output' => [ 'shape' => 'GetExportJobResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetImportJob' => [ 'name' => 'GetImportJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/email/import-jobs/{JobId}', ], 'input' => [ 'shape' => 'GetImportJobRequest', ], 'output' => [ 'shape' => 'GetImportJobResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetMessageInsights' => [ 'name' => 'GetMessageInsights', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/email/insights/{MessageId}/', ], 'input' => [ 'shape' => 'GetMessageInsightsRequest', ], 'output' => [ 'shape' => 'GetMessageInsightsResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetSuppressedDestination' => [ 'name' => 'GetSuppressedDestination', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/email/suppression/addresses/{EmailAddress}', ], 'input' => [ 'shape' => 'GetSuppressedDestinationRequest', ], 'output' => [ 'shape' => 'GetSuppressedDestinationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotFoundException', ], ], ], 'ListConfigurationSets' => [ 'name' => 'ListConfigurationSets', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/email/configuration-sets', ], 'input' => [ 'shape' => 'ListConfigurationSetsRequest', ], 'output' => [ 'shape' => 'ListConfigurationSetsResponse', ], 'errors' => [ [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'ListContactLists' => [ 'name' => 'ListContactLists', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/email/contact-lists', ], 'input' => [ 'shape' => 'ListContactListsRequest', ], 'output' => [ 'shape' => 'ListContactListsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListContacts' => [ 'name' => 'ListContacts', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/email/contact-lists/{ContactListName}/contacts/list', ], 'input' => [ 'shape' => 'ListContactsRequest', ], 'output' => [ 'shape' => 'ListContactsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotFoundException', ], ], ], 'ListCustomVerificationEmailTemplates' => [ 'name' => 'ListCustomVerificationEmailTemplates', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/email/custom-verification-email-templates', ], 'input' => [ 'shape' => 'ListCustomVerificationEmailTemplatesRequest', ], 'output' => [ 'shape' => 'ListCustomVerificationEmailTemplatesResponse', ], 'errors' => [ [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'ListDedicatedIpPools' => [ 'name' => 'ListDedicatedIpPools', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/email/dedicated-ip-pools', ], 'input' => [ 'shape' => 'ListDedicatedIpPoolsRequest', ], 'output' => [ 'shape' => 'ListDedicatedIpPoolsResponse', ], 'errors' => [ [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'ListDeliverabilityTestReports' => [ 'name' => 'ListDeliverabilityTestReports', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/email/deliverability-dashboard/test-reports', ], 'input' => [ 'shape' => 'ListDeliverabilityTestReportsRequest', ], 'output' => [ 'shape' => 'ListDeliverabilityTestReportsResponse', ], 'errors' => [ [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], ], ], 'ListDomainDeliverabilityCampaigns' => [ 'name' => 'ListDomainDeliverabilityCampaigns', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/email/deliverability-dashboard/domains/{SubscribedDomain}/campaigns', ], 'input' => [ 'shape' => 'ListDomainDeliverabilityCampaignsRequest', ], 'output' => [ 'shape' => 'ListDomainDeliverabilityCampaignsResponse', ], 'errors' => [ [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], ], ], 'ListEmailIdentities' => [ 'name' => 'ListEmailIdentities', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/email/identities', ], 'input' => [ 'shape' => 'ListEmailIdentitiesRequest', ], 'output' => [ 'shape' => 'ListEmailIdentitiesResponse', ], 'errors' => [ [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'ListEmailTemplates' => [ 'name' => 'ListEmailTemplates', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/email/templates', ], 'input' => [ 'shape' => 'ListEmailTemplatesRequest', ], 'output' => [ 'shape' => 'ListEmailTemplatesResponse', ], 'errors' => [ [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'ListExportJobs' => [ 'name' => 'ListExportJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/email/list-export-jobs', ], 'input' => [ 'shape' => 'ListExportJobsRequest', ], 'output' => [ 'shape' => 'ListExportJobsResponse', ], 'errors' => [ [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'ListImportJobs' => [ 'name' => 'ListImportJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/email/import-jobs/list', ], 'input' => [ 'shape' => 'ListImportJobsRequest', ], 'output' => [ 'shape' => 'ListImportJobsResponse', ], 'errors' => [ [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'ListRecommendations' => [ 'name' => 'ListRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/email/vdm/recommendations', ], 'input' => [ 'shape' => 'ListRecommendationsRequest', ], 'output' => [ 'shape' => 'ListRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], ], ], 'ListSuppressedDestinations' => [ 'name' => 'ListSuppressedDestinations', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/email/suppression/addresses', ], 'input' => [ 'shape' => 'ListSuppressedDestinationsRequest', ], 'output' => [ 'shape' => 'ListSuppressedDestinationsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/email/tags', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'PutAccountDedicatedIpWarmupAttributes' => [ 'name' => 'PutAccountDedicatedIpWarmupAttributes', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/email/account/dedicated-ips/warmup', ], 'input' => [ 'shape' => 'PutAccountDedicatedIpWarmupAttributesRequest', ], 'output' => [ 'shape' => 'PutAccountDedicatedIpWarmupAttributesResponse', ], 'errors' => [ [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'PutAccountDetails' => [ 'name' => 'PutAccountDetails', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/email/account/details', ], 'input' => [ 'shape' => 'PutAccountDetailsRequest', ], 'output' => [ 'shape' => 'PutAccountDetailsResponse', ], 'errors' => [ [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], ], ], 'PutAccountSendingAttributes' => [ 'name' => 'PutAccountSendingAttributes', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/email/account/sending', ], 'input' => [ 'shape' => 'PutAccountSendingAttributesRequest', ], 'output' => [ 'shape' => 'PutAccountSendingAttributesResponse', ], 'errors' => [ [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'PutAccountSuppressionAttributes' => [ 'name' => 'PutAccountSuppressionAttributes', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/email/account/suppression', ], 'input' => [ 'shape' => 'PutAccountSuppressionAttributesRequest', ], 'output' => [ 'shape' => 'PutAccountSuppressionAttributesResponse', ], 'errors' => [ [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'PutAccountVdmAttributes' => [ 'name' => 'PutAccountVdmAttributes', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/email/account/vdm', ], 'input' => [ 'shape' => 'PutAccountVdmAttributesRequest', ], 'output' => [ 'shape' => 'PutAccountVdmAttributesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'PutConfigurationSetDeliveryOptions' => [ 'name' => 'PutConfigurationSetDeliveryOptions', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/email/configuration-sets/{ConfigurationSetName}/delivery-options', ], 'input' => [ 'shape' => 'PutConfigurationSetDeliveryOptionsRequest', ], 'output' => [ 'shape' => 'PutConfigurationSetDeliveryOptionsResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'PutConfigurationSetReputationOptions' => [ 'name' => 'PutConfigurationSetReputationOptions', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/email/configuration-sets/{ConfigurationSetName}/reputation-options', ], 'input' => [ 'shape' => 'PutConfigurationSetReputationOptionsRequest', ], 'output' => [ 'shape' => 'PutConfigurationSetReputationOptionsResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'PutConfigurationSetSendingOptions' => [ 'name' => 'PutConfigurationSetSendingOptions', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/email/configuration-sets/{ConfigurationSetName}/sending', ], 'input' => [ 'shape' => 'PutConfigurationSetSendingOptionsRequest', ], 'output' => [ 'shape' => 'PutConfigurationSetSendingOptionsResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'PutConfigurationSetSuppressionOptions' => [ 'name' => 'PutConfigurationSetSuppressionOptions', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/email/configuration-sets/{ConfigurationSetName}/suppression-options', ], 'input' => [ 'shape' => 'PutConfigurationSetSuppressionOptionsRequest', ], 'output' => [ 'shape' => 'PutConfigurationSetSuppressionOptionsResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'PutConfigurationSetTrackingOptions' => [ 'name' => 'PutConfigurationSetTrackingOptions', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/email/configuration-sets/{ConfigurationSetName}/tracking-options', ], 'input' => [ 'shape' => 'PutConfigurationSetTrackingOptionsRequest', ], 'output' => [ 'shape' => 'PutConfigurationSetTrackingOptionsResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'PutConfigurationSetVdmOptions' => [ 'name' => 'PutConfigurationSetVdmOptions', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/email/configuration-sets/{ConfigurationSetName}/vdm-options', ], 'input' => [ 'shape' => 'PutConfigurationSetVdmOptionsRequest', ], 'output' => [ 'shape' => 'PutConfigurationSetVdmOptionsResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'PutDedicatedIpInPool' => [ 'name' => 'PutDedicatedIpInPool', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/email/dedicated-ips/{IP}/pool', ], 'input' => [ 'shape' => 'PutDedicatedIpInPoolRequest', ], 'output' => [ 'shape' => 'PutDedicatedIpInPoolResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'PutDedicatedIpPoolScalingAttributes' => [ 'name' => 'PutDedicatedIpPoolScalingAttributes', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/email/dedicated-ip-pools/{PoolName}/scaling', ], 'input' => [ 'shape' => 'PutDedicatedIpPoolScalingAttributesRequest', ], 'output' => [ 'shape' => 'PutDedicatedIpPoolScalingAttributesResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], 'idempotent' => true, ], 'PutDedicatedIpWarmupAttributes' => [ 'name' => 'PutDedicatedIpWarmupAttributes', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/email/dedicated-ips/{IP}/warmup', ], 'input' => [ 'shape' => 'PutDedicatedIpWarmupAttributesRequest', ], 'output' => [ 'shape' => 'PutDedicatedIpWarmupAttributesResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'PutDeliverabilityDashboardOption' => [ 'name' => 'PutDeliverabilityDashboardOption', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/email/deliverability-dashboard', ], 'input' => [ 'shape' => 'PutDeliverabilityDashboardOptionRequest', ], 'output' => [ 'shape' => 'PutDeliverabilityDashboardOptionResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'BadRequestException', ], ], ], 'PutEmailIdentityConfigurationSetAttributes' => [ 'name' => 'PutEmailIdentityConfigurationSetAttributes', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/email/identities/{EmailIdentity}/configuration-set', ], 'input' => [ 'shape' => 'PutEmailIdentityConfigurationSetAttributesRequest', ], 'output' => [ 'shape' => 'PutEmailIdentityConfigurationSetAttributesResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'PutEmailIdentityDkimAttributes' => [ 'name' => 'PutEmailIdentityDkimAttributes', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/email/identities/{EmailIdentity}/dkim', ], 'input' => [ 'shape' => 'PutEmailIdentityDkimAttributesRequest', ], 'output' => [ 'shape' => 'PutEmailIdentityDkimAttributesResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'PutEmailIdentityDkimSigningAttributes' => [ 'name' => 'PutEmailIdentityDkimSigningAttributes', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/email/identities/{EmailIdentity}/dkim/signing', ], 'input' => [ 'shape' => 'PutEmailIdentityDkimSigningAttributesRequest', ], 'output' => [ 'shape' => 'PutEmailIdentityDkimSigningAttributesResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'PutEmailIdentityFeedbackAttributes' => [ 'name' => 'PutEmailIdentityFeedbackAttributes', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/email/identities/{EmailIdentity}/feedback', ], 'input' => [ 'shape' => 'PutEmailIdentityFeedbackAttributesRequest', ], 'output' => [ 'shape' => 'PutEmailIdentityFeedbackAttributesResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'PutEmailIdentityMailFromAttributes' => [ 'name' => 'PutEmailIdentityMailFromAttributes', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/email/identities/{EmailIdentity}/mail-from', ], 'input' => [ 'shape' => 'PutEmailIdentityMailFromAttributesRequest', ], 'output' => [ 'shape' => 'PutEmailIdentityMailFromAttributesResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'PutSuppressedDestination' => [ 'name' => 'PutSuppressedDestination', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/email/suppression/addresses', ], 'input' => [ 'shape' => 'PutSuppressedDestinationRequest', ], 'output' => [ 'shape' => 'PutSuppressedDestinationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'SendBulkEmail' => [ 'name' => 'SendBulkEmail', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/email/outbound-bulk-emails', ], 'input' => [ 'shape' => 'SendBulkEmailRequest', ], 'output' => [ 'shape' => 'SendBulkEmailResponse', ], 'errors' => [ [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccountSuspendedException', ], [ 'shape' => 'SendingPausedException', ], [ 'shape' => 'MessageRejected', ], [ 'shape' => 'MailFromDomainNotVerifiedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], ], ], 'SendCustomVerificationEmail' => [ 'name' => 'SendCustomVerificationEmail', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/email/outbound-custom-verification-emails', ], 'input' => [ 'shape' => 'SendCustomVerificationEmailRequest', ], 'output' => [ 'shape' => 'SendCustomVerificationEmailResponse', ], 'errors' => [ [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'MessageRejected', ], [ 'shape' => 'SendingPausedException', ], [ 'shape' => 'MailFromDomainNotVerifiedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], ], ], 'SendEmail' => [ 'name' => 'SendEmail', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/email/outbound-emails', ], 'input' => [ 'shape' => 'SendEmailRequest', ], 'output' => [ 'shape' => 'SendEmailResponse', ], 'errors' => [ [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccountSuspendedException', ], [ 'shape' => 'SendingPausedException', ], [ 'shape' => 'MessageRejected', ], [ 'shape' => 'MailFromDomainNotVerifiedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/email/tags', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'TestRenderEmailTemplate' => [ 'name' => 'TestRenderEmailTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/email/templates/{TemplateName}/render', ], 'input' => [ 'shape' => 'TestRenderEmailTemplateRequest', ], 'output' => [ 'shape' => 'TestRenderEmailTemplateResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v2/email/tags', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateConfigurationSetEventDestination' => [ 'name' => 'UpdateConfigurationSetEventDestination', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/email/configuration-sets/{ConfigurationSetName}/event-destinations/{EventDestinationName}', ], 'input' => [ 'shape' => 'UpdateConfigurationSetEventDestinationRequest', ], 'output' => [ 'shape' => 'UpdateConfigurationSetEventDestinationResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'UpdateContact' => [ 'name' => 'UpdateContact', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/email/contact-lists/{ContactListName}/contacts/{EmailAddress}', ], 'input' => [ 'shape' => 'UpdateContactRequest', ], 'output' => [ 'shape' => 'UpdateContactResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'UpdateContactList' => [ 'name' => 'UpdateContactList', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/email/contact-lists/{ContactListName}', ], 'input' => [ 'shape' => 'UpdateContactListRequest', ], 'output' => [ 'shape' => 'UpdateContactListResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'UpdateCustomVerificationEmailTemplate' => [ 'name' => 'UpdateCustomVerificationEmailTemplate', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/email/custom-verification-email-templates/{TemplateName}', ], 'input' => [ 'shape' => 'UpdateCustomVerificationEmailTemplateRequest', ], 'output' => [ 'shape' => 'UpdateCustomVerificationEmailTemplateResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateEmailIdentityPolicy' => [ 'name' => 'UpdateEmailIdentityPolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/email/identities/{EmailIdentity}/policies/{PolicyName}', ], 'input' => [ 'shape' => 'UpdateEmailIdentityPolicyRequest', ], 'output' => [ 'shape' => 'UpdateEmailIdentityPolicyResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'UpdateEmailTemplate' => [ 'name' => 'UpdateEmailTemplate', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/email/templates/{TemplateName}', ], 'input' => [ 'shape' => 'UpdateEmailTemplateRequest', ], 'output' => [ 'shape' => 'UpdateEmailTemplateResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], ], 'shapes' => [ 'AccountDetails' => [ 'type' => 'structure', 'members' => [ 'MailType' => [ 'shape' => 'MailType', ], 'WebsiteURL' => [ 'shape' => 'WebsiteURL', ], 'ContactLanguage' => [ 'shape' => 'ContactLanguage', ], 'UseCaseDescription' => [ 'shape' => 'UseCaseDescription', ], 'AdditionalContactEmailAddresses' => [ 'shape' => 'AdditionalContactEmailAddresses', ], 'ReviewDetails' => [ 'shape' => 'ReviewDetails', ], ], ], 'AccountSuspendedException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'AdditionalContactEmailAddress' => [ 'type' => 'string', 'max' => 254, 'min' => 6, 'pattern' => '^(.+)@(.+)$', 'sensitive' => true, ], 'AdditionalContactEmailAddresses' => [ 'type' => 'list', 'member' => [ 'shape' => 'AdditionalContactEmailAddress', ], 'max' => 4, 'min' => 1, 'sensitive' => true, ], 'AdminEmail' => [ 'type' => 'string', ], 'AlreadyExistsException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'AmazonResourceName' => [ 'type' => 'string', ], 'AttributesData' => [ 'type' => 'string', ], 'BadRequestException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'BatchGetMetricDataQueries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchGetMetricDataQuery', ], 'max' => 10, 'min' => 1, ], 'BatchGetMetricDataQuery' => [ 'type' => 'structure', 'required' => [ 'Id', 'Namespace', 'Metric', 'StartDate', 'EndDate', ], 'members' => [ 'Id' => [ 'shape' => 'QueryIdentifier', ], 'Namespace' => [ 'shape' => 'MetricNamespace', ], 'Metric' => [ 'shape' => 'Metric', ], 'Dimensions' => [ 'shape' => 'Dimensions', ], 'StartDate' => [ 'shape' => 'Timestamp', ], 'EndDate' => [ 'shape' => 'Timestamp', ], ], ], 'BatchGetMetricDataRequest' => [ 'type' => 'structure', 'required' => [ 'Queries', ], 'members' => [ 'Queries' => [ 'shape' => 'BatchGetMetricDataQueries', ], ], ], 'BatchGetMetricDataResponse' => [ 'type' => 'structure', 'members' => [ 'Results' => [ 'shape' => 'MetricDataResultList', ], 'Errors' => [ 'shape' => 'MetricDataErrorList', ], ], ], 'BehaviorOnMxFailure' => [ 'type' => 'string', 'enum' => [ 'USE_DEFAULT_VALUE', 'REJECT_MESSAGE', ], ], 'BlacklistEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BlacklistEntry', ], ], 'BlacklistEntry' => [ 'type' => 'structure', 'members' => [ 'RblName' => [ 'shape' => 'RblName', ], 'ListingTime' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'BlacklistingDescription', ], ], ], 'BlacklistItemName' => [ 'type' => 'string', ], 'BlacklistItemNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'BlacklistItemName', ], ], 'BlacklistReport' => [ 'type' => 'map', 'key' => [ 'shape' => 'BlacklistItemName', ], 'value' => [ 'shape' => 'BlacklistEntries', ], ], 'BlacklistingDescription' => [ 'type' => 'string', ], 'Body' => [ 'type' => 'structure', 'members' => [ 'Text' => [ 'shape' => 'Content', ], 'Html' => [ 'shape' => 'Content', ], ], ], 'Bounce' => [ 'type' => 'structure', 'members' => [ 'BounceType' => [ 'shape' => 'BounceType', ], 'BounceSubType' => [ 'shape' => 'BounceSubType', ], 'DiagnosticCode' => [ 'shape' => 'DiagnosticCode', ], ], ], 'BounceSubType' => [ 'type' => 'string', ], 'BounceType' => [ 'type' => 'string', 'enum' => [ 'UNDETERMINED', 'TRANSIENT', 'PERMANENT', ], ], 'BulkEmailContent' => [ 'type' => 'structure', 'members' => [ 'Template' => [ 'shape' => 'Template', ], ], ], 'BulkEmailEntry' => [ 'type' => 'structure', 'required' => [ 'Destination', ], 'members' => [ 'Destination' => [ 'shape' => 'Destination', ], 'ReplacementTags' => [ 'shape' => 'MessageTagList', ], 'ReplacementEmailContent' => [ 'shape' => 'ReplacementEmailContent', ], 'ReplacementHeaders' => [ 'shape' => 'MessageHeaderList', ], ], ], 'BulkEmailEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BulkEmailEntry', ], ], 'BulkEmailEntryResult' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'BulkEmailStatus', ], 'Error' => [ 'shape' => 'ErrorMessage', ], 'MessageId' => [ 'shape' => 'OutboundMessageId', ], ], ], 'BulkEmailEntryResultList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BulkEmailEntryResult', ], ], 'BulkEmailStatus' => [ 'type' => 'string', 'enum' => [ 'SUCCESS', 'MESSAGE_REJECTED', 'MAIL_FROM_DOMAIN_NOT_VERIFIED', 'CONFIGURATION_SET_NOT_FOUND', 'TEMPLATE_NOT_FOUND', 'ACCOUNT_SUSPENDED', 'ACCOUNT_THROTTLED', 'ACCOUNT_DAILY_QUOTA_EXCEEDED', 'INVALID_SENDING_POOL_NAME', 'ACCOUNT_SENDING_PAUSED', 'CONFIGURATION_SET_SENDING_PAUSED', 'INVALID_PARAMETER', 'TRANSIENT_FAILURE', 'FAILED', ], ], 'CampaignId' => [ 'type' => 'string', ], 'CancelExportJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'JobId', ], ], ], 'CancelExportJobResponse' => [ 'type' => 'structure', 'members' => [], ], 'CaseId' => [ 'type' => 'string', ], 'Charset' => [ 'type' => 'string', ], 'CloudWatchDestination' => [ 'type' => 'structure', 'required' => [ 'DimensionConfigurations', ], 'members' => [ 'DimensionConfigurations' => [ 'shape' => 'CloudWatchDimensionConfigurations', ], ], ], 'CloudWatchDimensionConfiguration' => [ 'type' => 'structure', 'required' => [ 'DimensionName', 'DimensionValueSource', 'DefaultDimensionValue', ], 'members' => [ 'DimensionName' => [ 'shape' => 'DimensionName', ], 'DimensionValueSource' => [ 'shape' => 'DimensionValueSource', ], 'DefaultDimensionValue' => [ 'shape' => 'DefaultDimensionValue', ], ], ], 'CloudWatchDimensionConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'CloudWatchDimensionConfiguration', ], ], 'Complaint' => [ 'type' => 'structure', 'members' => [ 'ComplaintSubType' => [ 'shape' => 'ComplaintSubType', ], 'ComplaintFeedbackType' => [ 'shape' => 'ComplaintFeedbackType', ], ], ], 'ComplaintFeedbackType' => [ 'type' => 'string', ], 'ComplaintSubType' => [ 'type' => 'string', ], 'ConcurrentModificationException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'ConfigurationSetName' => [ 'type' => 'string', ], 'ConfigurationSetNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfigurationSetName', ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'Contact' => [ 'type' => 'structure', 'members' => [ 'EmailAddress' => [ 'shape' => 'EmailAddress', ], 'TopicPreferences' => [ 'shape' => 'TopicPreferenceList', ], 'TopicDefaultPreferences' => [ 'shape' => 'TopicPreferenceList', ], 'UnsubscribeAll' => [ 'shape' => 'UnsubscribeAll', ], 'LastUpdatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'ContactLanguage' => [ 'type' => 'string', 'enum' => [ 'EN', 'JA', ], ], 'ContactList' => [ 'type' => 'structure', 'members' => [ 'ContactListName' => [ 'shape' => 'ContactListName', ], 'LastUpdatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'ContactListDestination' => [ 'type' => 'structure', 'required' => [ 'ContactListName', 'ContactListImportAction', ], 'members' => [ 'ContactListName' => [ 'shape' => 'ContactListName', ], 'ContactListImportAction' => [ 'shape' => 'ContactListImportAction', ], ], ], 'ContactListImportAction' => [ 'type' => 'string', 'enum' => [ 'DELETE', 'PUT', ], ], 'ContactListName' => [ 'type' => 'string', ], 'Content' => [ 'type' => 'structure', 'required' => [ 'Data', ], 'members' => [ 'Data' => [ 'shape' => 'MessageData', ], 'Charset' => [ 'shape' => 'Charset', ], ], ], 'Counter' => [ 'type' => 'long', ], 'CreateConfigurationSetEventDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationSetName', 'EventDestinationName', 'EventDestination', ], 'members' => [ 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetName', 'location' => 'uri', 'locationName' => 'ConfigurationSetName', ], 'EventDestinationName' => [ 'shape' => 'EventDestinationName', ], 'EventDestination' => [ 'shape' => 'EventDestinationDefinition', ], ], ], 'CreateConfigurationSetEventDestinationResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateConfigurationSetRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationSetName', ], 'members' => [ 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetName', ], 'TrackingOptions' => [ 'shape' => 'TrackingOptions', ], 'DeliveryOptions' => [ 'shape' => 'DeliveryOptions', ], 'ReputationOptions' => [ 'shape' => 'ReputationOptions', ], 'SendingOptions' => [ 'shape' => 'SendingOptions', ], 'Tags' => [ 'shape' => 'TagList', ], 'SuppressionOptions' => [ 'shape' => 'SuppressionOptions', ], 'VdmOptions' => [ 'shape' => 'VdmOptions', ], ], ], 'CreateConfigurationSetResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateContactListRequest' => [ 'type' => 'structure', 'required' => [ 'ContactListName', ], 'members' => [ 'ContactListName' => [ 'shape' => 'ContactListName', ], 'Topics' => [ 'shape' => 'Topics', ], 'Description' => [ 'shape' => 'Description', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateContactListResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateContactRequest' => [ 'type' => 'structure', 'required' => [ 'ContactListName', 'EmailAddress', ], 'members' => [ 'ContactListName' => [ 'shape' => 'ContactListName', 'location' => 'uri', 'locationName' => 'ContactListName', ], 'EmailAddress' => [ 'shape' => 'EmailAddress', ], 'TopicPreferences' => [ 'shape' => 'TopicPreferenceList', ], 'UnsubscribeAll' => [ 'shape' => 'UnsubscribeAll', ], 'AttributesData' => [ 'shape' => 'AttributesData', ], ], ], 'CreateContactResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateCustomVerificationEmailTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'TemplateName', 'FromEmailAddress', 'TemplateSubject', 'TemplateContent', 'SuccessRedirectionURL', 'FailureRedirectionURL', ], 'members' => [ 'TemplateName' => [ 'shape' => 'EmailTemplateName', ], 'FromEmailAddress' => [ 'shape' => 'EmailAddress', ], 'TemplateSubject' => [ 'shape' => 'EmailTemplateSubject', ], 'TemplateContent' => [ 'shape' => 'TemplateContent', ], 'SuccessRedirectionURL' => [ 'shape' => 'SuccessRedirectionURL', ], 'FailureRedirectionURL' => [ 'shape' => 'FailureRedirectionURL', ], ], ], 'CreateCustomVerificationEmailTemplateResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateDedicatedIpPoolRequest' => [ 'type' => 'structure', 'required' => [ 'PoolName', ], 'members' => [ 'PoolName' => [ 'shape' => 'PoolName', ], 'Tags' => [ 'shape' => 'TagList', ], 'ScalingMode' => [ 'shape' => 'ScalingMode', ], ], ], 'CreateDedicatedIpPoolResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateDeliverabilityTestReportRequest' => [ 'type' => 'structure', 'required' => [ 'FromEmailAddress', 'Content', ], 'members' => [ 'ReportName' => [ 'shape' => 'ReportName', ], 'FromEmailAddress' => [ 'shape' => 'EmailAddress', ], 'Content' => [ 'shape' => 'EmailContent', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateDeliverabilityTestReportResponse' => [ 'type' => 'structure', 'required' => [ 'ReportId', 'DeliverabilityTestStatus', ], 'members' => [ 'ReportId' => [ 'shape' => 'ReportId', ], 'DeliverabilityTestStatus' => [ 'shape' => 'DeliverabilityTestStatus', ], ], ], 'CreateEmailIdentityPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'EmailIdentity', 'PolicyName', 'Policy', ], 'members' => [ 'EmailIdentity' => [ 'shape' => 'Identity', 'location' => 'uri', 'locationName' => 'EmailIdentity', ], 'PolicyName' => [ 'shape' => 'PolicyName', 'location' => 'uri', 'locationName' => 'PolicyName', ], 'Policy' => [ 'shape' => 'Policy', ], ], ], 'CreateEmailIdentityPolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateEmailIdentityRequest' => [ 'type' => 'structure', 'required' => [ 'EmailIdentity', ], 'members' => [ 'EmailIdentity' => [ 'shape' => 'Identity', ], 'Tags' => [ 'shape' => 'TagList', ], 'DkimSigningAttributes' => [ 'shape' => 'DkimSigningAttributes', ], 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetName', ], ], ], 'CreateEmailIdentityResponse' => [ 'type' => 'structure', 'members' => [ 'IdentityType' => [ 'shape' => 'IdentityType', ], 'VerifiedForSendingStatus' => [ 'shape' => 'Enabled', ], 'DkimAttributes' => [ 'shape' => 'DkimAttributes', ], ], ], 'CreateEmailTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'TemplateName', 'TemplateContent', ], 'members' => [ 'TemplateName' => [ 'shape' => 'EmailTemplateName', ], 'TemplateContent' => [ 'shape' => 'EmailTemplateContent', ], ], ], 'CreateEmailTemplateResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateExportJobRequest' => [ 'type' => 'structure', 'required' => [ 'ExportDataSource', 'ExportDestination', ], 'members' => [ 'ExportDataSource' => [ 'shape' => 'ExportDataSource', ], 'ExportDestination' => [ 'shape' => 'ExportDestination', ], ], ], 'CreateExportJobResponse' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], ], ], 'CreateImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'ImportDestination', 'ImportDataSource', ], 'members' => [ 'ImportDestination' => [ 'shape' => 'ImportDestination', ], 'ImportDataSource' => [ 'shape' => 'ImportDataSource', ], ], ], 'CreateImportJobResponse' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], ], ], 'CustomRedirectDomain' => [ 'type' => 'string', ], 'CustomVerificationEmailTemplateMetadata' => [ 'type' => 'structure', 'members' => [ 'TemplateName' => [ 'shape' => 'EmailTemplateName', ], 'FromEmailAddress' => [ 'shape' => 'EmailAddress', ], 'TemplateSubject' => [ 'shape' => 'EmailTemplateSubject', ], 'SuccessRedirectionURL' => [ 'shape' => 'SuccessRedirectionURL', ], 'FailureRedirectionURL' => [ 'shape' => 'FailureRedirectionURL', ], ], ], 'CustomVerificationEmailTemplatesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomVerificationEmailTemplateMetadata', ], ], 'DailyVolume' => [ 'type' => 'structure', 'members' => [ 'StartDate' => [ 'shape' => 'Timestamp', ], 'VolumeStatistics' => [ 'shape' => 'VolumeStatistics', ], 'DomainIspPlacements' => [ 'shape' => 'DomainIspPlacements', ], ], ], 'DailyVolumes' => [ 'type' => 'list', 'member' => [ 'shape' => 'DailyVolume', ], ], 'DashboardAttributes' => [ 'type' => 'structure', 'members' => [ 'EngagementMetrics' => [ 'shape' => 'FeatureStatus', ], ], ], 'DashboardOptions' => [ 'type' => 'structure', 'members' => [ 'EngagementMetrics' => [ 'shape' => 'FeatureStatus', ], ], ], 'DataFormat' => [ 'type' => 'string', 'enum' => [ 'CSV', 'JSON', ], ], 'DedicatedIp' => [ 'type' => 'structure', 'required' => [ 'Ip', 'WarmupStatus', 'WarmupPercentage', ], 'members' => [ 'Ip' => [ 'shape' => 'Ip', ], 'WarmupStatus' => [ 'shape' => 'WarmupStatus', ], 'WarmupPercentage' => [ 'shape' => 'Percentage100Wrapper', ], 'PoolName' => [ 'shape' => 'PoolName', ], ], ], 'DedicatedIpList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DedicatedIp', ], ], 'DedicatedIpPool' => [ 'type' => 'structure', 'required' => [ 'PoolName', 'ScalingMode', ], 'members' => [ 'PoolName' => [ 'shape' => 'PoolName', ], 'ScalingMode' => [ 'shape' => 'ScalingMode', ], ], ], 'DefaultDimensionValue' => [ 'type' => 'string', ], 'DeleteConfigurationSetEventDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationSetName', 'EventDestinationName', ], 'members' => [ 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetName', 'location' => 'uri', 'locationName' => 'ConfigurationSetName', ], 'EventDestinationName' => [ 'shape' => 'EventDestinationName', 'location' => 'uri', 'locationName' => 'EventDestinationName', ], ], ], 'DeleteConfigurationSetEventDestinationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteConfigurationSetRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationSetName', ], 'members' => [ 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetName', 'location' => 'uri', 'locationName' => 'ConfigurationSetName', ], ], ], 'DeleteConfigurationSetResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteContactListRequest' => [ 'type' => 'structure', 'required' => [ 'ContactListName', ], 'members' => [ 'ContactListName' => [ 'shape' => 'ContactListName', 'location' => 'uri', 'locationName' => 'ContactListName', ], ], ], 'DeleteContactListResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteContactRequest' => [ 'type' => 'structure', 'required' => [ 'ContactListName', 'EmailAddress', ], 'members' => [ 'ContactListName' => [ 'shape' => 'ContactListName', 'location' => 'uri', 'locationName' => 'ContactListName', ], 'EmailAddress' => [ 'shape' => 'EmailAddress', 'location' => 'uri', 'locationName' => 'EmailAddress', ], ], ], 'DeleteContactResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteCustomVerificationEmailTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'TemplateName', ], 'members' => [ 'TemplateName' => [ 'shape' => 'EmailTemplateName', 'location' => 'uri', 'locationName' => 'TemplateName', ], ], ], 'DeleteCustomVerificationEmailTemplateResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDedicatedIpPoolRequest' => [ 'type' => 'structure', 'required' => [ 'PoolName', ], 'members' => [ 'PoolName' => [ 'shape' => 'PoolName', 'location' => 'uri', 'locationName' => 'PoolName', ], ], ], 'DeleteDedicatedIpPoolResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteEmailIdentityPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'EmailIdentity', 'PolicyName', ], 'members' => [ 'EmailIdentity' => [ 'shape' => 'Identity', 'location' => 'uri', 'locationName' => 'EmailIdentity', ], 'PolicyName' => [ 'shape' => 'PolicyName', 'location' => 'uri', 'locationName' => 'PolicyName', ], ], ], 'DeleteEmailIdentityPolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteEmailIdentityRequest' => [ 'type' => 'structure', 'required' => [ 'EmailIdentity', ], 'members' => [ 'EmailIdentity' => [ 'shape' => 'Identity', 'location' => 'uri', 'locationName' => 'EmailIdentity', ], ], ], 'DeleteEmailIdentityResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteEmailTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'TemplateName', ], 'members' => [ 'TemplateName' => [ 'shape' => 'EmailTemplateName', 'location' => 'uri', 'locationName' => 'TemplateName', ], ], ], 'DeleteEmailTemplateResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSuppressedDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'EmailAddress', ], 'members' => [ 'EmailAddress' => [ 'shape' => 'EmailAddress', 'location' => 'uri', 'locationName' => 'EmailAddress', ], ], ], 'DeleteSuppressedDestinationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeliverabilityDashboardAccountStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'PENDING_EXPIRATION', 'DISABLED', ], ], 'DeliverabilityTestReport' => [ 'type' => 'structure', 'members' => [ 'ReportId' => [ 'shape' => 'ReportId', ], 'ReportName' => [ 'shape' => 'ReportName', ], 'Subject' => [ 'shape' => 'DeliverabilityTestSubject', ], 'FromEmailAddress' => [ 'shape' => 'EmailAddress', ], 'CreateDate' => [ 'shape' => 'Timestamp', ], 'DeliverabilityTestStatus' => [ 'shape' => 'DeliverabilityTestStatus', ], ], ], 'DeliverabilityTestReports' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeliverabilityTestReport', ], ], 'DeliverabilityTestStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'COMPLETED', ], ], 'DeliverabilityTestSubject' => [ 'type' => 'string', ], 'DeliveryEventType' => [ 'type' => 'string', 'enum' => [ 'SEND', 'DELIVERY', 'TRANSIENT_BOUNCE', 'PERMANENT_BOUNCE', 'UNDETERMINED_BOUNCE', 'COMPLAINT', ], ], 'DeliveryOptions' => [ 'type' => 'structure', 'members' => [ 'TlsPolicy' => [ 'shape' => 'TlsPolicy', ], 'SendingPoolName' => [ 'shape' => 'PoolName', ], ], ], 'Description' => [ 'type' => 'string', ], 'Destination' => [ 'type' => 'structure', 'members' => [ 'ToAddresses' => [ 'shape' => 'EmailAddressList', ], 'CcAddresses' => [ 'shape' => 'EmailAddressList', ], 'BccAddresses' => [ 'shape' => 'EmailAddressList', ], ], ], 'DiagnosticCode' => [ 'type' => 'string', ], 'DimensionName' => [ 'type' => 'string', ], 'DimensionValueSource' => [ 'type' => 'string', 'enum' => [ 'MESSAGE_TAG', 'EMAIL_HEADER', 'LINK_TAG', ], ], 'Dimensions' => [ 'type' => 'map', 'key' => [ 'shape' => 'MetricDimensionName', ], 'value' => [ 'shape' => 'MetricDimensionValue', ], 'max' => 3, 'min' => 1, ], 'DisplayName' => [ 'type' => 'string', ], 'DkimAttributes' => [ 'type' => 'structure', 'members' => [ 'SigningEnabled' => [ 'shape' => 'Enabled', ], 'Status' => [ 'shape' => 'DkimStatus', ], 'Tokens' => [ 'shape' => 'DnsTokenList', ], 'SigningAttributesOrigin' => [ 'shape' => 'DkimSigningAttributesOrigin', ], 'NextSigningKeyLength' => [ 'shape' => 'DkimSigningKeyLength', ], 'CurrentSigningKeyLength' => [ 'shape' => 'DkimSigningKeyLength', ], 'LastKeyGenerationTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'DkimSigningAttributes' => [ 'type' => 'structure', 'members' => [ 'DomainSigningSelector' => [ 'shape' => 'Selector', ], 'DomainSigningPrivateKey' => [ 'shape' => 'PrivateKey', ], 'NextSigningKeyLength' => [ 'shape' => 'DkimSigningKeyLength', ], ], ], 'DkimSigningAttributesOrigin' => [ 'type' => 'string', 'enum' => [ 'AWS_SES', 'EXTERNAL', ], ], 'DkimSigningKeyLength' => [ 'type' => 'string', 'enum' => [ 'RSA_1024_BIT', 'RSA_2048_BIT', ], ], 'DkimStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'SUCCESS', 'FAILED', 'TEMPORARY_FAILURE', 'NOT_STARTED', ], ], 'DnsToken' => [ 'type' => 'string', ], 'DnsTokenList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DnsToken', ], ], 'Domain' => [ 'type' => 'string', ], 'DomainDeliverabilityCampaign' => [ 'type' => 'structure', 'members' => [ 'CampaignId' => [ 'shape' => 'CampaignId', ], 'ImageUrl' => [ 'shape' => 'ImageUrl', ], 'Subject' => [ 'shape' => 'Subject', ], 'FromAddress' => [ 'shape' => 'Identity', ], 'SendingIps' => [ 'shape' => 'IpList', ], 'FirstSeenDateTime' => [ 'shape' => 'Timestamp', ], 'LastSeenDateTime' => [ 'shape' => 'Timestamp', ], 'InboxCount' => [ 'shape' => 'Volume', ], 'SpamCount' => [ 'shape' => 'Volume', ], 'ReadRate' => [ 'shape' => 'Percentage', ], 'DeleteRate' => [ 'shape' => 'Percentage', ], 'ReadDeleteRate' => [ 'shape' => 'Percentage', ], 'ProjectedVolume' => [ 'shape' => 'Volume', ], 'Esps' => [ 'shape' => 'Esps', ], ], ], 'DomainDeliverabilityCampaignList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DomainDeliverabilityCampaign', ], ], 'DomainDeliverabilityTrackingOption' => [ 'type' => 'structure', 'members' => [ 'Domain' => [ 'shape' => 'Domain', ], 'SubscriptionStartDate' => [ 'shape' => 'Timestamp', ], 'InboxPlacementTrackingOption' => [ 'shape' => 'InboxPlacementTrackingOption', ], ], ], 'DomainDeliverabilityTrackingOptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'DomainDeliverabilityTrackingOption', ], ], 'DomainIspPlacement' => [ 'type' => 'structure', 'members' => [ 'IspName' => [ 'shape' => 'IspName', ], 'InboxRawCount' => [ 'shape' => 'Volume', ], 'SpamRawCount' => [ 'shape' => 'Volume', ], 'InboxPercentage' => [ 'shape' => 'Percentage', ], 'SpamPercentage' => [ 'shape' => 'Percentage', ], ], ], 'DomainIspPlacements' => [ 'type' => 'list', 'member' => [ 'shape' => 'DomainIspPlacement', ], ], 'EmailAddress' => [ 'type' => 'string', ], 'EmailAddressFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InsightsEmailAddress', ], 'max' => 5, ], 'EmailAddressList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EmailAddress', ], ], 'EmailContent' => [ 'type' => 'structure', 'members' => [ 'Simple' => [ 'shape' => 'Message', ], 'Raw' => [ 'shape' => 'RawMessage', ], 'Template' => [ 'shape' => 'Template', ], ], ], 'EmailInsights' => [ 'type' => 'structure', 'members' => [ 'Destination' => [ 'shape' => 'InsightsEmailAddress', ], 'Isp' => [ 'shape' => 'Isp', ], 'Events' => [ 'shape' => 'InsightsEvents', ], ], ], 'EmailInsightsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EmailInsights', ], ], 'EmailSubject' => [ 'type' => 'string', 'max' => 998, 'min' => 1, 'sensitive' => true, ], 'EmailSubjectFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EmailSubject', ], 'max' => 1, ], 'EmailTemplateContent' => [ 'type' => 'structure', 'members' => [ 'Subject' => [ 'shape' => 'EmailTemplateSubject', ], 'Text' => [ 'shape' => 'EmailTemplateText', ], 'Html' => [ 'shape' => 'EmailTemplateHtml', ], ], ], 'EmailTemplateData' => [ 'type' => 'string', 'max' => 262144, ], 'EmailTemplateHtml' => [ 'type' => 'string', ], 'EmailTemplateMetadata' => [ 'type' => 'structure', 'members' => [ 'TemplateName' => [ 'shape' => 'EmailTemplateName', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'EmailTemplateMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EmailTemplateMetadata', ], ], 'EmailTemplateName' => [ 'type' => 'string', 'min' => 1, ], 'EmailTemplateSubject' => [ 'type' => 'string', ], 'EmailTemplateText' => [ 'type' => 'string', ], 'Enabled' => [ 'type' => 'boolean', ], 'EnabledWrapper' => [ 'type' => 'boolean', ], 'EngagementEventType' => [ 'type' => 'string', 'enum' => [ 'OPEN', 'CLICK', ], ], 'ErrorMessage' => [ 'type' => 'string', ], 'Esp' => [ 'type' => 'string', ], 'Esps' => [ 'type' => 'list', 'member' => [ 'shape' => 'Esp', ], ], 'EventBridgeDestination' => [ 'type' => 'structure', 'required' => [ 'EventBusArn', ], 'members' => [ 'EventBusArn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'EventDestination' => [ 'type' => 'structure', 'required' => [ 'Name', 'MatchingEventTypes', ], 'members' => [ 'Name' => [ 'shape' => 'EventDestinationName', ], 'Enabled' => [ 'shape' => 'Enabled', ], 'MatchingEventTypes' => [ 'shape' => 'EventTypes', ], 'KinesisFirehoseDestination' => [ 'shape' => 'KinesisFirehoseDestination', ], 'CloudWatchDestination' => [ 'shape' => 'CloudWatchDestination', ], 'SnsDestination' => [ 'shape' => 'SnsDestination', ], 'EventBridgeDestination' => [ 'shape' => 'EventBridgeDestination', ], 'PinpointDestination' => [ 'shape' => 'PinpointDestination', ], ], ], 'EventDestinationDefinition' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Enabled', ], 'MatchingEventTypes' => [ 'shape' => 'EventTypes', ], 'KinesisFirehoseDestination' => [ 'shape' => 'KinesisFirehoseDestination', ], 'CloudWatchDestination' => [ 'shape' => 'CloudWatchDestination', ], 'SnsDestination' => [ 'shape' => 'SnsDestination', ], 'EventBridgeDestination' => [ 'shape' => 'EventBridgeDestination', ], 'PinpointDestination' => [ 'shape' => 'PinpointDestination', ], ], ], 'EventDestinationName' => [ 'type' => 'string', ], 'EventDestinations' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventDestination', ], ], 'EventDetails' => [ 'type' => 'structure', 'members' => [ 'Bounce' => [ 'shape' => 'Bounce', ], 'Complaint' => [ 'shape' => 'Complaint', ], ], ], 'EventType' => [ 'type' => 'string', 'enum' => [ 'SEND', 'REJECT', 'BOUNCE', 'COMPLAINT', 'DELIVERY', 'OPEN', 'CLICK', 'RENDERING_FAILURE', 'DELIVERY_DELAY', 'SUBSCRIPTION', ], ], 'EventTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventType', ], ], 'ExportDataSource' => [ 'type' => 'structure', 'members' => [ 'MetricsDataSource' => [ 'shape' => 'MetricsDataSource', ], 'MessageInsightsDataSource' => [ 'shape' => 'MessageInsightsDataSource', ], ], ], 'ExportDestination' => [ 'type' => 'structure', 'required' => [ 'DataFormat', ], 'members' => [ 'DataFormat' => [ 'shape' => 'DataFormat', ], 'S3Url' => [ 'shape' => 'S3Url', ], ], ], 'ExportDimensionValue' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricDimensionValue', ], 'max' => 10, 'min' => 1, ], 'ExportDimensions' => [ 'type' => 'map', 'key' => [ 'shape' => 'MetricDimensionName', ], 'value' => [ 'shape' => 'ExportDimensionValue', ], 'max' => 3, 'min' => 1, ], 'ExportJobSummary' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'ExportSourceType' => [ 'shape' => 'ExportSourceType', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], 'CompletedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'ExportJobSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExportJobSummary', ], ], 'ExportMetric' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'Metric', ], 'Aggregation' => [ 'shape' => 'MetricAggregation', ], ], ], 'ExportMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExportMetric', ], 'max' => 10, 'min' => 1, ], 'ExportSourceType' => [ 'type' => 'string', 'enum' => [ 'METRICS_DATA', 'MESSAGE_INSIGHTS', ], ], 'ExportStatistics' => [ 'type' => 'structure', 'members' => [ 'ProcessedRecordsCount' => [ 'shape' => 'ProcessedRecordsCount', ], 'ExportedRecordsCount' => [ 'shape' => 'ExportedRecordsCount', ], ], ], 'ExportedRecordsCount' => [ 'type' => 'integer', ], 'FailedRecordsCount' => [ 'type' => 'integer', ], 'FailedRecordsS3Url' => [ 'type' => 'string', ], 'FailureInfo' => [ 'type' => 'structure', 'members' => [ 'FailedRecordsS3Url' => [ 'shape' => 'FailedRecordsS3Url', ], 'ErrorMessage' => [ 'shape' => 'ErrorMessage', ], ], ], 'FailureRedirectionURL' => [ 'type' => 'string', ], 'FeatureStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'FeedbackId' => [ 'type' => 'string', ], 'GeneralEnforcementStatus' => [ 'type' => 'string', ], 'GetAccountRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetAccountResponse' => [ 'type' => 'structure', 'members' => [ 'DedicatedIpAutoWarmupEnabled' => [ 'shape' => 'Enabled', ], 'EnforcementStatus' => [ 'shape' => 'GeneralEnforcementStatus', ], 'ProductionAccessEnabled' => [ 'shape' => 'Enabled', ], 'SendQuota' => [ 'shape' => 'SendQuota', ], 'SendingEnabled' => [ 'shape' => 'Enabled', ], 'SuppressionAttributes' => [ 'shape' => 'SuppressionAttributes', ], 'Details' => [ 'shape' => 'AccountDetails', ], 'VdmAttributes' => [ 'shape' => 'VdmAttributes', ], ], ], 'GetBlacklistReportsRequest' => [ 'type' => 'structure', 'required' => [ 'BlacklistItemNames', ], 'members' => [ 'BlacklistItemNames' => [ 'shape' => 'BlacklistItemNames', 'location' => 'querystring', 'locationName' => 'BlacklistItemNames', ], ], ], 'GetBlacklistReportsResponse' => [ 'type' => 'structure', 'required' => [ 'BlacklistReport', ], 'members' => [ 'BlacklistReport' => [ 'shape' => 'BlacklistReport', ], ], ], 'GetConfigurationSetEventDestinationsRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationSetName', ], 'members' => [ 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetName', 'location' => 'uri', 'locationName' => 'ConfigurationSetName', ], ], ], 'GetConfigurationSetEventDestinationsResponse' => [ 'type' => 'structure', 'members' => [ 'EventDestinations' => [ 'shape' => 'EventDestinations', ], ], ], 'GetConfigurationSetRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationSetName', ], 'members' => [ 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetName', 'location' => 'uri', 'locationName' => 'ConfigurationSetName', ], ], ], 'GetConfigurationSetResponse' => [ 'type' => 'structure', 'members' => [ 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetName', ], 'TrackingOptions' => [ 'shape' => 'TrackingOptions', ], 'DeliveryOptions' => [ 'shape' => 'DeliveryOptions', ], 'ReputationOptions' => [ 'shape' => 'ReputationOptions', ], 'SendingOptions' => [ 'shape' => 'SendingOptions', ], 'Tags' => [ 'shape' => 'TagList', ], 'SuppressionOptions' => [ 'shape' => 'SuppressionOptions', ], 'VdmOptions' => [ 'shape' => 'VdmOptions', ], ], ], 'GetContactListRequest' => [ 'type' => 'structure', 'required' => [ 'ContactListName', ], 'members' => [ 'ContactListName' => [ 'shape' => 'ContactListName', 'location' => 'uri', 'locationName' => 'ContactListName', ], ], ], 'GetContactListResponse' => [ 'type' => 'structure', 'members' => [ 'ContactListName' => [ 'shape' => 'ContactListName', ], 'Topics' => [ 'shape' => 'Topics', ], 'Description' => [ 'shape' => 'Description', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTimestamp' => [ 'shape' => 'Timestamp', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'GetContactRequest' => [ 'type' => 'structure', 'required' => [ 'ContactListName', 'EmailAddress', ], 'members' => [ 'ContactListName' => [ 'shape' => 'ContactListName', 'location' => 'uri', 'locationName' => 'ContactListName', ], 'EmailAddress' => [ 'shape' => 'EmailAddress', 'location' => 'uri', 'locationName' => 'EmailAddress', ], ], ], 'GetContactResponse' => [ 'type' => 'structure', 'members' => [ 'ContactListName' => [ 'shape' => 'ContactListName', ], 'EmailAddress' => [ 'shape' => 'EmailAddress', ], 'TopicPreferences' => [ 'shape' => 'TopicPreferenceList', ], 'TopicDefaultPreferences' => [ 'shape' => 'TopicPreferenceList', ], 'UnsubscribeAll' => [ 'shape' => 'UnsubscribeAll', ], 'AttributesData' => [ 'shape' => 'AttributesData', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'GetCustomVerificationEmailTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'TemplateName', ], 'members' => [ 'TemplateName' => [ 'shape' => 'EmailTemplateName', 'location' => 'uri', 'locationName' => 'TemplateName', ], ], ], 'GetCustomVerificationEmailTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'TemplateName' => [ 'shape' => 'EmailTemplateName', ], 'FromEmailAddress' => [ 'shape' => 'EmailAddress', ], 'TemplateSubject' => [ 'shape' => 'EmailTemplateSubject', ], 'TemplateContent' => [ 'shape' => 'TemplateContent', ], 'SuccessRedirectionURL' => [ 'shape' => 'SuccessRedirectionURL', ], 'FailureRedirectionURL' => [ 'shape' => 'FailureRedirectionURL', ], ], ], 'GetDedicatedIpPoolRequest' => [ 'type' => 'structure', 'required' => [ 'PoolName', ], 'members' => [ 'PoolName' => [ 'shape' => 'PoolName', 'location' => 'uri', 'locationName' => 'PoolName', ], ], ], 'GetDedicatedIpPoolResponse' => [ 'type' => 'structure', 'members' => [ 'DedicatedIpPool' => [ 'shape' => 'DedicatedIpPool', ], ], ], 'GetDedicatedIpRequest' => [ 'type' => 'structure', 'required' => [ 'Ip', ], 'members' => [ 'Ip' => [ 'shape' => 'Ip', 'location' => 'uri', 'locationName' => 'IP', ], ], ], 'GetDedicatedIpResponse' => [ 'type' => 'structure', 'members' => [ 'DedicatedIp' => [ 'shape' => 'DedicatedIp', ], ], ], 'GetDedicatedIpsRequest' => [ 'type' => 'structure', 'members' => [ 'PoolName' => [ 'shape' => 'PoolName', 'location' => 'querystring', 'locationName' => 'PoolName', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'PageSize' => [ 'shape' => 'MaxItems', 'location' => 'querystring', 'locationName' => 'PageSize', ], ], ], 'GetDedicatedIpsResponse' => [ 'type' => 'structure', 'members' => [ 'DedicatedIps' => [ 'shape' => 'DedicatedIpList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetDeliverabilityDashboardOptionsRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetDeliverabilityDashboardOptionsResponse' => [ 'type' => 'structure', 'required' => [ 'DashboardEnabled', ], 'members' => [ 'DashboardEnabled' => [ 'shape' => 'Enabled', ], 'SubscriptionExpiryDate' => [ 'shape' => 'Timestamp', ], 'AccountStatus' => [ 'shape' => 'DeliverabilityDashboardAccountStatus', ], 'ActiveSubscribedDomains' => [ 'shape' => 'DomainDeliverabilityTrackingOptions', ], 'PendingExpirationSubscribedDomains' => [ 'shape' => 'DomainDeliverabilityTrackingOptions', ], ], ], 'GetDeliverabilityTestReportRequest' => [ 'type' => 'structure', 'required' => [ 'ReportId', ], 'members' => [ 'ReportId' => [ 'shape' => 'ReportId', 'location' => 'uri', 'locationName' => 'ReportId', ], ], ], 'GetDeliverabilityTestReportResponse' => [ 'type' => 'structure', 'required' => [ 'DeliverabilityTestReport', 'OverallPlacement', 'IspPlacements', ], 'members' => [ 'DeliverabilityTestReport' => [ 'shape' => 'DeliverabilityTestReport', ], 'OverallPlacement' => [ 'shape' => 'PlacementStatistics', ], 'IspPlacements' => [ 'shape' => 'IspPlacements', ], 'Message' => [ 'shape' => 'MessageContent', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'GetDomainDeliverabilityCampaignRequest' => [ 'type' => 'structure', 'required' => [ 'CampaignId', ], 'members' => [ 'CampaignId' => [ 'shape' => 'CampaignId', 'location' => 'uri', 'locationName' => 'CampaignId', ], ], ], 'GetDomainDeliverabilityCampaignResponse' => [ 'type' => 'structure', 'required' => [ 'DomainDeliverabilityCampaign', ], 'members' => [ 'DomainDeliverabilityCampaign' => [ 'shape' => 'DomainDeliverabilityCampaign', ], ], ], 'GetDomainStatisticsReportRequest' => [ 'type' => 'structure', 'required' => [ 'Domain', 'StartDate', 'EndDate', ], 'members' => [ 'Domain' => [ 'shape' => 'Identity', 'location' => 'uri', 'locationName' => 'Domain', ], 'StartDate' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'StartDate', ], 'EndDate' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'EndDate', ], ], ], 'GetDomainStatisticsReportResponse' => [ 'type' => 'structure', 'required' => [ 'OverallVolume', 'DailyVolumes', ], 'members' => [ 'OverallVolume' => [ 'shape' => 'OverallVolume', ], 'DailyVolumes' => [ 'shape' => 'DailyVolumes', ], ], ], 'GetEmailIdentityPoliciesRequest' => [ 'type' => 'structure', 'required' => [ 'EmailIdentity', ], 'members' => [ 'EmailIdentity' => [ 'shape' => 'Identity', 'location' => 'uri', 'locationName' => 'EmailIdentity', ], ], ], 'GetEmailIdentityPoliciesResponse' => [ 'type' => 'structure', 'members' => [ 'Policies' => [ 'shape' => 'PolicyMap', ], ], ], 'GetEmailIdentityRequest' => [ 'type' => 'structure', 'required' => [ 'EmailIdentity', ], 'members' => [ 'EmailIdentity' => [ 'shape' => 'Identity', 'location' => 'uri', 'locationName' => 'EmailIdentity', ], ], ], 'GetEmailIdentityResponse' => [ 'type' => 'structure', 'members' => [ 'IdentityType' => [ 'shape' => 'IdentityType', ], 'FeedbackForwardingStatus' => [ 'shape' => 'Enabled', ], 'VerifiedForSendingStatus' => [ 'shape' => 'Enabled', ], 'DkimAttributes' => [ 'shape' => 'DkimAttributes', ], 'MailFromAttributes' => [ 'shape' => 'MailFromAttributes', ], 'Policies' => [ 'shape' => 'PolicyMap', ], 'Tags' => [ 'shape' => 'TagList', ], 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetName', ], 'VerificationStatus' => [ 'shape' => 'VerificationStatus', ], 'VerificationInfo' => [ 'shape' => 'VerificationInfo', ], ], ], 'GetEmailTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'TemplateName', ], 'members' => [ 'TemplateName' => [ 'shape' => 'EmailTemplateName', 'location' => 'uri', 'locationName' => 'TemplateName', ], ], ], 'GetEmailTemplateResponse' => [ 'type' => 'structure', 'required' => [ 'TemplateName', 'TemplateContent', ], 'members' => [ 'TemplateName' => [ 'shape' => 'EmailTemplateName', ], 'TemplateContent' => [ 'shape' => 'EmailTemplateContent', ], ], ], 'GetExportJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'JobId', ], ], ], 'GetExportJobResponse' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'ExportSourceType' => [ 'shape' => 'ExportSourceType', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], 'ExportDestination' => [ 'shape' => 'ExportDestination', ], 'ExportDataSource' => [ 'shape' => 'ExportDataSource', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], 'CompletedTimestamp' => [ 'shape' => 'Timestamp', ], 'FailureInfo' => [ 'shape' => 'FailureInfo', ], 'Statistics' => [ 'shape' => 'ExportStatistics', ], ], ], 'GetImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'JobId', ], ], ], 'GetImportJobResponse' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'ImportDestination' => [ 'shape' => 'ImportDestination', ], 'ImportDataSource' => [ 'shape' => 'ImportDataSource', ], 'FailureInfo' => [ 'shape' => 'FailureInfo', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], 'CompletedTimestamp' => [ 'shape' => 'Timestamp', ], 'ProcessedRecordsCount' => [ 'shape' => 'ProcessedRecordsCount', ], 'FailedRecordsCount' => [ 'shape' => 'FailedRecordsCount', ], ], ], 'GetMessageInsightsRequest' => [ 'type' => 'structure', 'required' => [ 'MessageId', ], 'members' => [ 'MessageId' => [ 'shape' => 'OutboundMessageId', 'location' => 'uri', 'locationName' => 'MessageId', ], ], ], 'GetMessageInsightsResponse' => [ 'type' => 'structure', 'members' => [ 'MessageId' => [ 'shape' => 'OutboundMessageId', ], 'FromEmailAddress' => [ 'shape' => 'InsightsEmailAddress', ], 'Subject' => [ 'shape' => 'EmailSubject', ], 'EmailTags' => [ 'shape' => 'MessageTagList', ], 'Insights' => [ 'shape' => 'EmailInsightsList', ], ], ], 'GetSuppressedDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'EmailAddress', ], 'members' => [ 'EmailAddress' => [ 'shape' => 'EmailAddress', 'location' => 'uri', 'locationName' => 'EmailAddress', ], ], ], 'GetSuppressedDestinationResponse' => [ 'type' => 'structure', 'required' => [ 'SuppressedDestination', ], 'members' => [ 'SuppressedDestination' => [ 'shape' => 'SuppressedDestination', ], ], ], 'GuardianAttributes' => [ 'type' => 'structure', 'members' => [ 'OptimizedSharedDelivery' => [ 'shape' => 'FeatureStatus', ], ], ], 'GuardianOptions' => [ 'type' => 'structure', 'members' => [ 'OptimizedSharedDelivery' => [ 'shape' => 'FeatureStatus', ], ], ], 'Identity' => [ 'type' => 'string', 'min' => 1, ], 'IdentityInfo' => [ 'type' => 'structure', 'members' => [ 'IdentityType' => [ 'shape' => 'IdentityType', ], 'IdentityName' => [ 'shape' => 'Identity', ], 'SendingEnabled' => [ 'shape' => 'Enabled', ], 'VerificationStatus' => [ 'shape' => 'VerificationStatus', ], ], ], 'IdentityInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdentityInfo', ], ], 'IdentityType' => [ 'type' => 'string', 'enum' => [ 'EMAIL_ADDRESS', 'DOMAIN', 'MANAGED_DOMAIN', ], ], 'ImageUrl' => [ 'type' => 'string', ], 'ImportDataSource' => [ 'type' => 'structure', 'required' => [ 'S3Url', 'DataFormat', ], 'members' => [ 'S3Url' => [ 'shape' => 'S3Url', ], 'DataFormat' => [ 'shape' => 'DataFormat', ], ], ], 'ImportDestination' => [ 'type' => 'structure', 'members' => [ 'SuppressionListDestination' => [ 'shape' => 'SuppressionListDestination', ], 'ContactListDestination' => [ 'shape' => 'ContactListDestination', ], ], ], 'ImportDestinationType' => [ 'type' => 'string', 'enum' => [ 'SUPPRESSION_LIST', 'CONTACT_LIST', ], ], 'ImportJobSummary' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'ImportDestination' => [ 'shape' => 'ImportDestination', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], 'ProcessedRecordsCount' => [ 'shape' => 'ProcessedRecordsCount', ], 'FailedRecordsCount' => [ 'shape' => 'FailedRecordsCount', ], ], ], 'ImportJobSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImportJobSummary', ], ], 'InboxPlacementTrackingOption' => [ 'type' => 'structure', 'members' => [ 'Global' => [ 'shape' => 'Enabled', ], 'TrackedIsps' => [ 'shape' => 'IspNameList', ], ], ], 'InsightsEmailAddress' => [ 'type' => 'string', 'max' => 320, 'min' => 1, 'sensitive' => true, ], 'InsightsEvent' => [ 'type' => 'structure', 'members' => [ 'Timestamp' => [ 'shape' => 'Timestamp', ], 'Type' => [ 'shape' => 'EventType', ], 'Details' => [ 'shape' => 'EventDetails', ], ], ], 'InsightsEvents' => [ 'type' => 'list', 'member' => [ 'shape' => 'InsightsEvent', ], ], 'InternalServiceErrorException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'InvalidNextTokenException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'Ip' => [ 'type' => 'string', ], 'IpList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Ip', ], ], 'Isp' => [ 'type' => 'string', ], 'IspFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Isp', ], 'max' => 5, ], 'IspName' => [ 'type' => 'string', ], 'IspNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IspName', ], ], 'IspPlacement' => [ 'type' => 'structure', 'members' => [ 'IspName' => [ 'shape' => 'IspName', ], 'PlacementStatistics' => [ 'shape' => 'PlacementStatistics', ], ], ], 'IspPlacements' => [ 'type' => 'list', 'member' => [ 'shape' => 'IspPlacement', ], ], 'JobId' => [ 'type' => 'string', 'min' => 1, ], 'JobStatus' => [ 'type' => 'string', 'enum' => [ 'CREATED', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED', ], ], 'KinesisFirehoseDestination' => [ 'type' => 'structure', 'required' => [ 'IamRoleArn', 'DeliveryStreamArn', ], 'members' => [ 'IamRoleArn' => [ 'shape' => 'AmazonResourceName', ], 'DeliveryStreamArn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'LastDeliveryEventList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeliveryEventType', ], 'max' => 5, ], 'LastEngagementEventList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EngagementEventType', ], 'max' => 2, ], 'LastFreshStart' => [ 'type' => 'timestamp', ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ListConfigurationSetsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'PageSize' => [ 'shape' => 'MaxItems', 'location' => 'querystring', 'locationName' => 'PageSize', ], ], ], 'ListConfigurationSetsResponse' => [ 'type' => 'structure', 'members' => [ 'ConfigurationSets' => [ 'shape' => 'ConfigurationSetNameList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListContactListsRequest' => [ 'type' => 'structure', 'members' => [ 'PageSize' => [ 'shape' => 'MaxItems', 'location' => 'querystring', 'locationName' => 'PageSize', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'ListContactListsResponse' => [ 'type' => 'structure', 'members' => [ 'ContactLists' => [ 'shape' => 'ListOfContactLists', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListContactsFilter' => [ 'type' => 'structure', 'members' => [ 'FilteredStatus' => [ 'shape' => 'SubscriptionStatus', ], 'TopicFilter' => [ 'shape' => 'TopicFilter', ], ], ], 'ListContactsRequest' => [ 'type' => 'structure', 'required' => [ 'ContactListName', ], 'members' => [ 'ContactListName' => [ 'shape' => 'ContactListName', 'location' => 'uri', 'locationName' => 'ContactListName', ], 'Filter' => [ 'shape' => 'ListContactsFilter', ], 'PageSize' => [ 'shape' => 'MaxItems', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListContactsResponse' => [ 'type' => 'structure', 'members' => [ 'Contacts' => [ 'shape' => 'ListOfContacts', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListCustomVerificationEmailTemplatesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'PageSize' => [ 'shape' => 'MaxItems', 'location' => 'querystring', 'locationName' => 'PageSize', ], ], ], 'ListCustomVerificationEmailTemplatesResponse' => [ 'type' => 'structure', 'members' => [ 'CustomVerificationEmailTemplates' => [ 'shape' => 'CustomVerificationEmailTemplatesList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDedicatedIpPoolsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'PageSize' => [ 'shape' => 'MaxItems', 'location' => 'querystring', 'locationName' => 'PageSize', ], ], ], 'ListDedicatedIpPoolsResponse' => [ 'type' => 'structure', 'members' => [ 'DedicatedIpPools' => [ 'shape' => 'ListOfDedicatedIpPools', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDeliverabilityTestReportsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'PageSize' => [ 'shape' => 'MaxItems', 'location' => 'querystring', 'locationName' => 'PageSize', ], ], ], 'ListDeliverabilityTestReportsResponse' => [ 'type' => 'structure', 'required' => [ 'DeliverabilityTestReports', ], 'members' => [ 'DeliverabilityTestReports' => [ 'shape' => 'DeliverabilityTestReports', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDomainDeliverabilityCampaignsRequest' => [ 'type' => 'structure', 'required' => [ 'StartDate', 'EndDate', 'SubscribedDomain', ], 'members' => [ 'StartDate' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'StartDate', ], 'EndDate' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'EndDate', ], 'SubscribedDomain' => [ 'shape' => 'Domain', 'location' => 'uri', 'locationName' => 'SubscribedDomain', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'PageSize' => [ 'shape' => 'MaxItems', 'location' => 'querystring', 'locationName' => 'PageSize', ], ], ], 'ListDomainDeliverabilityCampaignsResponse' => [ 'type' => 'structure', 'required' => [ 'DomainDeliverabilityCampaigns', ], 'members' => [ 'DomainDeliverabilityCampaigns' => [ 'shape' => 'DomainDeliverabilityCampaignList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListEmailIdentitiesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'PageSize' => [ 'shape' => 'MaxItems', 'location' => 'querystring', 'locationName' => 'PageSize', ], ], ], 'ListEmailIdentitiesResponse' => [ 'type' => 'structure', 'members' => [ 'EmailIdentities' => [ 'shape' => 'IdentityInfoList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListEmailTemplatesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'PageSize' => [ 'shape' => 'MaxItems', 'location' => 'querystring', 'locationName' => 'PageSize', ], ], ], 'ListEmailTemplatesResponse' => [ 'type' => 'structure', 'members' => [ 'TemplatesMetadata' => [ 'shape' => 'EmailTemplateMetadataList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListExportJobsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'PageSize' => [ 'shape' => 'MaxItems', ], 'ExportSourceType' => [ 'shape' => 'ExportSourceType', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], ], ], 'ListExportJobsResponse' => [ 'type' => 'structure', 'members' => [ 'ExportJobs' => [ 'shape' => 'ExportJobSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListImportJobsRequest' => [ 'type' => 'structure', 'members' => [ 'ImportDestinationType' => [ 'shape' => 'ImportDestinationType', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'PageSize' => [ 'shape' => 'MaxItems', ], ], ], 'ListImportJobsResponse' => [ 'type' => 'structure', 'members' => [ 'ImportJobs' => [ 'shape' => 'ImportJobSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListManagementOptions' => [ 'type' => 'structure', 'required' => [ 'ContactListName', ], 'members' => [ 'ContactListName' => [ 'shape' => 'ContactListName', ], 'TopicName' => [ 'shape' => 'TopicName', ], ], ], 'ListOfContactLists' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContactList', ], ], 'ListOfContacts' => [ 'type' => 'list', 'member' => [ 'shape' => 'Contact', ], ], 'ListOfDedicatedIpPools' => [ 'type' => 'list', 'member' => [ 'shape' => 'PoolName', ], ], 'ListRecommendationFilterValue' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'ListRecommendationsFilter' => [ 'type' => 'map', 'key' => [ 'shape' => 'ListRecommendationsFilterKey', ], 'value' => [ 'shape' => 'ListRecommendationFilterValue', ], 'max' => 2, 'min' => 1, ], 'ListRecommendationsFilterKey' => [ 'type' => 'string', 'enum' => [ 'TYPE', 'IMPACT', 'STATUS', 'RESOURCE_ARN', ], ], 'ListRecommendationsRequest' => [ 'type' => 'structure', 'members' => [ 'Filter' => [ 'shape' => 'ListRecommendationsFilter', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'PageSize' => [ 'shape' => 'MaxItems', ], ], ], 'ListRecommendationsResponse' => [ 'type' => 'structure', 'members' => [ 'Recommendations' => [ 'shape' => 'RecommendationsList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSuppressedDestinationsRequest' => [ 'type' => 'structure', 'members' => [ 'Reasons' => [ 'shape' => 'SuppressionListReasons', 'location' => 'querystring', 'locationName' => 'Reason', ], 'StartDate' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'StartDate', ], 'EndDate' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'EndDate', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'PageSize' => [ 'shape' => 'MaxItems', 'location' => 'querystring', 'locationName' => 'PageSize', ], ], ], 'ListSuppressedDestinationsResponse' => [ 'type' => 'structure', 'members' => [ 'SuppressedDestinationSummaries' => [ 'shape' => 'SuppressedDestinationSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'AmazonResourceName', 'location' => 'querystring', 'locationName' => 'ResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'required' => [ 'Tags', ], 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], ], ], 'MailFromAttributes' => [ 'type' => 'structure', 'required' => [ 'MailFromDomain', 'MailFromDomainStatus', 'BehaviorOnMxFailure', ], 'members' => [ 'MailFromDomain' => [ 'shape' => 'MailFromDomainName', ], 'MailFromDomainStatus' => [ 'shape' => 'MailFromDomainStatus', ], 'BehaviorOnMxFailure' => [ 'shape' => 'BehaviorOnMxFailure', ], ], ], 'MailFromDomainName' => [ 'type' => 'string', ], 'MailFromDomainNotVerifiedException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'MailFromDomainStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'SUCCESS', 'FAILED', 'TEMPORARY_FAILURE', ], ], 'MailType' => [ 'type' => 'string', 'enum' => [ 'MARKETING', 'TRANSACTIONAL', ], ], 'Max24HourSend' => [ 'type' => 'double', ], 'MaxItems' => [ 'type' => 'integer', ], 'MaxSendRate' => [ 'type' => 'double', ], 'Message' => [ 'type' => 'structure', 'required' => [ 'Subject', 'Body', ], 'members' => [ 'Subject' => [ 'shape' => 'Content', ], 'Body' => [ 'shape' => 'Body', ], 'Headers' => [ 'shape' => 'MessageHeaderList', ], ], ], 'MessageContent' => [ 'type' => 'string', ], 'MessageData' => [ 'type' => 'string', ], 'MessageHeader' => [ 'type' => 'structure', 'required' => [ 'Name', 'Value', ], 'members' => [ 'Name' => [ 'shape' => 'MessageHeaderName', ], 'Value' => [ 'shape' => 'MessageHeaderValue', ], ], ], 'MessageHeaderList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MessageHeader', ], 'max' => 15, 'min' => 0, ], 'MessageHeaderName' => [ 'type' => 'string', 'max' => 126, 'min' => 1, 'pattern' => '^[!-9;-@A-~]+$', ], 'MessageHeaderValue' => [ 'type' => 'string', 'max' => 870, 'min' => 1, 'pattern' => '[ -~]*', ], 'MessageInsightsDataSource' => [ 'type' => 'structure', 'required' => [ 'StartDate', 'EndDate', ], 'members' => [ 'StartDate' => [ 'shape' => 'Timestamp', ], 'EndDate' => [ 'shape' => 'Timestamp', ], 'Include' => [ 'shape' => 'MessageInsightsFilters', ], 'Exclude' => [ 'shape' => 'MessageInsightsFilters', ], 'MaxResults' => [ 'shape' => 'MessageInsightsExportMaxResults', ], ], ], 'MessageInsightsExportMaxResults' => [ 'type' => 'integer', 'max' => 10000, 'min' => 1, ], 'MessageInsightsFilters' => [ 'type' => 'structure', 'members' => [ 'FromEmailAddress' => [ 'shape' => 'EmailAddressFilterList', ], 'Destination' => [ 'shape' => 'EmailAddressFilterList', ], 'Subject' => [ 'shape' => 'EmailSubjectFilterList', ], 'Isp' => [ 'shape' => 'IspFilterList', ], 'LastDeliveryEvent' => [ 'shape' => 'LastDeliveryEventList', ], 'LastEngagementEvent' => [ 'shape' => 'LastEngagementEventList', ], ], ], 'MessageRejected' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'MessageTag' => [ 'type' => 'structure', 'required' => [ 'Name', 'Value', ], 'members' => [ 'Name' => [ 'shape' => 'MessageTagName', ], 'Value' => [ 'shape' => 'MessageTagValue', ], ], ], 'MessageTagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MessageTag', ], ], 'MessageTagName' => [ 'type' => 'string', ], 'MessageTagValue' => [ 'type' => 'string', ], 'Metric' => [ 'type' => 'string', 'enum' => [ 'SEND', 'COMPLAINT', 'PERMANENT_BOUNCE', 'TRANSIENT_BOUNCE', 'OPEN', 'CLICK', 'DELIVERY', 'DELIVERY_OPEN', 'DELIVERY_CLICK', 'DELIVERY_COMPLAINT', ], ], 'MetricAggregation' => [ 'type' => 'string', 'enum' => [ 'RATE', 'VOLUME', ], ], 'MetricDataError' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'QueryIdentifier', ], 'Code' => [ 'shape' => 'QueryErrorCode', ], 'Message' => [ 'shape' => 'QueryErrorMessage', ], ], ], 'MetricDataErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricDataError', ], ], 'MetricDataResult' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'QueryIdentifier', ], 'Timestamps' => [ 'shape' => 'TimestampList', ], 'Values' => [ 'shape' => 'MetricValueList', ], ], ], 'MetricDataResultList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricDataResult', ], ], 'MetricDimensionName' => [ 'type' => 'string', 'enum' => [ 'EMAIL_IDENTITY', 'CONFIGURATION_SET', 'ISP', ], ], 'MetricDimensionValue' => [ 'type' => 'string', ], 'MetricNamespace' => [ 'type' => 'string', 'enum' => [ 'VDM', ], ], 'MetricValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Counter', ], ], 'MetricsDataSource' => [ 'type' => 'structure', 'required' => [ 'Dimensions', 'Namespace', 'Metrics', 'StartDate', 'EndDate', ], 'members' => [ 'Dimensions' => [ 'shape' => 'ExportDimensions', ], 'Namespace' => [ 'shape' => 'MetricNamespace', ], 'Metrics' => [ 'shape' => 'ExportMetrics', ], 'StartDate' => [ 'shape' => 'Timestamp', ], 'EndDate' => [ 'shape' => 'Timestamp', ], ], ], 'NextToken' => [ 'type' => 'string', ], 'NotFoundException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'OutboundMessageId' => [ 'type' => 'string', ], 'OverallVolume' => [ 'type' => 'structure', 'members' => [ 'VolumeStatistics' => [ 'shape' => 'VolumeStatistics', ], 'ReadRatePercent' => [ 'shape' => 'Percentage', ], 'DomainIspPlacements' => [ 'shape' => 'DomainIspPlacements', ], ], ], 'Percentage' => [ 'type' => 'double', ], 'Percentage100Wrapper' => [ 'type' => 'integer', ], 'PinpointDestination' => [ 'type' => 'structure', 'members' => [ 'ApplicationArn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'PlacementStatistics' => [ 'type' => 'structure', 'members' => [ 'InboxPercentage' => [ 'shape' => 'Percentage', ], 'SpamPercentage' => [ 'shape' => 'Percentage', ], 'MissingPercentage' => [ 'shape' => 'Percentage', ], 'SpfPercentage' => [ 'shape' => 'Percentage', ], 'DkimPercentage' => [ 'shape' => 'Percentage', ], ], ], 'Policy' => [ 'type' => 'string', 'min' => 1, ], 'PolicyMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'PolicyName', ], 'value' => [ 'shape' => 'Policy', ], ], 'PolicyName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'PoolName' => [ 'type' => 'string', ], 'PrimaryNameServer' => [ 'type' => 'string', ], 'PrivateKey' => [ 'type' => 'string', 'max' => 20480, 'min' => 1, 'pattern' => '^[a-zA-Z0-9+\\/]+={0,2}$', 'sensitive' => true, ], 'ProcessedRecordsCount' => [ 'type' => 'integer', ], 'PutAccountDedicatedIpWarmupAttributesRequest' => [ 'type' => 'structure', 'members' => [ 'AutoWarmupEnabled' => [ 'shape' => 'Enabled', ], ], ], 'PutAccountDedicatedIpWarmupAttributesResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutAccountDetailsRequest' => [ 'type' => 'structure', 'required' => [ 'MailType', 'WebsiteURL', ], 'members' => [ 'MailType' => [ 'shape' => 'MailType', ], 'WebsiteURL' => [ 'shape' => 'WebsiteURL', ], 'ContactLanguage' => [ 'shape' => 'ContactLanguage', ], 'UseCaseDescription' => [ 'shape' => 'UseCaseDescription', ], 'AdditionalContactEmailAddresses' => [ 'shape' => 'AdditionalContactEmailAddresses', ], 'ProductionAccessEnabled' => [ 'shape' => 'EnabledWrapper', ], ], ], 'PutAccountDetailsResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutAccountSendingAttributesRequest' => [ 'type' => 'structure', 'members' => [ 'SendingEnabled' => [ 'shape' => 'Enabled', ], ], ], 'PutAccountSendingAttributesResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutAccountSuppressionAttributesRequest' => [ 'type' => 'structure', 'members' => [ 'SuppressedReasons' => [ 'shape' => 'SuppressionListReasons', ], ], ], 'PutAccountSuppressionAttributesResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutAccountVdmAttributesRequest' => [ 'type' => 'structure', 'required' => [ 'VdmAttributes', ], 'members' => [ 'VdmAttributes' => [ 'shape' => 'VdmAttributes', ], ], ], 'PutAccountVdmAttributesResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutConfigurationSetDeliveryOptionsRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationSetName', ], 'members' => [ 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetName', 'location' => 'uri', 'locationName' => 'ConfigurationSetName', ], 'TlsPolicy' => [ 'shape' => 'TlsPolicy', ], 'SendingPoolName' => [ 'shape' => 'SendingPoolName', ], ], ], 'PutConfigurationSetDeliveryOptionsResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutConfigurationSetReputationOptionsRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationSetName', ], 'members' => [ 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetName', 'location' => 'uri', 'locationName' => 'ConfigurationSetName', ], 'ReputationMetricsEnabled' => [ 'shape' => 'Enabled', ], ], ], 'PutConfigurationSetReputationOptionsResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutConfigurationSetSendingOptionsRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationSetName', ], 'members' => [ 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetName', 'location' => 'uri', 'locationName' => 'ConfigurationSetName', ], 'SendingEnabled' => [ 'shape' => 'Enabled', ], ], ], 'PutConfigurationSetSendingOptionsResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutConfigurationSetSuppressionOptionsRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationSetName', ], 'members' => [ 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetName', 'location' => 'uri', 'locationName' => 'ConfigurationSetName', ], 'SuppressedReasons' => [ 'shape' => 'SuppressionListReasons', ], ], ], 'PutConfigurationSetSuppressionOptionsResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutConfigurationSetTrackingOptionsRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationSetName', ], 'members' => [ 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetName', 'location' => 'uri', 'locationName' => 'ConfigurationSetName', ], 'CustomRedirectDomain' => [ 'shape' => 'CustomRedirectDomain', ], ], ], 'PutConfigurationSetTrackingOptionsResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutConfigurationSetVdmOptionsRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationSetName', ], 'members' => [ 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetName', 'location' => 'uri', 'locationName' => 'ConfigurationSetName', ], 'VdmOptions' => [ 'shape' => 'VdmOptions', ], ], ], 'PutConfigurationSetVdmOptionsResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutDedicatedIpInPoolRequest' => [ 'type' => 'structure', 'required' => [ 'Ip', 'DestinationPoolName', ], 'members' => [ 'Ip' => [ 'shape' => 'Ip', 'location' => 'uri', 'locationName' => 'IP', ], 'DestinationPoolName' => [ 'shape' => 'PoolName', ], ], ], 'PutDedicatedIpInPoolResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutDedicatedIpPoolScalingAttributesRequest' => [ 'type' => 'structure', 'required' => [ 'PoolName', 'ScalingMode', ], 'members' => [ 'PoolName' => [ 'shape' => 'PoolName', 'location' => 'uri', 'locationName' => 'PoolName', ], 'ScalingMode' => [ 'shape' => 'ScalingMode', ], ], ], 'PutDedicatedIpPoolScalingAttributesResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutDedicatedIpWarmupAttributesRequest' => [ 'type' => 'structure', 'required' => [ 'Ip', 'WarmupPercentage', ], 'members' => [ 'Ip' => [ 'shape' => 'Ip', 'location' => 'uri', 'locationName' => 'IP', ], 'WarmupPercentage' => [ 'shape' => 'Percentage100Wrapper', ], ], ], 'PutDedicatedIpWarmupAttributesResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutDeliverabilityDashboardOptionRequest' => [ 'type' => 'structure', 'required' => [ 'DashboardEnabled', ], 'members' => [ 'DashboardEnabled' => [ 'shape' => 'Enabled', ], 'SubscribedDomains' => [ 'shape' => 'DomainDeliverabilityTrackingOptions', ], ], ], 'PutDeliverabilityDashboardOptionResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutEmailIdentityConfigurationSetAttributesRequest' => [ 'type' => 'structure', 'required' => [ 'EmailIdentity', ], 'members' => [ 'EmailIdentity' => [ 'shape' => 'Identity', 'location' => 'uri', 'locationName' => 'EmailIdentity', ], 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetName', ], ], ], 'PutEmailIdentityConfigurationSetAttributesResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutEmailIdentityDkimAttributesRequest' => [ 'type' => 'structure', 'required' => [ 'EmailIdentity', ], 'members' => [ 'EmailIdentity' => [ 'shape' => 'Identity', 'location' => 'uri', 'locationName' => 'EmailIdentity', ], 'SigningEnabled' => [ 'shape' => 'Enabled', ], ], ], 'PutEmailIdentityDkimAttributesResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutEmailIdentityDkimSigningAttributesRequest' => [ 'type' => 'structure', 'required' => [ 'EmailIdentity', 'SigningAttributesOrigin', ], 'members' => [ 'EmailIdentity' => [ 'shape' => 'Identity', 'location' => 'uri', 'locationName' => 'EmailIdentity', ], 'SigningAttributesOrigin' => [ 'shape' => 'DkimSigningAttributesOrigin', ], 'SigningAttributes' => [ 'shape' => 'DkimSigningAttributes', ], ], ], 'PutEmailIdentityDkimSigningAttributesResponse' => [ 'type' => 'structure', 'members' => [ 'DkimStatus' => [ 'shape' => 'DkimStatus', ], 'DkimTokens' => [ 'shape' => 'DnsTokenList', ], ], ], 'PutEmailIdentityFeedbackAttributesRequest' => [ 'type' => 'structure', 'required' => [ 'EmailIdentity', ], 'members' => [ 'EmailIdentity' => [ 'shape' => 'Identity', 'location' => 'uri', 'locationName' => 'EmailIdentity', ], 'EmailForwardingEnabled' => [ 'shape' => 'Enabled', ], ], ], 'PutEmailIdentityFeedbackAttributesResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutEmailIdentityMailFromAttributesRequest' => [ 'type' => 'structure', 'required' => [ 'EmailIdentity', ], 'members' => [ 'EmailIdentity' => [ 'shape' => 'Identity', 'location' => 'uri', 'locationName' => 'EmailIdentity', ], 'MailFromDomain' => [ 'shape' => 'MailFromDomainName', ], 'BehaviorOnMxFailure' => [ 'shape' => 'BehaviorOnMxFailure', ], ], ], 'PutEmailIdentityMailFromAttributesResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutSuppressedDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'EmailAddress', 'Reason', ], 'members' => [ 'EmailAddress' => [ 'shape' => 'EmailAddress', ], 'Reason' => [ 'shape' => 'SuppressionListReason', ], ], ], 'PutSuppressedDestinationResponse' => [ 'type' => 'structure', 'members' => [], ], 'QueryErrorCode' => [ 'type' => 'string', 'enum' => [ 'INTERNAL_FAILURE', 'ACCESS_DENIED', ], ], 'QueryErrorMessage' => [ 'type' => 'string', ], 'QueryIdentifier' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'RawMessage' => [ 'type' => 'structure', 'required' => [ 'Data', ], 'members' => [ 'Data' => [ 'shape' => 'RawMessageData', ], ], ], 'RawMessageData' => [ 'type' => 'blob', ], 'RblName' => [ 'type' => 'string', ], 'Recommendation' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => 'AmazonResourceName', ], 'Type' => [ 'shape' => 'RecommendationType', ], 'Description' => [ 'shape' => 'RecommendationDescription', ], 'Status' => [ 'shape' => 'RecommendationStatus', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTimestamp' => [ 'shape' => 'Timestamp', ], 'Impact' => [ 'shape' => 'RecommendationImpact', ], ], ], 'RecommendationDescription' => [ 'type' => 'string', ], 'RecommendationImpact' => [ 'type' => 'string', 'enum' => [ 'LOW', 'HIGH', ], ], 'RecommendationStatus' => [ 'type' => 'string', 'enum' => [ 'OPEN', 'FIXED', ], ], 'RecommendationType' => [ 'type' => 'string', 'enum' => [ 'DKIM', 'DMARC', 'SPF', 'BIMI', ], ], 'RecommendationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Recommendation', ], ], 'RenderedEmailTemplate' => [ 'type' => 'string', ], 'ReplacementEmailContent' => [ 'type' => 'structure', 'members' => [ 'ReplacementTemplate' => [ 'shape' => 'ReplacementTemplate', ], ], ], 'ReplacementTemplate' => [ 'type' => 'structure', 'members' => [ 'ReplacementTemplateData' => [ 'shape' => 'EmailTemplateData', ], ], ], 'ReportId' => [ 'type' => 'string', ], 'ReportName' => [ 'type' => 'string', ], 'ReputationOptions' => [ 'type' => 'structure', 'members' => [ 'ReputationMetricsEnabled' => [ 'shape' => 'Enabled', ], 'LastFreshStart' => [ 'shape' => 'LastFreshStart', ], ], ], 'ReviewDetails' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'ReviewStatus', ], 'CaseId' => [ 'shape' => 'CaseId', ], ], ], 'ReviewStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'FAILED', 'GRANTED', 'DENIED', ], ], 'S3Url' => [ 'type' => 'string', 'pattern' => '^s3:\\/\\/([^\\/]+)\\/(.*?([^\\/]+)\\/?)$', ], 'SOARecord' => [ 'type' => 'structure', 'members' => [ 'PrimaryNameServer' => [ 'shape' => 'PrimaryNameServer', ], 'AdminEmail' => [ 'shape' => 'AdminEmail', ], 'SerialNumber' => [ 'shape' => 'SerialNumber', ], ], ], 'ScalingMode' => [ 'type' => 'string', 'enum' => [ 'STANDARD', 'MANAGED', ], ], 'Selector' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\\-]*[a-zA-Z0-9]))$', ], 'SendBulkEmailRequest' => [ 'type' => 'structure', 'required' => [ 'DefaultContent', 'BulkEmailEntries', ], 'members' => [ 'FromEmailAddress' => [ 'shape' => 'EmailAddress', ], 'FromEmailAddressIdentityArn' => [ 'shape' => 'AmazonResourceName', ], 'ReplyToAddresses' => [ 'shape' => 'EmailAddressList', ], 'FeedbackForwardingEmailAddress' => [ 'shape' => 'EmailAddress', ], 'FeedbackForwardingEmailAddressIdentityArn' => [ 'shape' => 'AmazonResourceName', ], 'DefaultEmailTags' => [ 'shape' => 'MessageTagList', ], 'DefaultContent' => [ 'shape' => 'BulkEmailContent', ], 'BulkEmailEntries' => [ 'shape' => 'BulkEmailEntryList', ], 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetName', ], ], ], 'SendBulkEmailResponse' => [ 'type' => 'structure', 'required' => [ 'BulkEmailEntryResults', ], 'members' => [ 'BulkEmailEntryResults' => [ 'shape' => 'BulkEmailEntryResultList', ], ], ], 'SendCustomVerificationEmailRequest' => [ 'type' => 'structure', 'required' => [ 'EmailAddress', 'TemplateName', ], 'members' => [ 'EmailAddress' => [ 'shape' => 'EmailAddress', ], 'TemplateName' => [ 'shape' => 'EmailTemplateName', ], 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetName', ], ], ], 'SendCustomVerificationEmailResponse' => [ 'type' => 'structure', 'members' => [ 'MessageId' => [ 'shape' => 'OutboundMessageId', ], ], ], 'SendEmailRequest' => [ 'type' => 'structure', 'required' => [ 'Content', ], 'members' => [ 'FromEmailAddress' => [ 'shape' => 'EmailAddress', ], 'FromEmailAddressIdentityArn' => [ 'shape' => 'AmazonResourceName', ], 'Destination' => [ 'shape' => 'Destination', ], 'ReplyToAddresses' => [ 'shape' => 'EmailAddressList', ], 'FeedbackForwardingEmailAddress' => [ 'shape' => 'EmailAddress', ], 'FeedbackForwardingEmailAddressIdentityArn' => [ 'shape' => 'AmazonResourceName', ], 'Content' => [ 'shape' => 'EmailContent', ], 'EmailTags' => [ 'shape' => 'MessageTagList', ], 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetName', ], 'ListManagementOptions' => [ 'shape' => 'ListManagementOptions', ], ], ], 'SendEmailResponse' => [ 'type' => 'structure', 'members' => [ 'MessageId' => [ 'shape' => 'OutboundMessageId', ], ], ], 'SendQuota' => [ 'type' => 'structure', 'members' => [ 'Max24HourSend' => [ 'shape' => 'Max24HourSend', ], 'MaxSendRate' => [ 'shape' => 'MaxSendRate', ], 'SentLast24Hours' => [ 'shape' => 'SentLast24Hours', ], ], ], 'SendingOptions' => [ 'type' => 'structure', 'members' => [ 'SendingEnabled' => [ 'shape' => 'Enabled', ], ], ], 'SendingPausedException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'SendingPoolName' => [ 'type' => 'string', ], 'SentLast24Hours' => [ 'type' => 'double', ], 'SerialNumber' => [ 'type' => 'long', ], 'SnsDestination' => [ 'type' => 'structure', 'required' => [ 'TopicArn', ], 'members' => [ 'TopicArn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'Subject' => [ 'type' => 'string', ], 'SubscriptionStatus' => [ 'type' => 'string', 'enum' => [ 'OPT_IN', 'OPT_OUT', ], ], 'SuccessRedirectionURL' => [ 'type' => 'string', ], 'SuppressedDestination' => [ 'type' => 'structure', 'required' => [ 'EmailAddress', 'Reason', 'LastUpdateTime', ], 'members' => [ 'EmailAddress' => [ 'shape' => 'EmailAddress', ], 'Reason' => [ 'shape' => 'SuppressionListReason', ], 'LastUpdateTime' => [ 'shape' => 'Timestamp', ], 'Attributes' => [ 'shape' => 'SuppressedDestinationAttributes', ], ], ], 'SuppressedDestinationAttributes' => [ 'type' => 'structure', 'members' => [ 'MessageId' => [ 'shape' => 'OutboundMessageId', ], 'FeedbackId' => [ 'shape' => 'FeedbackId', ], ], ], 'SuppressedDestinationSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'SuppressedDestinationSummary', ], ], 'SuppressedDestinationSummary' => [ 'type' => 'structure', 'required' => [ 'EmailAddress', 'Reason', 'LastUpdateTime', ], 'members' => [ 'EmailAddress' => [ 'shape' => 'EmailAddress', ], 'Reason' => [ 'shape' => 'SuppressionListReason', ], 'LastUpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'SuppressionAttributes' => [ 'type' => 'structure', 'members' => [ 'SuppressedReasons' => [ 'shape' => 'SuppressionListReasons', ], ], ], 'SuppressionListDestination' => [ 'type' => 'structure', 'required' => [ 'SuppressionListImportAction', ], 'members' => [ 'SuppressionListImportAction' => [ 'shape' => 'SuppressionListImportAction', ], ], ], 'SuppressionListImportAction' => [ 'type' => 'string', 'enum' => [ 'DELETE', 'PUT', ], ], 'SuppressionListReason' => [ 'type' => 'string', 'enum' => [ 'BOUNCE', 'COMPLAINT', ], ], 'SuppressionListReasons' => [ 'type' => 'list', 'member' => [ 'shape' => 'SuppressionListReason', ], ], 'SuppressionOptions' => [ 'type' => 'structure', 'members' => [ 'SuppressedReasons' => [ 'shape' => 'SuppressionListReasons', ], ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'AmazonResourceName', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', ], 'Template' => [ 'type' => 'structure', 'members' => [ 'TemplateName' => [ 'shape' => 'EmailTemplateName', ], 'TemplateArn' => [ 'shape' => 'AmazonResourceName', ], 'TemplateData' => [ 'shape' => 'EmailTemplateData', ], 'Headers' => [ 'shape' => 'MessageHeaderList', ], ], ], 'TemplateContent' => [ 'type' => 'string', ], 'TestRenderEmailTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'TemplateName', 'TemplateData', ], 'members' => [ 'TemplateName' => [ 'shape' => 'EmailTemplateName', 'location' => 'uri', 'locationName' => 'TemplateName', ], 'TemplateData' => [ 'shape' => 'EmailTemplateData', ], ], ], 'TestRenderEmailTemplateResponse' => [ 'type' => 'structure', 'required' => [ 'RenderedTemplate', ], 'members' => [ 'RenderedTemplate' => [ 'shape' => 'RenderedEmailTemplate', ], ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TimestampList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Timestamp', ], ], 'TlsPolicy' => [ 'type' => 'string', 'enum' => [ 'REQUIRE', 'OPTIONAL', ], ], 'TooManyRequestsException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'Topic' => [ 'type' => 'structure', 'required' => [ 'TopicName', 'DisplayName', 'DefaultSubscriptionStatus', ], 'members' => [ 'TopicName' => [ 'shape' => 'TopicName', ], 'DisplayName' => [ 'shape' => 'DisplayName', ], 'Description' => [ 'shape' => 'Description', ], 'DefaultSubscriptionStatus' => [ 'shape' => 'SubscriptionStatus', ], ], ], 'TopicFilter' => [ 'type' => 'structure', 'members' => [ 'TopicName' => [ 'shape' => 'TopicName', ], 'UseDefaultIfPreferenceUnavailable' => [ 'shape' => 'UseDefaultIfPreferenceUnavailable', ], ], ], 'TopicName' => [ 'type' => 'string', ], 'TopicPreference' => [ 'type' => 'structure', 'required' => [ 'TopicName', 'SubscriptionStatus', ], 'members' => [ 'TopicName' => [ 'shape' => 'TopicName', ], 'SubscriptionStatus' => [ 'shape' => 'SubscriptionStatus', ], ], ], 'TopicPreferenceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TopicPreference', ], ], 'Topics' => [ 'type' => 'list', 'member' => [ 'shape' => 'Topic', ], ], 'TrackingOptions' => [ 'type' => 'structure', 'required' => [ 'CustomRedirectDomain', ], 'members' => [ 'CustomRedirectDomain' => [ 'shape' => 'CustomRedirectDomain', ], ], ], 'UnsubscribeAll' => [ 'type' => 'boolean', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'AmazonResourceName', 'location' => 'querystring', 'locationName' => 'ResourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'TagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateConfigurationSetEventDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationSetName', 'EventDestinationName', 'EventDestination', ], 'members' => [ 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetName', 'location' => 'uri', 'locationName' => 'ConfigurationSetName', ], 'EventDestinationName' => [ 'shape' => 'EventDestinationName', 'location' => 'uri', 'locationName' => 'EventDestinationName', ], 'EventDestination' => [ 'shape' => 'EventDestinationDefinition', ], ], ], 'UpdateConfigurationSetEventDestinationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateContactListRequest' => [ 'type' => 'structure', 'required' => [ 'ContactListName', ], 'members' => [ 'ContactListName' => [ 'shape' => 'ContactListName', 'location' => 'uri', 'locationName' => 'ContactListName', ], 'Topics' => [ 'shape' => 'Topics', ], 'Description' => [ 'shape' => 'Description', ], ], ], 'UpdateContactListResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateContactRequest' => [ 'type' => 'structure', 'required' => [ 'ContactListName', 'EmailAddress', ], 'members' => [ 'ContactListName' => [ 'shape' => 'ContactListName', 'location' => 'uri', 'locationName' => 'ContactListName', ], 'EmailAddress' => [ 'shape' => 'EmailAddress', 'location' => 'uri', 'locationName' => 'EmailAddress', ], 'TopicPreferences' => [ 'shape' => 'TopicPreferenceList', ], 'UnsubscribeAll' => [ 'shape' => 'UnsubscribeAll', ], 'AttributesData' => [ 'shape' => 'AttributesData', ], ], ], 'UpdateContactResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateCustomVerificationEmailTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'TemplateName', 'FromEmailAddress', 'TemplateSubject', 'TemplateContent', 'SuccessRedirectionURL', 'FailureRedirectionURL', ], 'members' => [ 'TemplateName' => [ 'shape' => 'EmailTemplateName', 'location' => 'uri', 'locationName' => 'TemplateName', ], 'FromEmailAddress' => [ 'shape' => 'EmailAddress', ], 'TemplateSubject' => [ 'shape' => 'EmailTemplateSubject', ], 'TemplateContent' => [ 'shape' => 'TemplateContent', ], 'SuccessRedirectionURL' => [ 'shape' => 'SuccessRedirectionURL', ], 'FailureRedirectionURL' => [ 'shape' => 'FailureRedirectionURL', ], ], ], 'UpdateCustomVerificationEmailTemplateResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateEmailIdentityPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'EmailIdentity', 'PolicyName', 'Policy', ], 'members' => [ 'EmailIdentity' => [ 'shape' => 'Identity', 'location' => 'uri', 'locationName' => 'EmailIdentity', ], 'PolicyName' => [ 'shape' => 'PolicyName', 'location' => 'uri', 'locationName' => 'PolicyName', ], 'Policy' => [ 'shape' => 'Policy', ], ], ], 'UpdateEmailIdentityPolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateEmailTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'TemplateName', 'TemplateContent', ], 'members' => [ 'TemplateName' => [ 'shape' => 'EmailTemplateName', 'location' => 'uri', 'locationName' => 'TemplateName', ], 'TemplateContent' => [ 'shape' => 'EmailTemplateContent', ], ], ], 'UpdateEmailTemplateResponse' => [ 'type' => 'structure', 'members' => [], ], 'UseCaseDescription' => [ 'type' => 'string', 'deprecated' => true, 'deprecatedMessage' => 'Use case description is optional and deprecated', 'max' => 5000, 'sensitive' => true, ], 'UseDefaultIfPreferenceUnavailable' => [ 'type' => 'boolean', ], 'VdmAttributes' => [ 'type' => 'structure', 'required' => [ 'VdmEnabled', ], 'members' => [ 'VdmEnabled' => [ 'shape' => 'FeatureStatus', ], 'DashboardAttributes' => [ 'shape' => 'DashboardAttributes', ], 'GuardianAttributes' => [ 'shape' => 'GuardianAttributes', ], ], ], 'VdmOptions' => [ 'type' => 'structure', 'members' => [ 'DashboardOptions' => [ 'shape' => 'DashboardOptions', ], 'GuardianOptions' => [ 'shape' => 'GuardianOptions', ], ], ], 'VerificationError' => [ 'type' => 'string', 'enum' => [ 'SERVICE_ERROR', 'DNS_SERVER_ERROR', 'HOST_NOT_FOUND', 'TYPE_NOT_FOUND', 'INVALID_VALUE', ], ], 'VerificationInfo' => [ 'type' => 'structure', 'members' => [ 'LastCheckedTimestamp' => [ 'shape' => 'Timestamp', ], 'LastSuccessTimestamp' => [ 'shape' => 'Timestamp', ], 'ErrorType' => [ 'shape' => 'VerificationError', ], 'SOARecord' => [ 'shape' => 'SOARecord', ], ], ], 'VerificationStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'SUCCESS', 'FAILED', 'TEMPORARY_FAILURE', 'NOT_STARTED', ], ], 'Volume' => [ 'type' => 'long', ], 'VolumeStatistics' => [ 'type' => 'structure', 'members' => [ 'InboxRawCount' => [ 'shape' => 'Volume', ], 'SpamRawCount' => [ 'shape' => 'Volume', ], 'ProjectedInbox' => [ 'shape' => 'Volume', ], 'ProjectedSpam' => [ 'shape' => 'Volume', ], ], ], 'WarmupStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'DONE', ], ], 'WebsiteURL' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '^(([^:/?#]+):)?(//([^/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?', 'sensitive' => true, ], ],];
