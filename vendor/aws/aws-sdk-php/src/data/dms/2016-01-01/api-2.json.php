<?php
// This file was auto-generated from sdk-root/src/data/dms/2016-01-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2016-01-01', 'endpointPrefix' => 'dms', 'jsonVersion' => '1.1', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceFullName' => 'AWS Database Migration Service', 'serviceId' => 'Database Migration Service', 'signatureVersion' => 'v4', 'targetPrefix' => 'AmazonDMSv20160101', 'uid' => 'dms-2016-01-01', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'AddTagsToResource' => [ 'name' => 'AddTagsToResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddTagsToResourceMessage', ], 'output' => [ 'shape' => 'AddTagsToResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'ApplyPendingMaintenanceAction' => [ 'name' => 'ApplyPendingMaintenanceAction', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ApplyPendingMaintenanceActionMessage', ], 'output' => [ 'shape' => 'ApplyPendingMaintenanceActionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'BatchStartRecommendations' => [ 'name' => 'BatchStartRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchStartRecommendationsRequest', ], 'output' => [ 'shape' => 'BatchStartRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'CancelReplicationTaskAssessmentRun' => [ 'name' => 'CancelReplicationTaskAssessmentRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CancelReplicationTaskAssessmentRunMessage', ], 'output' => [ 'shape' => 'CancelReplicationTaskAssessmentRunResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'CreateDataProvider' => [ 'name' => 'CreateDataProvider', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDataProviderMessage', ], 'output' => [ 'shape' => 'CreateDataProviderResponse', ], 'errors' => [ [ 'shape' => 'ResourceQuotaExceededFault', ], [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'ResourceAlreadyExistsFault', ], ], ], 'CreateEndpoint' => [ 'name' => 'CreateEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateEndpointMessage', ], 'output' => [ 'shape' => 'CreateEndpointResponse', ], 'errors' => [ [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'ResourceAlreadyExistsFault', ], [ 'shape' => 'ResourceQuotaExceededFault', ], [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'S3AccessDeniedFault', ], ], ], 'CreateEventSubscription' => [ 'name' => 'CreateEventSubscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateEventSubscriptionMessage', ], 'output' => [ 'shape' => 'CreateEventSubscriptionResponse', ], 'errors' => [ [ 'shape' => 'ResourceQuotaExceededFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'ResourceAlreadyExistsFault', ], [ 'shape' => 'SNSInvalidTopicFault', ], [ 'shape' => 'SNSNoAuthorizationFault', ], [ 'shape' => 'KMSAccessDeniedFault', ], [ 'shape' => 'KMSDisabledFault', ], [ 'shape' => 'KMSInvalidStateFault', ], [ 'shape' => 'KMSNotFoundFault', ], [ 'shape' => 'KMSThrottlingFault', ], ], ], 'CreateFleetAdvisorCollector' => [ 'name' => 'CreateFleetAdvisorCollector', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateFleetAdvisorCollectorRequest', ], 'output' => [ 'shape' => 'CreateFleetAdvisorCollectorResponse', ], 'errors' => [ [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'S3AccessDeniedFault', ], [ 'shape' => 'S3ResourceNotFoundFault', ], [ 'shape' => 'ResourceQuotaExceededFault', ], ], ], 'CreateInstanceProfile' => [ 'name' => 'CreateInstanceProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateInstanceProfileMessage', ], 'output' => [ 'shape' => 'CreateInstanceProfileResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'ResourceAlreadyExistsFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'ResourceQuotaExceededFault', ], [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'S3ResourceNotFoundFault', ], [ 'shape' => 'S3AccessDeniedFault', ], ], ], 'CreateMigrationProject' => [ 'name' => 'CreateMigrationProject', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateMigrationProjectMessage', ], 'output' => [ 'shape' => 'CreateMigrationProjectResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'ResourceAlreadyExistsFault', ], [ 'shape' => 'ResourceQuotaExceededFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'S3ResourceNotFoundFault', ], [ 'shape' => 'S3AccessDeniedFault', ], ], ], 'CreateReplicationConfig' => [ 'name' => 'CreateReplicationConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateReplicationConfigMessage', ], 'output' => [ 'shape' => 'CreateReplicationConfigResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'ResourceAlreadyExistsFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'ReplicationSubnetGroupDoesNotCoverEnoughAZs', ], [ 'shape' => 'InvalidSubnet', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'ResourceQuotaExceededFault', ], ], ], 'CreateReplicationInstance' => [ 'name' => 'CreateReplicationInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateReplicationInstanceMessage', ], 'output' => [ 'shape' => 'CreateReplicationInstanceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'ResourceAlreadyExistsFault', ], [ 'shape' => 'InsufficientResourceCapacityFault', ], [ 'shape' => 'ResourceQuotaExceededFault', ], [ 'shape' => 'StorageQuotaExceededFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'ReplicationSubnetGroupDoesNotCoverEnoughAZs', ], [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'InvalidSubnet', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], ], ], 'CreateReplicationSubnetGroup' => [ 'name' => 'CreateReplicationSubnetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateReplicationSubnetGroupMessage', ], 'output' => [ 'shape' => 'CreateReplicationSubnetGroupResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'ResourceAlreadyExistsFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'ResourceQuotaExceededFault', ], [ 'shape' => 'ReplicationSubnetGroupDoesNotCoverEnoughAZs', ], [ 'shape' => 'InvalidSubnet', ], ], ], 'CreateReplicationTask' => [ 'name' => 'CreateReplicationTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateReplicationTaskMessage', ], 'output' => [ 'shape' => 'CreateReplicationTaskResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'ResourceAlreadyExistsFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'ResourceQuotaExceededFault', ], ], ], 'DeleteCertificate' => [ 'name' => 'DeleteCertificate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteCertificateMessage', ], 'output' => [ 'shape' => 'DeleteCertificateResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'DeleteConnection' => [ 'name' => 'DeleteConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteConnectionMessage', ], 'output' => [ 'shape' => 'DeleteConnectionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'DeleteDataProvider' => [ 'name' => 'DeleteDataProvider', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDataProviderMessage', ], 'output' => [ 'shape' => 'DeleteDataProviderResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'DeleteEndpoint' => [ 'name' => 'DeleteEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteEndpointMessage', ], 'output' => [ 'shape' => 'DeleteEndpointResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'DeleteEventSubscription' => [ 'name' => 'DeleteEventSubscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteEventSubscriptionMessage', ], 'output' => [ 'shape' => 'DeleteEventSubscriptionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'DeleteFleetAdvisorCollector' => [ 'name' => 'DeleteFleetAdvisorCollector', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteCollectorRequest', ], 'errors' => [ [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'CollectorNotFoundFault', ], ], ], 'DeleteFleetAdvisorDatabases' => [ 'name' => 'DeleteFleetAdvisorDatabases', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteFleetAdvisorDatabasesRequest', ], 'output' => [ 'shape' => 'DeleteFleetAdvisorDatabasesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidOperationFault', ], ], ], 'DeleteInstanceProfile' => [ 'name' => 'DeleteInstanceProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteInstanceProfileMessage', ], 'output' => [ 'shape' => 'DeleteInstanceProfileResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'DeleteMigrationProject' => [ 'name' => 'DeleteMigrationProject', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteMigrationProjectMessage', ], 'output' => [ 'shape' => 'DeleteMigrationProjectResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'DeleteReplicationConfig' => [ 'name' => 'DeleteReplicationConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteReplicationConfigMessage', ], 'output' => [ 'shape' => 'DeleteReplicationConfigResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'DeleteReplicationInstance' => [ 'name' => 'DeleteReplicationInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteReplicationInstanceMessage', ], 'output' => [ 'shape' => 'DeleteReplicationInstanceResponse', ], 'errors' => [ [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DeleteReplicationSubnetGroup' => [ 'name' => 'DeleteReplicationSubnetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteReplicationSubnetGroupMessage', ], 'output' => [ 'shape' => 'DeleteReplicationSubnetGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DeleteReplicationTask' => [ 'name' => 'DeleteReplicationTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteReplicationTaskMessage', ], 'output' => [ 'shape' => 'DeleteReplicationTaskResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'DeleteReplicationTaskAssessmentRun' => [ 'name' => 'DeleteReplicationTaskAssessmentRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteReplicationTaskAssessmentRunMessage', ], 'output' => [ 'shape' => 'DeleteReplicationTaskAssessmentRunResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'DescribeAccountAttributes' => [ 'name' => 'DescribeAccountAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAccountAttributesMessage', ], 'output' => [ 'shape' => 'DescribeAccountAttributesResponse', ], ], 'DescribeApplicableIndividualAssessments' => [ 'name' => 'DescribeApplicableIndividualAssessments', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeApplicableIndividualAssessmentsMessage', ], 'output' => [ 'shape' => 'DescribeApplicableIndividualAssessmentsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'DescribeCertificates' => [ 'name' => 'DescribeCertificates', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeCertificatesMessage', ], 'output' => [ 'shape' => 'DescribeCertificatesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DescribeConnections' => [ 'name' => 'DescribeConnections', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeConnectionsMessage', ], 'output' => [ 'shape' => 'DescribeConnectionsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DescribeConversionConfiguration' => [ 'name' => 'DescribeConversionConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeConversionConfigurationMessage', ], 'output' => [ 'shape' => 'DescribeConversionConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DescribeDataProviders' => [ 'name' => 'DescribeDataProviders', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDataProvidersMessage', ], 'output' => [ 'shape' => 'DescribeDataProvidersResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'AccessDeniedFault', ], ], ], 'DescribeEndpointSettings' => [ 'name' => 'DescribeEndpointSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEndpointSettingsMessage', ], 'output' => [ 'shape' => 'DescribeEndpointSettingsResponse', ], ], 'DescribeEndpointTypes' => [ 'name' => 'DescribeEndpointTypes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEndpointTypesMessage', ], 'output' => [ 'shape' => 'DescribeEndpointTypesResponse', ], ], 'DescribeEndpoints' => [ 'name' => 'DescribeEndpoints', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEndpointsMessage', ], 'output' => [ 'shape' => 'DescribeEndpointsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DescribeEngineVersions' => [ 'name' => 'DescribeEngineVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEngineVersionsMessage', ], 'output' => [ 'shape' => 'DescribeEngineVersionsResponse', ], ], 'DescribeEventCategories' => [ 'name' => 'DescribeEventCategories', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEventCategoriesMessage', ], 'output' => [ 'shape' => 'DescribeEventCategoriesResponse', ], ], 'DescribeEventSubscriptions' => [ 'name' => 'DescribeEventSubscriptions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEventSubscriptionsMessage', ], 'output' => [ 'shape' => 'DescribeEventSubscriptionsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DescribeEvents' => [ 'name' => 'DescribeEvents', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEventsMessage', ], 'output' => [ 'shape' => 'DescribeEventsResponse', ], ], 'DescribeExtensionPackAssociations' => [ 'name' => 'DescribeExtensionPackAssociations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeExtensionPackAssociationsMessage', ], 'output' => [ 'shape' => 'DescribeExtensionPackAssociationsResponse', ], ], 'DescribeFleetAdvisorCollectors' => [ 'name' => 'DescribeFleetAdvisorCollectors', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeFleetAdvisorCollectorsRequest', ], 'output' => [ 'shape' => 'DescribeFleetAdvisorCollectorsResponse', ], 'errors' => [ [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'DescribeFleetAdvisorDatabases' => [ 'name' => 'DescribeFleetAdvisorDatabases', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeFleetAdvisorDatabasesRequest', ], 'output' => [ 'shape' => 'DescribeFleetAdvisorDatabasesResponse', ], 'errors' => [ [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'DescribeFleetAdvisorLsaAnalysis' => [ 'name' => 'DescribeFleetAdvisorLsaAnalysis', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeFleetAdvisorLsaAnalysisRequest', ], 'output' => [ 'shape' => 'DescribeFleetAdvisorLsaAnalysisResponse', ], 'errors' => [ [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'DescribeFleetAdvisorSchemaObjectSummary' => [ 'name' => 'DescribeFleetAdvisorSchemaObjectSummary', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeFleetAdvisorSchemaObjectSummaryRequest', ], 'output' => [ 'shape' => 'DescribeFleetAdvisorSchemaObjectSummaryResponse', ], 'errors' => [ [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'DescribeFleetAdvisorSchemas' => [ 'name' => 'DescribeFleetAdvisorSchemas', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeFleetAdvisorSchemasRequest', ], 'output' => [ 'shape' => 'DescribeFleetAdvisorSchemasResponse', ], 'errors' => [ [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'DescribeInstanceProfiles' => [ 'name' => 'DescribeInstanceProfiles', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeInstanceProfilesMessage', ], 'output' => [ 'shape' => 'DescribeInstanceProfilesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'AccessDeniedFault', ], ], ], 'DescribeMetadataModelAssessments' => [ 'name' => 'DescribeMetadataModelAssessments', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeMetadataModelAssessmentsMessage', ], 'output' => [ 'shape' => 'DescribeMetadataModelAssessmentsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DescribeMetadataModelConversions' => [ 'name' => 'DescribeMetadataModelConversions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeMetadataModelConversionsMessage', ], 'output' => [ 'shape' => 'DescribeMetadataModelConversionsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DescribeMetadataModelExportsAsScript' => [ 'name' => 'DescribeMetadataModelExportsAsScript', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeMetadataModelExportsAsScriptMessage', ], 'output' => [ 'shape' => 'DescribeMetadataModelExportsAsScriptResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DescribeMetadataModelExportsToTarget' => [ 'name' => 'DescribeMetadataModelExportsToTarget', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeMetadataModelExportsToTargetMessage', ], 'output' => [ 'shape' => 'DescribeMetadataModelExportsToTargetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DescribeMetadataModelImports' => [ 'name' => 'DescribeMetadataModelImports', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeMetadataModelImportsMessage', ], 'output' => [ 'shape' => 'DescribeMetadataModelImportsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DescribeMigrationProjects' => [ 'name' => 'DescribeMigrationProjects', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeMigrationProjectsMessage', ], 'output' => [ 'shape' => 'DescribeMigrationProjectsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'AccessDeniedFault', ], ], ], 'DescribeOrderableReplicationInstances' => [ 'name' => 'DescribeOrderableReplicationInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeOrderableReplicationInstancesMessage', ], 'output' => [ 'shape' => 'DescribeOrderableReplicationInstancesResponse', ], ], 'DescribePendingMaintenanceActions' => [ 'name' => 'DescribePendingMaintenanceActions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribePendingMaintenanceActionsMessage', ], 'output' => [ 'shape' => 'DescribePendingMaintenanceActionsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DescribeRecommendationLimitations' => [ 'name' => 'DescribeRecommendationLimitations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeRecommendationLimitationsRequest', ], 'output' => [ 'shape' => 'DescribeRecommendationLimitationsResponse', ], 'errors' => [ [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'AccessDeniedFault', ], ], ], 'DescribeRecommendations' => [ 'name' => 'DescribeRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeRecommendationsRequest', ], 'output' => [ 'shape' => 'DescribeRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'AccessDeniedFault', ], ], ], 'DescribeRefreshSchemasStatus' => [ 'name' => 'DescribeRefreshSchemasStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeRefreshSchemasStatusMessage', ], 'output' => [ 'shape' => 'DescribeRefreshSchemasStatusResponse', ], 'errors' => [ [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DescribeReplicationConfigs' => [ 'name' => 'DescribeReplicationConfigs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeReplicationConfigsMessage', ], 'output' => [ 'shape' => 'DescribeReplicationConfigsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DescribeReplicationInstanceTaskLogs' => [ 'name' => 'DescribeReplicationInstanceTaskLogs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeReplicationInstanceTaskLogsMessage', ], 'output' => [ 'shape' => 'DescribeReplicationInstanceTaskLogsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'DescribeReplicationInstances' => [ 'name' => 'DescribeReplicationInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeReplicationInstancesMessage', ], 'output' => [ 'shape' => 'DescribeReplicationInstancesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DescribeReplicationSubnetGroups' => [ 'name' => 'DescribeReplicationSubnetGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeReplicationSubnetGroupsMessage', ], 'output' => [ 'shape' => 'DescribeReplicationSubnetGroupsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DescribeReplicationTableStatistics' => [ 'name' => 'DescribeReplicationTableStatistics', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeReplicationTableStatisticsMessage', ], 'output' => [ 'shape' => 'DescribeReplicationTableStatisticsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'DescribeReplicationTaskAssessmentResults' => [ 'name' => 'DescribeReplicationTaskAssessmentResults', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeReplicationTaskAssessmentResultsMessage', ], 'output' => [ 'shape' => 'DescribeReplicationTaskAssessmentResultsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DescribeReplicationTaskAssessmentRuns' => [ 'name' => 'DescribeReplicationTaskAssessmentRuns', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeReplicationTaskAssessmentRunsMessage', ], 'output' => [ 'shape' => 'DescribeReplicationTaskAssessmentRunsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DescribeReplicationTaskIndividualAssessments' => [ 'name' => 'DescribeReplicationTaskIndividualAssessments', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeReplicationTaskIndividualAssessmentsMessage', ], 'output' => [ 'shape' => 'DescribeReplicationTaskIndividualAssessmentsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DescribeReplicationTasks' => [ 'name' => 'DescribeReplicationTasks', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeReplicationTasksMessage', ], 'output' => [ 'shape' => 'DescribeReplicationTasksResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DescribeReplications' => [ 'name' => 'DescribeReplications', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeReplicationsMessage', ], 'output' => [ 'shape' => 'DescribeReplicationsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DescribeSchemas' => [ 'name' => 'DescribeSchemas', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeSchemasMessage', ], 'output' => [ 'shape' => 'DescribeSchemasResponse', ], 'errors' => [ [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DescribeTableStatistics' => [ 'name' => 'DescribeTableStatistics', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTableStatisticsMessage', ], 'output' => [ 'shape' => 'DescribeTableStatisticsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'ExportMetadataModelAssessment' => [ 'name' => 'ExportMetadataModelAssessment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ExportMetadataModelAssessmentMessage', ], 'output' => [ 'shape' => 'ExportMetadataModelAssessmentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'ImportCertificate' => [ 'name' => 'ImportCertificate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ImportCertificateMessage', ], 'output' => [ 'shape' => 'ImportCertificateResponse', ], 'errors' => [ [ 'shape' => 'ResourceAlreadyExistsFault', ], [ 'shape' => 'InvalidCertificateFault', ], [ 'shape' => 'ResourceQuotaExceededFault', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceMessage', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'ModifyConversionConfiguration' => [ 'name' => 'ModifyConversionConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyConversionConfigurationMessage', ], 'output' => [ 'shape' => 'ModifyConversionConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'ModifyDataProvider' => [ 'name' => 'ModifyDataProvider', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyDataProviderMessage', ], 'output' => [ 'shape' => 'ModifyDataProviderResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'ModifyEndpoint' => [ 'name' => 'ModifyEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyEndpointMessage', ], 'output' => [ 'shape' => 'ModifyEndpointResponse', ], 'errors' => [ [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'ResourceAlreadyExistsFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'AccessDeniedFault', ], ], ], 'ModifyEventSubscription' => [ 'name' => 'ModifyEventSubscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyEventSubscriptionMessage', ], 'output' => [ 'shape' => 'ModifyEventSubscriptionResponse', ], 'errors' => [ [ 'shape' => 'ResourceQuotaExceededFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'SNSInvalidTopicFault', ], [ 'shape' => 'SNSNoAuthorizationFault', ], [ 'shape' => 'KMSAccessDeniedFault', ], [ 'shape' => 'KMSDisabledFault', ], [ 'shape' => 'KMSInvalidStateFault', ], [ 'shape' => 'KMSNotFoundFault', ], [ 'shape' => 'KMSThrottlingFault', ], ], ], 'ModifyInstanceProfile' => [ 'name' => 'ModifyInstanceProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyInstanceProfileMessage', ], 'output' => [ 'shape' => 'ModifyInstanceProfileResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'S3ResourceNotFoundFault', ], [ 'shape' => 'S3AccessDeniedFault', ], ], ], 'ModifyMigrationProject' => [ 'name' => 'ModifyMigrationProject', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyMigrationProjectMessage', ], 'output' => [ 'shape' => 'ModifyMigrationProjectResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'S3ResourceNotFoundFault', ], [ 'shape' => 'S3AccessDeniedFault', ], ], ], 'ModifyReplicationConfig' => [ 'name' => 'ModifyReplicationConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyReplicationConfigMessage', ], 'output' => [ 'shape' => 'ModifyReplicationConfigResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'ReplicationSubnetGroupDoesNotCoverEnoughAZs', ], [ 'shape' => 'InvalidSubnet', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'ModifyReplicationInstance' => [ 'name' => 'ModifyReplicationInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyReplicationInstanceMessage', ], 'output' => [ 'shape' => 'ModifyReplicationInstanceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'ResourceAlreadyExistsFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InsufficientResourceCapacityFault', ], [ 'shape' => 'StorageQuotaExceededFault', ], [ 'shape' => 'UpgradeDependencyFailureFault', ], ], ], 'ModifyReplicationSubnetGroup' => [ 'name' => 'ModifyReplicationSubnetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyReplicationSubnetGroupMessage', ], 'output' => [ 'shape' => 'ModifyReplicationSubnetGroupResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'ResourceQuotaExceededFault', ], [ 'shape' => 'SubnetAlreadyInUse', ], [ 'shape' => 'ReplicationSubnetGroupDoesNotCoverEnoughAZs', ], [ 'shape' => 'InvalidSubnet', ], ], ], 'ModifyReplicationTask' => [ 'name' => 'ModifyReplicationTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyReplicationTaskMessage', ], 'output' => [ 'shape' => 'ModifyReplicationTaskResponse', ], 'errors' => [ [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'ResourceAlreadyExistsFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], ], ], 'MoveReplicationTask' => [ 'name' => 'MoveReplicationTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'MoveReplicationTaskMessage', ], 'output' => [ 'shape' => 'MoveReplicationTaskResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'ResourceQuotaExceededFault', ], ], ], 'RebootReplicationInstance' => [ 'name' => 'RebootReplicationInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RebootReplicationInstanceMessage', ], 'output' => [ 'shape' => 'RebootReplicationInstanceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'RefreshSchemas' => [ 'name' => 'RefreshSchemas', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RefreshSchemasMessage', ], 'output' => [ 'shape' => 'RefreshSchemasResponse', ], 'errors' => [ [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'ResourceQuotaExceededFault', ], ], ], 'ReloadReplicationTables' => [ 'name' => 'ReloadReplicationTables', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ReloadReplicationTablesMessage', ], 'output' => [ 'shape' => 'ReloadReplicationTablesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'ReloadTables' => [ 'name' => 'ReloadTables', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ReloadTablesMessage', ], 'output' => [ 'shape' => 'ReloadTablesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'RemoveTagsFromResource' => [ 'name' => 'RemoveTagsFromResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemoveTagsFromResourceMessage', ], 'output' => [ 'shape' => 'RemoveTagsFromResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'RunFleetAdvisorLsaAnalysis' => [ 'name' => 'RunFleetAdvisorLsaAnalysis', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'output' => [ 'shape' => 'RunFleetAdvisorLsaAnalysisResponse', ], 'errors' => [ [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'StartExtensionPackAssociation' => [ 'name' => 'StartExtensionPackAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartExtensionPackAssociationMessage', ], 'output' => [ 'shape' => 'StartExtensionPackAssociationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'ResourceAlreadyExistsFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'ResourceQuotaExceededFault', ], [ 'shape' => 'S3ResourceNotFoundFault', ], [ 'shape' => 'S3AccessDeniedFault', ], ], ], 'StartMetadataModelAssessment' => [ 'name' => 'StartMetadataModelAssessment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartMetadataModelAssessmentMessage', ], 'output' => [ 'shape' => 'StartMetadataModelAssessmentResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'ResourceAlreadyExistsFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'ResourceQuotaExceededFault', ], [ 'shape' => 'S3ResourceNotFoundFault', ], [ 'shape' => 'S3AccessDeniedFault', ], ], ], 'StartMetadataModelConversion' => [ 'name' => 'StartMetadataModelConversion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartMetadataModelConversionMessage', ], 'output' => [ 'shape' => 'StartMetadataModelConversionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'ResourceAlreadyExistsFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'ResourceQuotaExceededFault', ], [ 'shape' => 'S3ResourceNotFoundFault', ], [ 'shape' => 'S3AccessDeniedFault', ], ], ], 'StartMetadataModelExportAsScript' => [ 'name' => 'StartMetadataModelExportAsScript', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartMetadataModelExportAsScriptMessage', ], 'output' => [ 'shape' => 'StartMetadataModelExportAsScriptResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'ResourceAlreadyExistsFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'ResourceQuotaExceededFault', ], [ 'shape' => 'S3ResourceNotFoundFault', ], [ 'shape' => 'S3AccessDeniedFault', ], ], ], 'StartMetadataModelExportToTarget' => [ 'name' => 'StartMetadataModelExportToTarget', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartMetadataModelExportToTargetMessage', ], 'output' => [ 'shape' => 'StartMetadataModelExportToTargetResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'ResourceAlreadyExistsFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'ResourceQuotaExceededFault', ], [ 'shape' => 'S3ResourceNotFoundFault', ], [ 'shape' => 'S3AccessDeniedFault', ], ], ], 'StartMetadataModelImport' => [ 'name' => 'StartMetadataModelImport', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartMetadataModelImportMessage', ], 'output' => [ 'shape' => 'StartMetadataModelImportResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'ResourceAlreadyExistsFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'ResourceQuotaExceededFault', ], [ 'shape' => 'S3ResourceNotFoundFault', ], [ 'shape' => 'S3AccessDeniedFault', ], ], ], 'StartRecommendations' => [ 'name' => 'StartRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartRecommendationsRequest', ], 'errors' => [ [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'StartReplication' => [ 'name' => 'StartReplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartReplicationMessage', ], 'output' => [ 'shape' => 'StartReplicationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'AccessDeniedFault', ], ], ], 'StartReplicationTask' => [ 'name' => 'StartReplicationTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartReplicationTaskMessage', ], 'output' => [ 'shape' => 'StartReplicationTaskResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'AccessDeniedFault', ], ], ], 'StartReplicationTaskAssessment' => [ 'name' => 'StartReplicationTaskAssessment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartReplicationTaskAssessmentMessage', ], 'output' => [ 'shape' => 'StartReplicationTaskAssessmentResponse', ], 'errors' => [ [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'StartReplicationTaskAssessmentRun' => [ 'name' => 'StartReplicationTaskAssessmentRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartReplicationTaskAssessmentRunMessage', ], 'output' => [ 'shape' => 'StartReplicationTaskAssessmentRunResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'KMSAccessDeniedFault', ], [ 'shape' => 'KMSDisabledFault', ], [ 'shape' => 'KMSFault', ], [ 'shape' => 'KMSInvalidStateFault', ], [ 'shape' => 'KMSNotFoundFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'S3AccessDeniedFault', ], [ 'shape' => 'S3ResourceNotFoundFault', ], [ 'shape' => 'ResourceAlreadyExistsFault', ], ], ], 'StopReplication' => [ 'name' => 'StopReplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopReplicationMessage', ], 'output' => [ 'shape' => 'StopReplicationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'AccessDeniedFault', ], ], ], 'StopReplicationTask' => [ 'name' => 'StopReplicationTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopReplicationTaskMessage', ], 'output' => [ 'shape' => 'StopReplicationTaskResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'TestConnection' => [ 'name' => 'TestConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TestConnectionMessage', ], 'output' => [ 'shape' => 'TestConnectionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'ResourceQuotaExceededFault', ], [ 'shape' => 'AccessDeniedFault', ], ], ], 'UpdateSubscriptionsToEventBridge' => [ 'name' => 'UpdateSubscriptionsToEventBridge', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateSubscriptionsToEventBridgeMessage', ], 'output' => [ 'shape' => 'UpdateSubscriptionsToEventBridgeResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'InvalidResourceStateFault', ], ], ], ], 'shapes' => [ 'AccessDeniedFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'AccountQuota' => [ 'type' => 'structure', 'members' => [ 'AccountQuotaName' => [ 'shape' => 'String', ], 'Used' => [ 'shape' => 'Long', ], 'Max' => [ 'shape' => 'Long', ], ], ], 'AccountQuotaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountQuota', ], ], 'AddTagsToResourceMessage' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'AddTagsToResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'ApplyPendingMaintenanceActionMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationInstanceArn', 'ApplyAction', 'OptInType', ], 'members' => [ 'ReplicationInstanceArn' => [ 'shape' => 'String', ], 'ApplyAction' => [ 'shape' => 'String', ], 'OptInType' => [ 'shape' => 'String', ], ], ], 'ApplyPendingMaintenanceActionResponse' => [ 'type' => 'structure', 'members' => [ 'ResourcePendingMaintenanceActions' => [ 'shape' => 'ResourcePendingMaintenanceActions', ], ], ], 'ArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'AssessmentReportType' => [ 'type' => 'string', 'enum' => [ 'pdf', 'csv', ], ], 'AssessmentReportTypesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssessmentReportType', ], 'min' => 1, ], 'AuthMechanismValue' => [ 'type' => 'string', 'enum' => [ 'default', 'mongodb_cr', 'scram_sha_1', ], ], 'AuthTypeValue' => [ 'type' => 'string', 'enum' => [ 'no', 'password', ], ], 'AvailabilityZone' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], ], ], 'AvailabilityZonesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'AvailableUpgradesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'BatchStartRecommendationsErrorEntry' => [ 'type' => 'structure', 'members' => [ 'DatabaseId' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], 'Code' => [ 'shape' => 'String', ], ], ], 'BatchStartRecommendationsErrorEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchStartRecommendationsErrorEntry', ], ], 'BatchStartRecommendationsRequest' => [ 'type' => 'structure', 'members' => [ 'Data' => [ 'shape' => 'StartRecommendationsRequestEntryList', ], ], ], 'BatchStartRecommendationsResponse' => [ 'type' => 'structure', 'members' => [ 'ErrorEntries' => [ 'shape' => 'BatchStartRecommendationsErrorEntryList', ], ], ], 'Boolean' => [ 'type' => 'boolean', ], 'BooleanOptional' => [ 'type' => 'boolean', ], 'CancelReplicationTaskAssessmentRunMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationTaskAssessmentRunArn', ], 'members' => [ 'ReplicationTaskAssessmentRunArn' => [ 'shape' => 'String', ], ], ], 'CancelReplicationTaskAssessmentRunResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationTaskAssessmentRun' => [ 'shape' => 'ReplicationTaskAssessmentRun', ], ], ], 'CannedAclForObjectsValue' => [ 'type' => 'string', 'enum' => [ 'none', 'private', 'public-read', 'public-read-write', 'authenticated-read', 'aws-exec-read', 'bucket-owner-read', 'bucket-owner-full-control', ], ], 'Certificate' => [ 'type' => 'structure', 'members' => [ 'CertificateIdentifier' => [ 'shape' => 'String', ], 'CertificateCreationDate' => [ 'shape' => 'TStamp', ], 'CertificatePem' => [ 'shape' => 'String', ], 'CertificateWallet' => [ 'shape' => 'CertificateWallet', ], 'CertificateArn' => [ 'shape' => 'String', ], 'CertificateOwner' => [ 'shape' => 'String', ], 'ValidFromDate' => [ 'shape' => 'TStamp', ], 'ValidToDate' => [ 'shape' => 'TStamp', ], 'SigningAlgorithm' => [ 'shape' => 'String', ], 'KeyLength' => [ 'shape' => 'IntegerOptional', ], ], ], 'CertificateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Certificate', ], ], 'CertificateWallet' => [ 'type' => 'blob', ], 'CharLengthSemantics' => [ 'type' => 'string', 'enum' => [ 'default', 'char', 'byte', ], ], 'CollectorHealthCheck' => [ 'type' => 'structure', 'members' => [ 'CollectorStatus' => [ 'shape' => 'CollectorStatus', ], 'LocalCollectorS3Access' => [ 'shape' => 'BooleanOptional', ], 'WebCollectorS3Access' => [ 'shape' => 'BooleanOptional', ], 'WebCollectorGrantedRoleBasedAccess' => [ 'shape' => 'BooleanOptional', ], ], ], 'CollectorNotFoundFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'CollectorResponse' => [ 'type' => 'structure', 'members' => [ 'CollectorReferencedId' => [ 'shape' => 'String', ], 'CollectorName' => [ 'shape' => 'String', ], 'CollectorVersion' => [ 'shape' => 'String', ], 'VersionStatus' => [ 'shape' => 'VersionStatus', ], 'Description' => [ 'shape' => 'String', ], 'S3BucketName' => [ 'shape' => 'String', ], 'ServiceAccessRoleArn' => [ 'shape' => 'String', ], 'CollectorHealthCheck' => [ 'shape' => 'CollectorHealthCheck', ], 'LastDataReceived' => [ 'shape' => 'String', ], 'RegisteredDate' => [ 'shape' => 'String', ], 'CreatedDate' => [ 'shape' => 'String', ], 'ModifiedDate' => [ 'shape' => 'String', ], 'InventoryData' => [ 'shape' => 'InventoryData', ], ], ], 'CollectorResponses' => [ 'type' => 'list', 'member' => [ 'shape' => 'CollectorResponse', ], ], 'CollectorShortInfoResponse' => [ 'type' => 'structure', 'members' => [ 'CollectorReferencedId' => [ 'shape' => 'String', ], 'CollectorName' => [ 'shape' => 'String', ], ], ], 'CollectorStatus' => [ 'type' => 'string', 'enum' => [ 'UNREGISTERED', 'ACTIVE', ], ], 'CollectorsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CollectorShortInfoResponse', ], ], 'CompressionTypeValue' => [ 'type' => 'string', 'enum' => [ 'none', 'gzip', ], ], 'ComputeConfig' => [ 'type' => 'structure', 'members' => [ 'AvailabilityZone' => [ 'shape' => 'String', ], 'DnsNameServers' => [ 'shape' => 'String', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'MaxCapacityUnits' => [ 'shape' => 'IntegerOptional', ], 'MinCapacityUnits' => [ 'shape' => 'IntegerOptional', ], 'MultiAZ' => [ 'shape' => 'BooleanOptional', ], 'PreferredMaintenanceWindow' => [ 'shape' => 'String', ], 'ReplicationSubnetGroupId' => [ 'shape' => 'String', ], 'VpcSecurityGroupIds' => [ 'shape' => 'StringList', ], ], ], 'Connection' => [ 'type' => 'structure', 'members' => [ 'ReplicationInstanceArn' => [ 'shape' => 'String', ], 'EndpointArn' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'LastFailureMessage' => [ 'shape' => 'String', ], 'EndpointIdentifier' => [ 'shape' => 'String', ], 'ReplicationInstanceIdentifier' => [ 'shape' => 'String', ], ], ], 'ConnectionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Connection', ], ], 'CreateDataProviderMessage' => [ 'type' => 'structure', 'required' => [ 'Engine', 'Settings', ], 'members' => [ 'DataProviderName' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'String', ], 'Settings' => [ 'shape' => 'DataProviderSettings', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateDataProviderResponse' => [ 'type' => 'structure', 'members' => [ 'DataProvider' => [ 'shape' => 'DataProvider', ], ], ], 'CreateEndpointMessage' => [ 'type' => 'structure', 'required' => [ 'EndpointIdentifier', 'EndpointType', 'EngineName', ], 'members' => [ 'EndpointIdentifier' => [ 'shape' => 'String', ], 'EndpointType' => [ 'shape' => 'ReplicationEndpointTypeValue', ], 'EngineName' => [ 'shape' => 'String', ], 'Username' => [ 'shape' => 'String', ], 'Password' => [ 'shape' => 'SecretString', ], 'ServerName' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'DatabaseName' => [ 'shape' => 'String', ], 'ExtraConnectionAttributes' => [ 'shape' => 'String', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], 'CertificateArn' => [ 'shape' => 'String', ], 'SslMode' => [ 'shape' => 'DmsSslModeValue', ], 'ServiceAccessRoleArn' => [ 'shape' => 'String', ], 'ExternalTableDefinition' => [ 'shape' => 'String', ], 'DynamoDbSettings' => [ 'shape' => 'DynamoDbSettings', ], 'S3Settings' => [ 'shape' => 'S3Settings', ], 'DmsTransferSettings' => [ 'shape' => 'DmsTransferSettings', ], 'MongoDbSettings' => [ 'shape' => 'MongoDbSettings', ], 'KinesisSettings' => [ 'shape' => 'KinesisSettings', ], 'KafkaSettings' => [ 'shape' => 'KafkaSettings', ], 'ElasticsearchSettings' => [ 'shape' => 'ElasticsearchSettings', ], 'NeptuneSettings' => [ 'shape' => 'NeptuneSettings', ], 'RedshiftSettings' => [ 'shape' => 'RedshiftSettings', ], 'PostgreSQLSettings' => [ 'shape' => 'PostgreSQLSettings', ], 'MySQLSettings' => [ 'shape' => 'MySQLSettings', ], 'OracleSettings' => [ 'shape' => 'OracleSettings', ], 'SybaseSettings' => [ 'shape' => 'SybaseSettings', ], 'MicrosoftSQLServerSettings' => [ 'shape' => 'MicrosoftSQLServerSettings', ], 'IBMDb2Settings' => [ 'shape' => 'IBMDb2Settings', ], 'ResourceIdentifier' => [ 'shape' => 'String', ], 'DocDbSettings' => [ 'shape' => 'DocDbSettings', ], 'RedisSettings' => [ 'shape' => 'RedisSettings', ], 'GcpMySQLSettings' => [ 'shape' => 'GcpMySQLSettings', ], 'TimestreamSettings' => [ 'shape' => 'TimestreamSettings', ], ], ], 'CreateEndpointResponse' => [ 'type' => 'structure', 'members' => [ 'Endpoint' => [ 'shape' => 'Endpoint', ], ], ], 'CreateEventSubscriptionMessage' => [ 'type' => 'structure', 'required' => [ 'SubscriptionName', 'SnsTopicArn', ], 'members' => [ 'SubscriptionName' => [ 'shape' => 'String', ], 'SnsTopicArn' => [ 'shape' => 'String', ], 'SourceType' => [ 'shape' => 'String', ], 'EventCategories' => [ 'shape' => 'EventCategoriesList', ], 'SourceIds' => [ 'shape' => 'SourceIdsList', ], 'Enabled' => [ 'shape' => 'BooleanOptional', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateEventSubscriptionResponse' => [ 'type' => 'structure', 'members' => [ 'EventSubscription' => [ 'shape' => 'EventSubscription', ], ], ], 'CreateFleetAdvisorCollectorRequest' => [ 'type' => 'structure', 'required' => [ 'CollectorName', 'ServiceAccessRoleArn', 'S3BucketName', ], 'members' => [ 'CollectorName' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'ServiceAccessRoleArn' => [ 'shape' => 'String', ], 'S3BucketName' => [ 'shape' => 'String', ], ], ], 'CreateFleetAdvisorCollectorResponse' => [ 'type' => 'structure', 'members' => [ 'CollectorReferencedId' => [ 'shape' => 'String', ], 'CollectorName' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'ServiceAccessRoleArn' => [ 'shape' => 'String', ], 'S3BucketName' => [ 'shape' => 'String', ], ], ], 'CreateInstanceProfileMessage' => [ 'type' => 'structure', 'members' => [ 'AvailabilityZone' => [ 'shape' => 'String', ], 'KmsKeyArn' => [ 'shape' => 'String', ], 'PubliclyAccessible' => [ 'shape' => 'BooleanOptional', ], 'Tags' => [ 'shape' => 'TagList', ], 'NetworkType' => [ 'shape' => 'String', ], 'InstanceProfileName' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'SubnetGroupIdentifier' => [ 'shape' => 'String', ], 'VpcSecurityGroups' => [ 'shape' => 'StringList', ], ], ], 'CreateInstanceProfileResponse' => [ 'type' => 'structure', 'members' => [ 'InstanceProfile' => [ 'shape' => 'InstanceProfile', ], ], ], 'CreateMigrationProjectMessage' => [ 'type' => 'structure', 'required' => [ 'SourceDataProviderDescriptors', 'TargetDataProviderDescriptors', 'InstanceProfileIdentifier', ], 'members' => [ 'MigrationProjectName' => [ 'shape' => 'String', ], 'SourceDataProviderDescriptors' => [ 'shape' => 'DataProviderDescriptorDefinitionList', ], 'TargetDataProviderDescriptors' => [ 'shape' => 'DataProviderDescriptorDefinitionList', ], 'InstanceProfileIdentifier' => [ 'shape' => 'String', ], 'TransformationRules' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], 'SchemaConversionApplicationAttributes' => [ 'shape' => 'SCApplicationAttributes', ], ], ], 'CreateMigrationProjectResponse' => [ 'type' => 'structure', 'members' => [ 'MigrationProject' => [ 'shape' => 'MigrationProject', ], ], ], 'CreateReplicationConfigMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationConfigIdentifier', 'SourceEndpointArn', 'TargetEndpointArn', 'ComputeConfig', 'ReplicationType', 'TableMappings', ], 'members' => [ 'ReplicationConfigIdentifier' => [ 'shape' => 'String', ], 'SourceEndpointArn' => [ 'shape' => 'String', ], 'TargetEndpointArn' => [ 'shape' => 'String', ], 'ComputeConfig' => [ 'shape' => 'ComputeConfig', ], 'ReplicationType' => [ 'shape' => 'MigrationTypeValue', ], 'TableMappings' => [ 'shape' => 'String', ], 'ReplicationSettings' => [ 'shape' => 'String', ], 'SupplementalSettings' => [ 'shape' => 'String', ], 'ResourceIdentifier' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateReplicationConfigResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationConfig' => [ 'shape' => 'ReplicationConfig', ], ], ], 'CreateReplicationInstanceMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationInstanceIdentifier', 'ReplicationInstanceClass', ], 'members' => [ 'ReplicationInstanceIdentifier' => [ 'shape' => 'String', ], 'AllocatedStorage' => [ 'shape' => 'IntegerOptional', ], 'ReplicationInstanceClass' => [ 'shape' => 'String', ], 'VpcSecurityGroupIds' => [ 'shape' => 'VpcSecurityGroupIdList', ], 'AvailabilityZone' => [ 'shape' => 'String', ], 'ReplicationSubnetGroupIdentifier' => [ 'shape' => 'String', ], 'PreferredMaintenanceWindow' => [ 'shape' => 'String', ], 'MultiAZ' => [ 'shape' => 'BooleanOptional', ], 'EngineVersion' => [ 'shape' => 'String', ], 'AutoMinorVersionUpgrade' => [ 'shape' => 'BooleanOptional', ], 'Tags' => [ 'shape' => 'TagList', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'PubliclyAccessible' => [ 'shape' => 'BooleanOptional', ], 'DnsNameServers' => [ 'shape' => 'String', ], 'ResourceIdentifier' => [ 'shape' => 'String', ], 'NetworkType' => [ 'shape' => 'String', ], ], ], 'CreateReplicationInstanceResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationInstance' => [ 'shape' => 'ReplicationInstance', ], ], ], 'CreateReplicationSubnetGroupMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationSubnetGroupIdentifier', 'ReplicationSubnetGroupDescription', 'SubnetIds', ], 'members' => [ 'ReplicationSubnetGroupIdentifier' => [ 'shape' => 'String', ], 'ReplicationSubnetGroupDescription' => [ 'shape' => 'String', ], 'SubnetIds' => [ 'shape' => 'SubnetIdentifierList', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateReplicationSubnetGroupResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationSubnetGroup' => [ 'shape' => 'ReplicationSubnetGroup', ], ], ], 'CreateReplicationTaskMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationTaskIdentifier', 'SourceEndpointArn', 'TargetEndpointArn', 'ReplicationInstanceArn', 'MigrationType', 'TableMappings', ], 'members' => [ 'ReplicationTaskIdentifier' => [ 'shape' => 'String', ], 'SourceEndpointArn' => [ 'shape' => 'String', ], 'TargetEndpointArn' => [ 'shape' => 'String', ], 'ReplicationInstanceArn' => [ 'shape' => 'String', ], 'MigrationType' => [ 'shape' => 'MigrationTypeValue', ], 'TableMappings' => [ 'shape' => 'String', ], 'ReplicationTaskSettings' => [ 'shape' => 'String', ], 'CdcStartTime' => [ 'shape' => 'TStamp', ], 'CdcStartPosition' => [ 'shape' => 'String', ], 'CdcStopPosition' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], 'TaskData' => [ 'shape' => 'String', ], 'ResourceIdentifier' => [ 'shape' => 'String', ], ], ], 'CreateReplicationTaskResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationTask' => [ 'shape' => 'ReplicationTask', ], ], ], 'DataFormatValue' => [ 'type' => 'string', 'enum' => [ 'csv', 'parquet', ], ], 'DataProvider' => [ 'type' => 'structure', 'members' => [ 'DataProviderName' => [ 'shape' => 'String', ], 'DataProviderArn' => [ 'shape' => 'String', ], 'DataProviderCreationTime' => [ 'shape' => 'Iso8601DateTime', ], 'Description' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'String', ], 'Settings' => [ 'shape' => 'DataProviderSettings', ], ], ], 'DataProviderDescriptor' => [ 'type' => 'structure', 'members' => [ 'SecretsManagerSecretId' => [ 'shape' => 'String', ], 'SecretsManagerAccessRoleArn' => [ 'shape' => 'String', ], 'DataProviderName' => [ 'shape' => 'String', ], 'DataProviderArn' => [ 'shape' => 'String', ], ], ], 'DataProviderDescriptorDefinition' => [ 'type' => 'structure', 'required' => [ 'DataProviderIdentifier', ], 'members' => [ 'DataProviderIdentifier' => [ 'shape' => 'String', ], 'SecretsManagerSecretId' => [ 'shape' => 'String', ], 'SecretsManagerAccessRoleArn' => [ 'shape' => 'String', ], ], ], 'DataProviderDescriptorDefinitionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataProviderDescriptorDefinition', ], ], 'DataProviderDescriptorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataProviderDescriptor', ], ], 'DataProviderList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataProvider', ], ], 'DataProviderSettings' => [ 'type' => 'structure', 'members' => [ 'RedshiftSettings' => [ 'shape' => 'RedshiftDataProviderSettings', ], 'PostgreSqlSettings' => [ 'shape' => 'PostgreSqlDataProviderSettings', ], 'MySqlSettings' => [ 'shape' => 'MySqlDataProviderSettings', ], 'OracleSettings' => [ 'shape' => 'OracleDataProviderSettings', ], 'MicrosoftSqlServerSettings' => [ 'shape' => 'MicrosoftSqlServerDataProviderSettings', ], 'DocDbSettings' => [ 'shape' => 'DocDbDataProviderSettings', ], 'MariaDbSettings' => [ 'shape' => 'MariaDbDataProviderSettings', ], 'MongoDbSettings' => [ 'shape' => 'MongoDbDataProviderSettings', ], ], 'union' => true, ], 'DatabaseInstanceSoftwareDetailsResponse' => [ 'type' => 'structure', 'members' => [ 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'EngineEdition' => [ 'shape' => 'String', ], 'ServicePack' => [ 'shape' => 'String', ], 'SupportLevel' => [ 'shape' => 'String', ], 'OsArchitecture' => [ 'shape' => 'IntegerOptional', ], 'Tooltip' => [ 'shape' => 'String', ], ], ], 'DatabaseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DatabaseResponse', ], ], 'DatabaseMode' => [ 'type' => 'string', 'enum' => [ 'default', 'babelfish', ], ], 'DatabaseResponse' => [ 'type' => 'structure', 'members' => [ 'DatabaseId' => [ 'shape' => 'String', ], 'DatabaseName' => [ 'shape' => 'String', ], 'IpAddress' => [ 'shape' => 'String', ], 'NumberOfSchemas' => [ 'shape' => 'LongOptional', ], 'Server' => [ 'shape' => 'ServerShortInfoResponse', ], 'SoftwareDetails' => [ 'shape' => 'DatabaseInstanceSoftwareDetailsResponse', ], 'Collectors' => [ 'shape' => 'CollectorsList', ], ], ], 'DatabaseShortInfoResponse' => [ 'type' => 'structure', 'members' => [ 'DatabaseId' => [ 'shape' => 'String', ], 'DatabaseName' => [ 'shape' => 'String', ], 'DatabaseIpAddress' => [ 'shape' => 'String', ], 'DatabaseEngine' => [ 'shape' => 'String', ], ], ], 'DatePartitionDelimiterValue' => [ 'type' => 'string', 'enum' => [ 'SLASH', 'UNDERSCORE', 'DASH', 'NONE', ], ], 'DatePartitionSequenceValue' => [ 'type' => 'string', 'enum' => [ 'YYYYMMDD', 'YYYYMMDDHH', 'YYYYMM', 'MMYYYYDD', 'DDMMYYYY', ], ], 'DefaultErrorDetails' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], ], 'DeleteCertificateMessage' => [ 'type' => 'structure', 'required' => [ 'CertificateArn', ], 'members' => [ 'CertificateArn' => [ 'shape' => 'String', ], ], ], 'DeleteCertificateResponse' => [ 'type' => 'structure', 'members' => [ 'Certificate' => [ 'shape' => 'Certificate', ], ], ], 'DeleteCollectorRequest' => [ 'type' => 'structure', 'required' => [ 'CollectorReferencedId', ], 'members' => [ 'CollectorReferencedId' => [ 'shape' => 'String', ], ], ], 'DeleteConnectionMessage' => [ 'type' => 'structure', 'required' => [ 'EndpointArn', 'ReplicationInstanceArn', ], 'members' => [ 'EndpointArn' => [ 'shape' => 'String', ], 'ReplicationInstanceArn' => [ 'shape' => 'String', ], ], ], 'DeleteConnectionResponse' => [ 'type' => 'structure', 'members' => [ 'Connection' => [ 'shape' => 'Connection', ], ], ], 'DeleteDataProviderMessage' => [ 'type' => 'structure', 'required' => [ 'DataProviderIdentifier', ], 'members' => [ 'DataProviderIdentifier' => [ 'shape' => 'String', ], ], ], 'DeleteDataProviderResponse' => [ 'type' => 'structure', 'members' => [ 'DataProvider' => [ 'shape' => 'DataProvider', ], ], ], 'DeleteEndpointMessage' => [ 'type' => 'structure', 'required' => [ 'EndpointArn', ], 'members' => [ 'EndpointArn' => [ 'shape' => 'String', ], ], ], 'DeleteEndpointResponse' => [ 'type' => 'structure', 'members' => [ 'Endpoint' => [ 'shape' => 'Endpoint', ], ], ], 'DeleteEventSubscriptionMessage' => [ 'type' => 'structure', 'required' => [ 'SubscriptionName', ], 'members' => [ 'SubscriptionName' => [ 'shape' => 'String', ], ], ], 'DeleteEventSubscriptionResponse' => [ 'type' => 'structure', 'members' => [ 'EventSubscription' => [ 'shape' => 'EventSubscription', ], ], ], 'DeleteFleetAdvisorDatabasesRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseIds', ], 'members' => [ 'DatabaseIds' => [ 'shape' => 'StringList', ], ], ], 'DeleteFleetAdvisorDatabasesResponse' => [ 'type' => 'structure', 'members' => [ 'DatabaseIds' => [ 'shape' => 'StringList', ], ], ], 'DeleteInstanceProfileMessage' => [ 'type' => 'structure', 'required' => [ 'InstanceProfileIdentifier', ], 'members' => [ 'InstanceProfileIdentifier' => [ 'shape' => 'String', ], ], ], 'DeleteInstanceProfileResponse' => [ 'type' => 'structure', 'members' => [ 'InstanceProfile' => [ 'shape' => 'InstanceProfile', ], ], ], 'DeleteMigrationProjectMessage' => [ 'type' => 'structure', 'required' => [ 'MigrationProjectIdentifier', ], 'members' => [ 'MigrationProjectIdentifier' => [ 'shape' => 'String', ], ], ], 'DeleteMigrationProjectResponse' => [ 'type' => 'structure', 'members' => [ 'MigrationProject' => [ 'shape' => 'MigrationProject', ], ], ], 'DeleteReplicationConfigMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationConfigArn', ], 'members' => [ 'ReplicationConfigArn' => [ 'shape' => 'String', ], ], ], 'DeleteReplicationConfigResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationConfig' => [ 'shape' => 'ReplicationConfig', ], ], ], 'DeleteReplicationInstanceMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationInstanceArn', ], 'members' => [ 'ReplicationInstanceArn' => [ 'shape' => 'String', ], ], ], 'DeleteReplicationInstanceResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationInstance' => [ 'shape' => 'ReplicationInstance', ], ], ], 'DeleteReplicationSubnetGroupMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationSubnetGroupIdentifier', ], 'members' => [ 'ReplicationSubnetGroupIdentifier' => [ 'shape' => 'String', ], ], ], 'DeleteReplicationSubnetGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteReplicationTaskAssessmentRunMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationTaskAssessmentRunArn', ], 'members' => [ 'ReplicationTaskAssessmentRunArn' => [ 'shape' => 'String', ], ], ], 'DeleteReplicationTaskAssessmentRunResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationTaskAssessmentRun' => [ 'shape' => 'ReplicationTaskAssessmentRun', ], ], ], 'DeleteReplicationTaskMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationTaskArn', ], 'members' => [ 'ReplicationTaskArn' => [ 'shape' => 'String', ], ], ], 'DeleteReplicationTaskResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationTask' => [ 'shape' => 'ReplicationTask', ], ], ], 'DescribeAccountAttributesMessage' => [ 'type' => 'structure', 'members' => [], ], 'DescribeAccountAttributesResponse' => [ 'type' => 'structure', 'members' => [ 'AccountQuotas' => [ 'shape' => 'AccountQuotaList', ], 'UniqueAccountIdentifier' => [ 'shape' => 'String', ], ], ], 'DescribeApplicableIndividualAssessmentsMessage' => [ 'type' => 'structure', 'members' => [ 'ReplicationTaskArn' => [ 'shape' => 'String', ], 'ReplicationInstanceArn' => [ 'shape' => 'String', ], 'SourceEngineName' => [ 'shape' => 'String', ], 'TargetEngineName' => [ 'shape' => 'String', ], 'MigrationType' => [ 'shape' => 'MigrationTypeValue', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeApplicableIndividualAssessmentsResponse' => [ 'type' => 'structure', 'members' => [ 'IndividualAssessmentNames' => [ 'shape' => 'IndividualAssessmentNameList', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeCertificatesMessage' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeCertificatesResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'Certificates' => [ 'shape' => 'CertificateList', ], ], ], 'DescribeConnectionsMessage' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeConnectionsResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'Connections' => [ 'shape' => 'ConnectionList', ], ], ], 'DescribeConversionConfigurationMessage' => [ 'type' => 'structure', 'required' => [ 'MigrationProjectIdentifier', ], 'members' => [ 'MigrationProjectIdentifier' => [ 'shape' => 'String', ], ], ], 'DescribeConversionConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'MigrationProjectIdentifier' => [ 'shape' => 'String', ], 'ConversionConfiguration' => [ 'shape' => 'String', ], ], ], 'DescribeDataProvidersMessage' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeDataProvidersResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'DataProviders' => [ 'shape' => 'DataProviderList', ], ], ], 'DescribeEndpointSettingsMessage' => [ 'type' => 'structure', 'required' => [ 'EngineName', ], 'members' => [ 'EngineName' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeEndpointSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'EndpointSettings' => [ 'shape' => 'EndpointSettingsList', ], ], ], 'DescribeEndpointTypesMessage' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeEndpointTypesResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'SupportedEndpointTypes' => [ 'shape' => 'SupportedEndpointTypeList', ], ], ], 'DescribeEndpointsMessage' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeEndpointsResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'Endpoints' => [ 'shape' => 'EndpointList', ], ], ], 'DescribeEngineVersionsMessage' => [ 'type' => 'structure', 'members' => [ 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeEngineVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'EngineVersions' => [ 'shape' => 'EngineVersionList', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeEventCategoriesMessage' => [ 'type' => 'structure', 'members' => [ 'SourceType' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], ], ], 'DescribeEventCategoriesResponse' => [ 'type' => 'structure', 'members' => [ 'EventCategoryGroupList' => [ 'shape' => 'EventCategoryGroupList', ], ], ], 'DescribeEventSubscriptionsMessage' => [ 'type' => 'structure', 'members' => [ 'SubscriptionName' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeEventSubscriptionsResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'EventSubscriptionsList' => [ 'shape' => 'EventSubscriptionsList', ], ], ], 'DescribeEventsMessage' => [ 'type' => 'structure', 'members' => [ 'SourceIdentifier' => [ 'shape' => 'String', ], 'SourceType' => [ 'shape' => 'SourceType', ], 'StartTime' => [ 'shape' => 'TStamp', ], 'EndTime' => [ 'shape' => 'TStamp', ], 'Duration' => [ 'shape' => 'IntegerOptional', ], 'EventCategories' => [ 'shape' => 'EventCategoriesList', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeEventsResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'Events' => [ 'shape' => 'EventList', ], ], ], 'DescribeExtensionPackAssociationsMessage' => [ 'type' => 'structure', 'required' => [ 'MigrationProjectIdentifier', ], 'members' => [ 'MigrationProjectIdentifier' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'Marker' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], ], ], 'DescribeExtensionPackAssociationsResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'Requests' => [ 'shape' => 'SchemaConversionRequestList', ], ], ], 'DescribeFleetAdvisorCollectorsRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeFleetAdvisorCollectorsResponse' => [ 'type' => 'structure', 'members' => [ 'Collectors' => [ 'shape' => 'CollectorResponses', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeFleetAdvisorDatabasesRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeFleetAdvisorDatabasesResponse' => [ 'type' => 'structure', 'members' => [ 'Databases' => [ 'shape' => 'DatabaseList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeFleetAdvisorLsaAnalysisRequest' => [ 'type' => 'structure', 'members' => [ 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeFleetAdvisorLsaAnalysisResponse' => [ 'type' => 'structure', 'members' => [ 'Analysis' => [ 'shape' => 'FleetAdvisorLsaAnalysisResponseList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeFleetAdvisorSchemaObjectSummaryRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeFleetAdvisorSchemaObjectSummaryResponse' => [ 'type' => 'structure', 'members' => [ 'FleetAdvisorSchemaObjects' => [ 'shape' => 'FleetAdvisorSchemaObjectList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeFleetAdvisorSchemasRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeFleetAdvisorSchemasResponse' => [ 'type' => 'structure', 'members' => [ 'FleetAdvisorSchemas' => [ 'shape' => 'FleetAdvisorSchemaList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeInstanceProfilesMessage' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeInstanceProfilesResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'InstanceProfiles' => [ 'shape' => 'InstanceProfileList', ], ], ], 'DescribeMetadataModelAssessmentsMessage' => [ 'type' => 'structure', 'required' => [ 'MigrationProjectIdentifier', ], 'members' => [ 'MigrationProjectIdentifier' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'Marker' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], ], ], 'DescribeMetadataModelAssessmentsResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'Requests' => [ 'shape' => 'SchemaConversionRequestList', ], ], ], 'DescribeMetadataModelConversionsMessage' => [ 'type' => 'structure', 'required' => [ 'MigrationProjectIdentifier', ], 'members' => [ 'MigrationProjectIdentifier' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'Marker' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], ], ], 'DescribeMetadataModelConversionsResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'Requests' => [ 'shape' => 'SchemaConversionRequestList', ], ], ], 'DescribeMetadataModelExportsAsScriptMessage' => [ 'type' => 'structure', 'required' => [ 'MigrationProjectIdentifier', ], 'members' => [ 'MigrationProjectIdentifier' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'Marker' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], ], ], 'DescribeMetadataModelExportsAsScriptResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'Requests' => [ 'shape' => 'SchemaConversionRequestList', ], ], ], 'DescribeMetadataModelExportsToTargetMessage' => [ 'type' => 'structure', 'required' => [ 'MigrationProjectIdentifier', ], 'members' => [ 'MigrationProjectIdentifier' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'Marker' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], ], ], 'DescribeMetadataModelExportsToTargetResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'Requests' => [ 'shape' => 'SchemaConversionRequestList', ], ], ], 'DescribeMetadataModelImportsMessage' => [ 'type' => 'structure', 'required' => [ 'MigrationProjectIdentifier', ], 'members' => [ 'MigrationProjectIdentifier' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'Marker' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], ], ], 'DescribeMetadataModelImportsResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'Requests' => [ 'shape' => 'SchemaConversionRequestList', ], ], ], 'DescribeMigrationProjectsMessage' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeMigrationProjectsResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'MigrationProjects' => [ 'shape' => 'MigrationProjectList', ], ], ], 'DescribeOrderableReplicationInstancesMessage' => [ 'type' => 'structure', 'members' => [ 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeOrderableReplicationInstancesResponse' => [ 'type' => 'structure', 'members' => [ 'OrderableReplicationInstances' => [ 'shape' => 'OrderableReplicationInstanceList', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribePendingMaintenanceActionsMessage' => [ 'type' => 'structure', 'members' => [ 'ReplicationInstanceArn' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'Marker' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], ], ], 'DescribePendingMaintenanceActionsResponse' => [ 'type' => 'structure', 'members' => [ 'PendingMaintenanceActions' => [ 'shape' => 'PendingMaintenanceActions', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeRecommendationLimitationsRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeRecommendationLimitationsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'Limitations' => [ 'shape' => 'LimitationList', ], ], ], 'DescribeRecommendationsRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeRecommendationsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'Recommendations' => [ 'shape' => 'RecommendationList', ], ], ], 'DescribeRefreshSchemasStatusMessage' => [ 'type' => 'structure', 'required' => [ 'EndpointArn', ], 'members' => [ 'EndpointArn' => [ 'shape' => 'String', ], ], ], 'DescribeRefreshSchemasStatusResponse' => [ 'type' => 'structure', 'members' => [ 'RefreshSchemasStatus' => [ 'shape' => 'RefreshSchemasStatus', ], ], ], 'DescribeReplicationConfigsMessage' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeReplicationConfigsResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'ReplicationConfigs' => [ 'shape' => 'ReplicationConfigList', ], ], ], 'DescribeReplicationInstanceTaskLogsMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationInstanceArn', ], 'members' => [ 'ReplicationInstanceArn' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeReplicationInstanceTaskLogsResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationInstanceArn' => [ 'shape' => 'String', ], 'ReplicationInstanceTaskLogs' => [ 'shape' => 'ReplicationInstanceTaskLogsList', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeReplicationInstancesMessage' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeReplicationInstancesResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'ReplicationInstances' => [ 'shape' => 'ReplicationInstanceList', ], ], ], 'DescribeReplicationSubnetGroupsMessage' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeReplicationSubnetGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'ReplicationSubnetGroups' => [ 'shape' => 'ReplicationSubnetGroups', ], ], ], 'DescribeReplicationTableStatisticsMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationConfigArn', ], 'members' => [ 'ReplicationConfigArn' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], ], ], 'DescribeReplicationTableStatisticsResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationConfigArn' => [ 'shape' => 'String', ], 'Marker' => [ 'shape' => 'String', ], 'ReplicationTableStatistics' => [ 'shape' => 'ReplicationTableStatisticsList', ], ], ], 'DescribeReplicationTaskAssessmentResultsMessage' => [ 'type' => 'structure', 'members' => [ 'ReplicationTaskArn' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeReplicationTaskAssessmentResultsResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'BucketName' => [ 'shape' => 'String', ], 'ReplicationTaskAssessmentResults' => [ 'shape' => 'ReplicationTaskAssessmentResultList', ], ], ], 'DescribeReplicationTaskAssessmentRunsMessage' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeReplicationTaskAssessmentRunsResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'ReplicationTaskAssessmentRuns' => [ 'shape' => 'ReplicationTaskAssessmentRunList', ], ], ], 'DescribeReplicationTaskIndividualAssessmentsMessage' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeReplicationTaskIndividualAssessmentsResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'ReplicationTaskIndividualAssessments' => [ 'shape' => 'ReplicationTaskIndividualAssessmentList', ], ], ], 'DescribeReplicationTasksMessage' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], 'WithoutSettings' => [ 'shape' => 'BooleanOptional', ], ], ], 'DescribeReplicationTasksResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'ReplicationTasks' => [ 'shape' => 'ReplicationTaskList', ], ], ], 'DescribeReplicationsMessage' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeReplicationsResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'Replications' => [ 'shape' => 'ReplicationList', ], ], ], 'DescribeSchemasMessage' => [ 'type' => 'structure', 'required' => [ 'EndpointArn', ], 'members' => [ 'EndpointArn' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeSchemasResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'Schemas' => [ 'shape' => 'SchemaList', ], ], ], 'DescribeTableStatisticsMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationTaskArn', ], 'members' => [ 'ReplicationTaskArn' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], ], ], 'DescribeTableStatisticsResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationTaskArn' => [ 'shape' => 'String', ], 'TableStatistics' => [ 'shape' => 'TableStatisticsList', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DmsSslModeValue' => [ 'type' => 'string', 'enum' => [ 'none', 'require', 'verify-ca', 'verify-full', ], ], 'DmsTransferSettings' => [ 'type' => 'structure', 'members' => [ 'ServiceAccessRoleArn' => [ 'shape' => 'String', ], 'BucketName' => [ 'shape' => 'String', ], ], ], 'DocDbDataProviderSettings' => [ 'type' => 'structure', 'members' => [ 'ServerName' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'DatabaseName' => [ 'shape' => 'String', ], 'SslMode' => [ 'shape' => 'DmsSslModeValue', ], 'CertificateArn' => [ 'shape' => 'String', ], ], ], 'DocDbSettings' => [ 'type' => 'structure', 'members' => [ 'Username' => [ 'shape' => 'String', ], 'Password' => [ 'shape' => 'SecretString', ], 'ServerName' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'DatabaseName' => [ 'shape' => 'String', ], 'NestingLevel' => [ 'shape' => 'NestingLevelValue', ], 'ExtractDocId' => [ 'shape' => 'BooleanOptional', ], 'DocsToInvestigate' => [ 'shape' => 'IntegerOptional', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'SecretsManagerAccessRoleArn' => [ 'shape' => 'String', ], 'SecretsManagerSecretId' => [ 'shape' => 'String', ], 'UseUpdateLookUp' => [ 'shape' => 'BooleanOptional', ], 'ReplicateShardCollections' => [ 'shape' => 'BooleanOptional', ], ], ], 'DoubleOptional' => [ 'type' => 'double', ], 'DynamoDbSettings' => [ 'type' => 'structure', 'required' => [ 'ServiceAccessRoleArn', ], 'members' => [ 'ServiceAccessRoleArn' => [ 'shape' => 'String', ], ], ], 'ElasticsearchSettings' => [ 'type' => 'structure', 'required' => [ 'ServiceAccessRoleArn', 'EndpointUri', ], 'members' => [ 'ServiceAccessRoleArn' => [ 'shape' => 'String', ], 'EndpointUri' => [ 'shape' => 'String', ], 'FullLoadErrorPercentage' => [ 'shape' => 'IntegerOptional', ], 'ErrorRetryDuration' => [ 'shape' => 'IntegerOptional', ], 'UseNewMappingType' => [ 'shape' => 'BooleanOptional', ], ], ], 'EncodingTypeValue' => [ 'type' => 'string', 'enum' => [ 'plain', 'plain-dictionary', 'rle-dictionary', ], ], 'EncryptionModeValue' => [ 'type' => 'string', 'enum' => [ 'sse-s3', 'sse-kms', ], ], 'Endpoint' => [ 'type' => 'structure', 'members' => [ 'EndpointIdentifier' => [ 'shape' => 'String', ], 'EndpointType' => [ 'shape' => 'ReplicationEndpointTypeValue', ], 'EngineName' => [ 'shape' => 'String', ], 'EngineDisplayName' => [ 'shape' => 'String', ], 'Username' => [ 'shape' => 'String', ], 'ServerName' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'DatabaseName' => [ 'shape' => 'String', ], 'ExtraConnectionAttributes' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'EndpointArn' => [ 'shape' => 'String', ], 'CertificateArn' => [ 'shape' => 'String', ], 'SslMode' => [ 'shape' => 'DmsSslModeValue', ], 'ServiceAccessRoleArn' => [ 'shape' => 'String', ], 'ExternalTableDefinition' => [ 'shape' => 'String', ], 'ExternalId' => [ 'shape' => 'String', ], 'DynamoDbSettings' => [ 'shape' => 'DynamoDbSettings', ], 'S3Settings' => [ 'shape' => 'S3Settings', ], 'DmsTransferSettings' => [ 'shape' => 'DmsTransferSettings', ], 'MongoDbSettings' => [ 'shape' => 'MongoDbSettings', ], 'KinesisSettings' => [ 'shape' => 'KinesisSettings', ], 'KafkaSettings' => [ 'shape' => 'KafkaSettings', ], 'ElasticsearchSettings' => [ 'shape' => 'ElasticsearchSettings', ], 'NeptuneSettings' => [ 'shape' => 'NeptuneSettings', ], 'RedshiftSettings' => [ 'shape' => 'RedshiftSettings', ], 'PostgreSQLSettings' => [ 'shape' => 'PostgreSQLSettings', ], 'MySQLSettings' => [ 'shape' => 'MySQLSettings', ], 'OracleSettings' => [ 'shape' => 'OracleSettings', ], 'SybaseSettings' => [ 'shape' => 'SybaseSettings', ], 'MicrosoftSQLServerSettings' => [ 'shape' => 'MicrosoftSQLServerSettings', ], 'IBMDb2Settings' => [ 'shape' => 'IBMDb2Settings', ], 'DocDbSettings' => [ 'shape' => 'DocDbSettings', ], 'RedisSettings' => [ 'shape' => 'RedisSettings', ], 'GcpMySQLSettings' => [ 'shape' => 'GcpMySQLSettings', ], 'TimestreamSettings' => [ 'shape' => 'TimestreamSettings', ], ], ], 'EndpointList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Endpoint', ], ], 'EndpointSetting' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Type' => [ 'shape' => 'EndpointSettingTypeValue', ], 'EnumValues' => [ 'shape' => 'EndpointSettingEnumValues', ], 'Sensitive' => [ 'shape' => 'BooleanOptional', ], 'Units' => [ 'shape' => 'String', ], 'Applicability' => [ 'shape' => 'String', ], 'IntValueMin' => [ 'shape' => 'IntegerOptional', ], 'IntValueMax' => [ 'shape' => 'IntegerOptional', ], 'DefaultValue' => [ 'shape' => 'String', ], ], ], 'EndpointSettingEnumValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'EndpointSettingTypeValue' => [ 'type' => 'string', 'enum' => [ 'string', 'boolean', 'integer', 'enum', ], ], 'EndpointSettingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EndpointSetting', ], ], 'EngineVersion' => [ 'type' => 'structure', 'members' => [ 'Version' => [ 'shape' => 'String', ], 'Lifecycle' => [ 'shape' => 'String', ], 'ReleaseStatus' => [ 'shape' => 'ReleaseStatusValues', ], 'LaunchDate' => [ 'shape' => 'TStamp', ], 'AutoUpgradeDate' => [ 'shape' => 'TStamp', ], 'DeprecationDate' => [ 'shape' => 'TStamp', ], 'ForceUpgradeDate' => [ 'shape' => 'TStamp', ], 'AvailableUpgrades' => [ 'shape' => 'AvailableUpgradesList', ], ], ], 'EngineVersionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EngineVersion', ], ], 'ErrorDetails' => [ 'type' => 'structure', 'members' => [ 'defaultErrorDetails' => [ 'shape' => 'DefaultErrorDetails', ], ], 'union' => true, ], 'Event' => [ 'type' => 'structure', 'members' => [ 'SourceIdentifier' => [ 'shape' => 'String', ], 'SourceType' => [ 'shape' => 'SourceType', ], 'Message' => [ 'shape' => 'String', ], 'EventCategories' => [ 'shape' => 'EventCategoriesList', ], 'Date' => [ 'shape' => 'TStamp', ], ], ], 'EventCategoriesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'EventCategoryGroup' => [ 'type' => 'structure', 'members' => [ 'SourceType' => [ 'shape' => 'String', ], 'EventCategories' => [ 'shape' => 'EventCategoriesList', ], ], ], 'EventCategoryGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventCategoryGroup', ], ], 'EventList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Event', ], ], 'EventSubscription' => [ 'type' => 'structure', 'members' => [ 'CustomerAwsId' => [ 'shape' => 'String', ], 'CustSubscriptionId' => [ 'shape' => 'String', ], 'SnsTopicArn' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'SubscriptionCreationTime' => [ 'shape' => 'String', ], 'SourceType' => [ 'shape' => 'String', ], 'SourceIdsList' => [ 'shape' => 'SourceIdsList', ], 'EventCategoriesList' => [ 'shape' => 'EventCategoriesList', ], 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'EventSubscriptionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventSubscription', ], ], 'ExceptionMessage' => [ 'type' => 'string', ], 'ExcludeTestList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ExportMetadataModelAssessmentMessage' => [ 'type' => 'structure', 'required' => [ 'MigrationProjectIdentifier', 'SelectionRules', ], 'members' => [ 'MigrationProjectIdentifier' => [ 'shape' => 'String', ], 'SelectionRules' => [ 'shape' => 'String', ], 'FileName' => [ 'shape' => 'String', ], 'AssessmentReportTypes' => [ 'shape' => 'AssessmentReportTypesList', ], ], ], 'ExportMetadataModelAssessmentResponse' => [ 'type' => 'structure', 'members' => [ 'PdfReport' => [ 'shape' => 'ExportMetadataModelAssessmentResultEntry', ], 'CsvReport' => [ 'shape' => 'ExportMetadataModelAssessmentResultEntry', ], ], ], 'ExportMetadataModelAssessmentResultEntry' => [ 'type' => 'structure', 'members' => [ 'S3ObjectKey' => [ 'shape' => 'String', ], 'ObjectURL' => [ 'shape' => 'String', ], ], ], 'ExportSqlDetails' => [ 'type' => 'structure', 'members' => [ 'S3ObjectKey' => [ 'shape' => 'String', ], 'ObjectURL' => [ 'shape' => 'String', ], ], ], 'Filter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Values', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Values' => [ 'shape' => 'FilterValueList', ], ], ], 'FilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], ], 'FilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'FleetAdvisorLsaAnalysisResponse' => [ 'type' => 'structure', 'members' => [ 'LsaAnalysisId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], ], ], 'FleetAdvisorLsaAnalysisResponseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FleetAdvisorLsaAnalysisResponse', ], ], 'FleetAdvisorSchemaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SchemaResponse', ], ], 'FleetAdvisorSchemaObjectList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FleetAdvisorSchemaObjectResponse', ], ], 'FleetAdvisorSchemaObjectResponse' => [ 'type' => 'structure', 'members' => [ 'SchemaId' => [ 'shape' => 'String', ], 'ObjectType' => [ 'shape' => 'String', ], 'NumberOfObjects' => [ 'shape' => 'LongOptional', ], 'CodeLineCount' => [ 'shape' => 'LongOptional', ], 'CodeSize' => [ 'shape' => 'LongOptional', ], ], ], 'GcpMySQLSettings' => [ 'type' => 'structure', 'members' => [ 'AfterConnectScript' => [ 'shape' => 'String', ], 'CleanSourceMetadataOnMismatch' => [ 'shape' => 'BooleanOptional', ], 'DatabaseName' => [ 'shape' => 'String', ], 'EventsPollInterval' => [ 'shape' => 'IntegerOptional', ], 'TargetDbType' => [ 'shape' => 'TargetDbType', ], 'MaxFileSize' => [ 'shape' => 'IntegerOptional', ], 'ParallelLoadThreads' => [ 'shape' => 'IntegerOptional', ], 'Password' => [ 'shape' => 'SecretString', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'ServerName' => [ 'shape' => 'String', ], 'ServerTimezone' => [ 'shape' => 'String', ], 'Username' => [ 'shape' => 'String', ], 'SecretsManagerAccessRoleArn' => [ 'shape' => 'String', ], 'SecretsManagerSecretId' => [ 'shape' => 'String', ], ], ], 'IBMDb2Settings' => [ 'type' => 'structure', 'members' => [ 'DatabaseName' => [ 'shape' => 'String', ], 'Password' => [ 'shape' => 'SecretString', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'ServerName' => [ 'shape' => 'String', ], 'SetDataCaptureChanges' => [ 'shape' => 'BooleanOptional', ], 'CurrentLsn' => [ 'shape' => 'String', ], 'MaxKBytesPerRead' => [ 'shape' => 'IntegerOptional', ], 'Username' => [ 'shape' => 'String', ], 'SecretsManagerAccessRoleArn' => [ 'shape' => 'String', ], 'SecretsManagerSecretId' => [ 'shape' => 'String', ], 'LoadTimeout' => [ 'shape' => 'IntegerOptional', ], 'WriteBufferSize' => [ 'shape' => 'IntegerOptional', ], 'MaxFileSize' => [ 'shape' => 'IntegerOptional', ], 'KeepCsvFiles' => [ 'shape' => 'BooleanOptional', ], ], ], 'ImportCertificateMessage' => [ 'type' => 'structure', 'required' => [ 'CertificateIdentifier', ], 'members' => [ 'CertificateIdentifier' => [ 'shape' => 'String', ], 'CertificatePem' => [ 'shape' => 'SecretString', ], 'CertificateWallet' => [ 'shape' => 'CertificateWallet', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ImportCertificateResponse' => [ 'type' => 'structure', 'members' => [ 'Certificate' => [ 'shape' => 'Certificate', ], ], ], 'IncludeTestList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'IndividualAssessmentNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'InstanceProfile' => [ 'type' => 'structure', 'members' => [ 'InstanceProfileArn' => [ 'shape' => 'String', ], 'AvailabilityZone' => [ 'shape' => 'String', ], 'KmsKeyArn' => [ 'shape' => 'String', ], 'PubliclyAccessible' => [ 'shape' => 'BooleanOptional', ], 'NetworkType' => [ 'shape' => 'String', ], 'InstanceProfileName' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'InstanceProfileCreationTime' => [ 'shape' => 'Iso8601DateTime', ], 'SubnetGroupIdentifier' => [ 'shape' => 'String', ], 'VpcSecurityGroups' => [ 'shape' => 'StringList', ], ], ], 'InstanceProfileList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceProfile', ], ], 'InsufficientResourceCapacityFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'Integer' => [ 'type' => 'integer', ], 'IntegerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Integer', ], ], 'IntegerOptional' => [ 'type' => 'integer', ], 'InvalidCertificateFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'InvalidOperationFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'InvalidResourceStateFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'InvalidSubnet' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'InventoryData' => [ 'type' => 'structure', 'members' => [ 'NumberOfDatabases' => [ 'shape' => 'IntegerOptional', ], 'NumberOfSchemas' => [ 'shape' => 'IntegerOptional', ], ], ], 'Iso8601DateTime' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'KMSAccessDeniedFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'KMSDisabledFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'KMSFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'KMSInvalidStateFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'KMSKeyNotAccessibleFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'KMSNotFoundFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'KMSThrottlingFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'KafkaSaslMechanism' => [ 'type' => 'string', 'enum' => [ 'scram-sha-512', 'plain', ], ], 'KafkaSecurityProtocol' => [ 'type' => 'string', 'enum' => [ 'plaintext', 'ssl-authentication', 'ssl-encryption', 'sasl-ssl', ], ], 'KafkaSettings' => [ 'type' => 'structure', 'members' => [ 'Broker' => [ 'shape' => 'String', ], 'Topic' => [ 'shape' => 'String', ], 'MessageFormat' => [ 'shape' => 'MessageFormatValue', ], 'IncludeTransactionDetails' => [ 'shape' => 'BooleanOptional', ], 'IncludePartitionValue' => [ 'shape' => 'BooleanOptional', ], 'PartitionIncludeSchemaTable' => [ 'shape' => 'BooleanOptional', ], 'IncludeTableAlterOperations' => [ 'shape' => 'BooleanOptional', ], 'IncludeControlDetails' => [ 'shape' => 'BooleanOptional', ], 'MessageMaxBytes' => [ 'shape' => 'IntegerOptional', ], 'IncludeNullAndEmpty' => [ 'shape' => 'BooleanOptional', ], 'SecurityProtocol' => [ 'shape' => 'KafkaSecurityProtocol', ], 'SslClientCertificateArn' => [ 'shape' => 'String', ], 'SslClientKeyArn' => [ 'shape' => 'String', ], 'SslClientKeyPassword' => [ 'shape' => 'SecretString', ], 'SslCaCertificateArn' => [ 'shape' => 'String', ], 'SaslUsername' => [ 'shape' => 'String', ], 'SaslPassword' => [ 'shape' => 'SecretString', ], 'NoHexPrefix' => [ 'shape' => 'BooleanOptional', ], 'SaslMechanism' => [ 'shape' => 'KafkaSaslMechanism', ], 'SslEndpointIdentificationAlgorithm' => [ 'shape' => 'KafkaSslEndpointIdentificationAlgorithm', ], ], ], 'KafkaSslEndpointIdentificationAlgorithm' => [ 'type' => 'string', 'enum' => [ 'none', 'https', ], ], 'KeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'KinesisSettings' => [ 'type' => 'structure', 'members' => [ 'StreamArn' => [ 'shape' => 'String', ], 'MessageFormat' => [ 'shape' => 'MessageFormatValue', ], 'ServiceAccessRoleArn' => [ 'shape' => 'String', ], 'IncludeTransactionDetails' => [ 'shape' => 'BooleanOptional', ], 'IncludePartitionValue' => [ 'shape' => 'BooleanOptional', ], 'PartitionIncludeSchemaTable' => [ 'shape' => 'BooleanOptional', ], 'IncludeTableAlterOperations' => [ 'shape' => 'BooleanOptional', ], 'IncludeControlDetails' => [ 'shape' => 'BooleanOptional', ], 'IncludeNullAndEmpty' => [ 'shape' => 'BooleanOptional', ], 'NoHexPrefix' => [ 'shape' => 'BooleanOptional', ], ], ], 'Limitation' => [ 'type' => 'structure', 'members' => [ 'DatabaseId' => [ 'shape' => 'String', ], 'EngineName' => [ 'shape' => 'String', ], 'Name' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'Impact' => [ 'shape' => 'String', ], 'Type' => [ 'shape' => 'String', ], ], ], 'LimitationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Limitation', ], ], 'ListTagsForResourceMessage' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => 'String', ], 'ResourceArnList' => [ 'shape' => 'ArnList', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'TagList' => [ 'shape' => 'TagList', ], ], ], 'Long' => [ 'type' => 'long', ], 'LongOptional' => [ 'type' => 'long', ], 'LongVarcharMappingType' => [ 'type' => 'string', 'enum' => [ 'wstring', 'clob', 'nclob', ], ], 'MariaDbDataProviderSettings' => [ 'type' => 'structure', 'members' => [ 'ServerName' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'SslMode' => [ 'shape' => 'DmsSslModeValue', ], 'CertificateArn' => [ 'shape' => 'String', ], ], ], 'MessageFormatValue' => [ 'type' => 'string', 'enum' => [ 'json', 'json-unformatted', ], ], 'MicrosoftSQLServerSettings' => [ 'type' => 'structure', 'members' => [ 'Port' => [ 'shape' => 'IntegerOptional', ], 'BcpPacketSize' => [ 'shape' => 'IntegerOptional', ], 'DatabaseName' => [ 'shape' => 'String', ], 'ControlTablesFileGroup' => [ 'shape' => 'String', ], 'Password' => [ 'shape' => 'SecretString', ], 'QuerySingleAlwaysOnNode' => [ 'shape' => 'BooleanOptional', ], 'ReadBackupOnly' => [ 'shape' => 'BooleanOptional', ], 'SafeguardPolicy' => [ 'shape' => 'SafeguardPolicy', ], 'ServerName' => [ 'shape' => 'String', ], 'Username' => [ 'shape' => 'String', ], 'UseBcpFullLoad' => [ 'shape' => 'BooleanOptional', ], 'UseThirdPartyBackupDevice' => [ 'shape' => 'BooleanOptional', ], 'SecretsManagerAccessRoleArn' => [ 'shape' => 'String', ], 'SecretsManagerSecretId' => [ 'shape' => 'String', ], 'TrimSpaceInChar' => [ 'shape' => 'BooleanOptional', ], 'TlogAccessMode' => [ 'shape' => 'TlogAccessMode', ], 'ForceLobLookup' => [ 'shape' => 'BooleanOptional', ], ], ], 'MicrosoftSqlServerDataProviderSettings' => [ 'type' => 'structure', 'members' => [ 'ServerName' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'DatabaseName' => [ 'shape' => 'String', ], 'SslMode' => [ 'shape' => 'DmsSslModeValue', ], 'CertificateArn' => [ 'shape' => 'String', ], ], ], 'MigrationProject' => [ 'type' => 'structure', 'members' => [ 'MigrationProjectName' => [ 'shape' => 'String', ], 'MigrationProjectArn' => [ 'shape' => 'String', ], 'MigrationProjectCreationTime' => [ 'shape' => 'Iso8601DateTime', ], 'SourceDataProviderDescriptors' => [ 'shape' => 'DataProviderDescriptorList', ], 'TargetDataProviderDescriptors' => [ 'shape' => 'DataProviderDescriptorList', ], 'InstanceProfileArn' => [ 'shape' => 'String', ], 'InstanceProfileName' => [ 'shape' => 'String', ], 'TransformationRules' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'SchemaConversionApplicationAttributes' => [ 'shape' => 'SCApplicationAttributes', ], ], ], 'MigrationProjectList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MigrationProject', ], ], 'MigrationTypeValue' => [ 'type' => 'string', 'enum' => [ 'full-load', 'cdc', 'full-load-and-cdc', ], ], 'ModifyConversionConfigurationMessage' => [ 'type' => 'structure', 'required' => [ 'MigrationProjectIdentifier', 'ConversionConfiguration', ], 'members' => [ 'MigrationProjectIdentifier' => [ 'shape' => 'String', ], 'ConversionConfiguration' => [ 'shape' => 'String', ], ], ], 'ModifyConversionConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'MigrationProjectIdentifier' => [ 'shape' => 'String', ], ], ], 'ModifyDataProviderMessage' => [ 'type' => 'structure', 'required' => [ 'DataProviderIdentifier', ], 'members' => [ 'DataProviderIdentifier' => [ 'shape' => 'String', ], 'DataProviderName' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'String', ], 'ExactSettings' => [ 'shape' => 'BooleanOptional', ], 'Settings' => [ 'shape' => 'DataProviderSettings', ], ], ], 'ModifyDataProviderResponse' => [ 'type' => 'structure', 'members' => [ 'DataProvider' => [ 'shape' => 'DataProvider', ], ], ], 'ModifyEndpointMessage' => [ 'type' => 'structure', 'required' => [ 'EndpointArn', ], 'members' => [ 'EndpointArn' => [ 'shape' => 'String', ], 'EndpointIdentifier' => [ 'shape' => 'String', ], 'EndpointType' => [ 'shape' => 'ReplicationEndpointTypeValue', ], 'EngineName' => [ 'shape' => 'String', ], 'Username' => [ 'shape' => 'String', ], 'Password' => [ 'shape' => 'SecretString', ], 'ServerName' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'DatabaseName' => [ 'shape' => 'String', ], 'ExtraConnectionAttributes' => [ 'shape' => 'String', ], 'CertificateArn' => [ 'shape' => 'String', ], 'SslMode' => [ 'shape' => 'DmsSslModeValue', ], 'ServiceAccessRoleArn' => [ 'shape' => 'String', ], 'ExternalTableDefinition' => [ 'shape' => 'String', ], 'DynamoDbSettings' => [ 'shape' => 'DynamoDbSettings', ], 'S3Settings' => [ 'shape' => 'S3Settings', ], 'DmsTransferSettings' => [ 'shape' => 'DmsTransferSettings', ], 'MongoDbSettings' => [ 'shape' => 'MongoDbSettings', ], 'KinesisSettings' => [ 'shape' => 'KinesisSettings', ], 'KafkaSettings' => [ 'shape' => 'KafkaSettings', ], 'ElasticsearchSettings' => [ 'shape' => 'ElasticsearchSettings', ], 'NeptuneSettings' => [ 'shape' => 'NeptuneSettings', ], 'RedshiftSettings' => [ 'shape' => 'RedshiftSettings', ], 'PostgreSQLSettings' => [ 'shape' => 'PostgreSQLSettings', ], 'MySQLSettings' => [ 'shape' => 'MySQLSettings', ], 'OracleSettings' => [ 'shape' => 'OracleSettings', ], 'SybaseSettings' => [ 'shape' => 'SybaseSettings', ], 'MicrosoftSQLServerSettings' => [ 'shape' => 'MicrosoftSQLServerSettings', ], 'IBMDb2Settings' => [ 'shape' => 'IBMDb2Settings', ], 'DocDbSettings' => [ 'shape' => 'DocDbSettings', ], 'RedisSettings' => [ 'shape' => 'RedisSettings', ], 'ExactSettings' => [ 'shape' => 'BooleanOptional', ], 'GcpMySQLSettings' => [ 'shape' => 'GcpMySQLSettings', ], 'TimestreamSettings' => [ 'shape' => 'TimestreamSettings', ], ], ], 'ModifyEndpointResponse' => [ 'type' => 'structure', 'members' => [ 'Endpoint' => [ 'shape' => 'Endpoint', ], ], ], 'ModifyEventSubscriptionMessage' => [ 'type' => 'structure', 'required' => [ 'SubscriptionName', ], 'members' => [ 'SubscriptionName' => [ 'shape' => 'String', ], 'SnsTopicArn' => [ 'shape' => 'String', ], 'SourceType' => [ 'shape' => 'String', ], 'EventCategories' => [ 'shape' => 'EventCategoriesList', ], 'Enabled' => [ 'shape' => 'BooleanOptional', ], ], ], 'ModifyEventSubscriptionResponse' => [ 'type' => 'structure', 'members' => [ 'EventSubscription' => [ 'shape' => 'EventSubscription', ], ], ], 'ModifyInstanceProfileMessage' => [ 'type' => 'structure', 'required' => [ 'InstanceProfileIdentifier', ], 'members' => [ 'InstanceProfileIdentifier' => [ 'shape' => 'String', ], 'AvailabilityZone' => [ 'shape' => 'String', ], 'KmsKeyArn' => [ 'shape' => 'String', ], 'PubliclyAccessible' => [ 'shape' => 'BooleanOptional', ], 'NetworkType' => [ 'shape' => 'String', ], 'InstanceProfileName' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'SubnetGroupIdentifier' => [ 'shape' => 'String', ], 'VpcSecurityGroups' => [ 'shape' => 'StringList', ], ], ], 'ModifyInstanceProfileResponse' => [ 'type' => 'structure', 'members' => [ 'InstanceProfile' => [ 'shape' => 'InstanceProfile', ], ], ], 'ModifyMigrationProjectMessage' => [ 'type' => 'structure', 'required' => [ 'MigrationProjectIdentifier', ], 'members' => [ 'MigrationProjectIdentifier' => [ 'shape' => 'String', ], 'MigrationProjectName' => [ 'shape' => 'String', ], 'SourceDataProviderDescriptors' => [ 'shape' => 'DataProviderDescriptorDefinitionList', ], 'TargetDataProviderDescriptors' => [ 'shape' => 'DataProviderDescriptorDefinitionList', ], 'InstanceProfileIdentifier' => [ 'shape' => 'String', ], 'TransformationRules' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'SchemaConversionApplicationAttributes' => [ 'shape' => 'SCApplicationAttributes', ], ], ], 'ModifyMigrationProjectResponse' => [ 'type' => 'structure', 'members' => [ 'MigrationProject' => [ 'shape' => 'MigrationProject', ], ], ], 'ModifyReplicationConfigMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationConfigArn', ], 'members' => [ 'ReplicationConfigArn' => [ 'shape' => 'String', ], 'ReplicationConfigIdentifier' => [ 'shape' => 'String', ], 'ReplicationType' => [ 'shape' => 'MigrationTypeValue', ], 'TableMappings' => [ 'shape' => 'String', ], 'ReplicationSettings' => [ 'shape' => 'String', ], 'SupplementalSettings' => [ 'shape' => 'String', ], 'ComputeConfig' => [ 'shape' => 'ComputeConfig', ], 'SourceEndpointArn' => [ 'shape' => 'String', ], 'TargetEndpointArn' => [ 'shape' => 'String', ], ], ], 'ModifyReplicationConfigResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationConfig' => [ 'shape' => 'ReplicationConfig', ], ], ], 'ModifyReplicationInstanceMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationInstanceArn', ], 'members' => [ 'ReplicationInstanceArn' => [ 'shape' => 'String', ], 'AllocatedStorage' => [ 'shape' => 'IntegerOptional', ], 'ApplyImmediately' => [ 'shape' => 'Boolean', ], 'ReplicationInstanceClass' => [ 'shape' => 'String', ], 'VpcSecurityGroupIds' => [ 'shape' => 'VpcSecurityGroupIdList', ], 'PreferredMaintenanceWindow' => [ 'shape' => 'String', ], 'MultiAZ' => [ 'shape' => 'BooleanOptional', ], 'EngineVersion' => [ 'shape' => 'String', ], 'AllowMajorVersionUpgrade' => [ 'shape' => 'Boolean', ], 'AutoMinorVersionUpgrade' => [ 'shape' => 'BooleanOptional', ], 'ReplicationInstanceIdentifier' => [ 'shape' => 'String', ], 'NetworkType' => [ 'shape' => 'String', ], ], ], 'ModifyReplicationInstanceResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationInstance' => [ 'shape' => 'ReplicationInstance', ], ], ], 'ModifyReplicationSubnetGroupMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationSubnetGroupIdentifier', 'SubnetIds', ], 'members' => [ 'ReplicationSubnetGroupIdentifier' => [ 'shape' => 'String', ], 'ReplicationSubnetGroupDescription' => [ 'shape' => 'String', ], 'SubnetIds' => [ 'shape' => 'SubnetIdentifierList', ], ], ], 'ModifyReplicationSubnetGroupResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationSubnetGroup' => [ 'shape' => 'ReplicationSubnetGroup', ], ], ], 'ModifyReplicationTaskMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationTaskArn', ], 'members' => [ 'ReplicationTaskArn' => [ 'shape' => 'String', ], 'ReplicationTaskIdentifier' => [ 'shape' => 'String', ], 'MigrationType' => [ 'shape' => 'MigrationTypeValue', ], 'TableMappings' => [ 'shape' => 'String', ], 'ReplicationTaskSettings' => [ 'shape' => 'String', ], 'CdcStartTime' => [ 'shape' => 'TStamp', ], 'CdcStartPosition' => [ 'shape' => 'String', ], 'CdcStopPosition' => [ 'shape' => 'String', ], 'TaskData' => [ 'shape' => 'String', ], ], ], 'ModifyReplicationTaskResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationTask' => [ 'shape' => 'ReplicationTask', ], ], ], 'MongoDbDataProviderSettings' => [ 'type' => 'structure', 'members' => [ 'ServerName' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'DatabaseName' => [ 'shape' => 'String', ], 'SslMode' => [ 'shape' => 'DmsSslModeValue', ], 'CertificateArn' => [ 'shape' => 'String', ], 'AuthType' => [ 'shape' => 'AuthTypeValue', ], 'AuthSource' => [ 'shape' => 'String', ], 'AuthMechanism' => [ 'shape' => 'AuthMechanismValue', ], ], ], 'MongoDbSettings' => [ 'type' => 'structure', 'members' => [ 'Username' => [ 'shape' => 'String', ], 'Password' => [ 'shape' => 'SecretString', ], 'ServerName' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'DatabaseName' => [ 'shape' => 'String', ], 'AuthType' => [ 'shape' => 'AuthTypeValue', ], 'AuthMechanism' => [ 'shape' => 'AuthMechanismValue', ], 'NestingLevel' => [ 'shape' => 'NestingLevelValue', ], 'ExtractDocId' => [ 'shape' => 'String', ], 'DocsToInvestigate' => [ 'shape' => 'String', ], 'AuthSource' => [ 'shape' => 'String', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'SecretsManagerAccessRoleArn' => [ 'shape' => 'String', ], 'SecretsManagerSecretId' => [ 'shape' => 'String', ], 'UseUpdateLookUp' => [ 'shape' => 'BooleanOptional', ], 'ReplicateShardCollections' => [ 'shape' => 'BooleanOptional', ], ], ], 'MoveReplicationTaskMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationTaskArn', 'TargetReplicationInstanceArn', ], 'members' => [ 'ReplicationTaskArn' => [ 'shape' => 'String', ], 'TargetReplicationInstanceArn' => [ 'shape' => 'String', ], ], ], 'MoveReplicationTaskResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationTask' => [ 'shape' => 'ReplicationTask', ], ], ], 'MySQLSettings' => [ 'type' => 'structure', 'members' => [ 'AfterConnectScript' => [ 'shape' => 'String', ], 'CleanSourceMetadataOnMismatch' => [ 'shape' => 'BooleanOptional', ], 'DatabaseName' => [ 'shape' => 'String', ], 'EventsPollInterval' => [ 'shape' => 'IntegerOptional', ], 'TargetDbType' => [ 'shape' => 'TargetDbType', ], 'MaxFileSize' => [ 'shape' => 'IntegerOptional', ], 'ParallelLoadThreads' => [ 'shape' => 'IntegerOptional', ], 'Password' => [ 'shape' => 'SecretString', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'ServerName' => [ 'shape' => 'String', ], 'ServerTimezone' => [ 'shape' => 'String', ], 'Username' => [ 'shape' => 'String', ], 'SecretsManagerAccessRoleArn' => [ 'shape' => 'String', ], 'SecretsManagerSecretId' => [ 'shape' => 'String', ], 'ExecuteTimeout' => [ 'shape' => 'IntegerOptional', ], ], ], 'MySqlDataProviderSettings' => [ 'type' => 'structure', 'members' => [ 'ServerName' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'SslMode' => [ 'shape' => 'DmsSslModeValue', ], 'CertificateArn' => [ 'shape' => 'String', ], ], ], 'NeptuneSettings' => [ 'type' => 'structure', 'required' => [ 'S3BucketName', 'S3BucketFolder', ], 'members' => [ 'ServiceAccessRoleArn' => [ 'shape' => 'String', ], 'S3BucketName' => [ 'shape' => 'String', ], 'S3BucketFolder' => [ 'shape' => 'String', ], 'ErrorRetryDuration' => [ 'shape' => 'IntegerOptional', ], 'MaxFileSize' => [ 'shape' => 'IntegerOptional', ], 'MaxRetryCount' => [ 'shape' => 'IntegerOptional', ], 'IamAuthEnabled' => [ 'shape' => 'BooleanOptional', ], ], ], 'NestingLevelValue' => [ 'type' => 'string', 'enum' => [ 'none', 'one', ], ], 'OracleDataProviderSettings' => [ 'type' => 'structure', 'members' => [ 'ServerName' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'DatabaseName' => [ 'shape' => 'String', ], 'SslMode' => [ 'shape' => 'DmsSslModeValue', ], 'CertificateArn' => [ 'shape' => 'String', ], 'AsmServer' => [ 'shape' => 'String', ], 'SecretsManagerOracleAsmSecretId' => [ 'shape' => 'String', ], 'SecretsManagerOracleAsmAccessRoleArn' => [ 'shape' => 'String', ], 'SecretsManagerSecurityDbEncryptionSecretId' => [ 'shape' => 'String', ], 'SecretsManagerSecurityDbEncryptionAccessRoleArn' => [ 'shape' => 'String', ], ], ], 'OracleSettings' => [ 'type' => 'structure', 'members' => [ 'AddSupplementalLogging' => [ 'shape' => 'BooleanOptional', ], 'ArchivedLogDestId' => [ 'shape' => 'IntegerOptional', ], 'AdditionalArchivedLogDestId' => [ 'shape' => 'IntegerOptional', ], 'ExtraArchivedLogDestIds' => [ 'shape' => 'IntegerList', ], 'AllowSelectNestedTables' => [ 'shape' => 'BooleanOptional', ], 'ParallelAsmReadThreads' => [ 'shape' => 'IntegerOptional', ], 'ReadAheadBlocks' => [ 'shape' => 'IntegerOptional', ], 'AccessAlternateDirectly' => [ 'shape' => 'BooleanOptional', ], 'UseAlternateFolderForOnline' => [ 'shape' => 'BooleanOptional', ], 'OraclePathPrefix' => [ 'shape' => 'String', ], 'UsePathPrefix' => [ 'shape' => 'String', ], 'ReplacePathPrefix' => [ 'shape' => 'BooleanOptional', ], 'EnableHomogenousTablespace' => [ 'shape' => 'BooleanOptional', ], 'DirectPathNoLog' => [ 'shape' => 'BooleanOptional', ], 'ArchivedLogsOnly' => [ 'shape' => 'BooleanOptional', ], 'AsmPassword' => [ 'shape' => 'SecretString', ], 'AsmServer' => [ 'shape' => 'String', ], 'AsmUser' => [ 'shape' => 'String', ], 'CharLengthSemantics' => [ 'shape' => 'CharLengthSemantics', ], 'DatabaseName' => [ 'shape' => 'String', ], 'DirectPathParallelLoad' => [ 'shape' => 'BooleanOptional', ], 'FailTasksOnLobTruncation' => [ 'shape' => 'BooleanOptional', ], 'NumberDatatypeScale' => [ 'shape' => 'IntegerOptional', ], 'Password' => [ 'shape' => 'SecretString', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'ReadTableSpaceName' => [ 'shape' => 'BooleanOptional', ], 'RetryInterval' => [ 'shape' => 'IntegerOptional', ], 'SecurityDbEncryption' => [ 'shape' => 'SecretString', ], 'SecurityDbEncryptionName' => [ 'shape' => 'String', ], 'ServerName' => [ 'shape' => 'String', ], 'SpatialDataOptionToGeoJsonFunctionName' => [ 'shape' => 'String', ], 'StandbyDelayTime' => [ 'shape' => 'IntegerOptional', ], 'Username' => [ 'shape' => 'String', ], 'UseBFile' => [ 'shape' => 'BooleanOptional', ], 'UseDirectPathFullLoad' => [ 'shape' => 'BooleanOptional', ], 'UseLogminerReader' => [ 'shape' => 'BooleanOptional', ], 'SecretsManagerAccessRoleArn' => [ 'shape' => 'String', ], 'SecretsManagerSecretId' => [ 'shape' => 'String', ], 'SecretsManagerOracleAsmAccessRoleArn' => [ 'shape' => 'String', ], 'SecretsManagerOracleAsmSecretId' => [ 'shape' => 'String', ], 'TrimSpaceInChar' => [ 'shape' => 'BooleanOptional', ], 'ConvertTimestampWithZoneToUTC' => [ 'shape' => 'BooleanOptional', ], 'OpenTransactionWindow' => [ 'shape' => 'IntegerOptional', ], ], ], 'OrderableReplicationInstance' => [ 'type' => 'structure', 'members' => [ 'EngineVersion' => [ 'shape' => 'String', ], 'ReplicationInstanceClass' => [ 'shape' => 'String', ], 'StorageType' => [ 'shape' => 'String', ], 'MinAllocatedStorage' => [ 'shape' => 'Integer', ], 'MaxAllocatedStorage' => [ 'shape' => 'Integer', ], 'DefaultAllocatedStorage' => [ 'shape' => 'Integer', ], 'IncludedAllocatedStorage' => [ 'shape' => 'Integer', ], 'AvailabilityZones' => [ 'shape' => 'AvailabilityZonesList', ], 'ReleaseStatus' => [ 'shape' => 'ReleaseStatusValues', ], ], ], 'OrderableReplicationInstanceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrderableReplicationInstance', ], ], 'OriginTypeValue' => [ 'type' => 'string', 'enum' => [ 'SOURCE', 'TARGET', ], ], 'ParquetVersionValue' => [ 'type' => 'string', 'enum' => [ 'parquet-1-0', 'parquet-2-0', ], ], 'PendingMaintenanceAction' => [ 'type' => 'structure', 'members' => [ 'Action' => [ 'shape' => 'String', ], 'AutoAppliedAfterDate' => [ 'shape' => 'TStamp', ], 'ForcedApplyDate' => [ 'shape' => 'TStamp', ], 'OptInStatus' => [ 'shape' => 'String', ], 'CurrentApplyDate' => [ 'shape' => 'TStamp', ], 'Description' => [ 'shape' => 'String', ], ], ], 'PendingMaintenanceActionDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'PendingMaintenanceAction', ], ], 'PendingMaintenanceActions' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourcePendingMaintenanceActions', ], ], 'PluginNameValue' => [ 'type' => 'string', 'enum' => [ 'no-preference', 'test-decoding', 'pglogical', ], ], 'PostgreSQLSettings' => [ 'type' => 'structure', 'members' => [ 'AfterConnectScript' => [ 'shape' => 'String', ], 'CaptureDdls' => [ 'shape' => 'BooleanOptional', ], 'MaxFileSize' => [ 'shape' => 'IntegerOptional', ], 'DatabaseName' => [ 'shape' => 'String', ], 'DdlArtifactsSchema' => [ 'shape' => 'String', ], 'ExecuteTimeout' => [ 'shape' => 'IntegerOptional', ], 'FailTasksOnLobTruncation' => [ 'shape' => 'BooleanOptional', ], 'HeartbeatEnable' => [ 'shape' => 'BooleanOptional', ], 'HeartbeatSchema' => [ 'shape' => 'String', ], 'HeartbeatFrequency' => [ 'shape' => 'IntegerOptional', ], 'Password' => [ 'shape' => 'SecretString', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'ServerName' => [ 'shape' => 'String', ], 'Username' => [ 'shape' => 'String', ], 'SlotName' => [ 'shape' => 'String', ], 'PluginName' => [ 'shape' => 'PluginNameValue', ], 'SecretsManagerAccessRoleArn' => [ 'shape' => 'String', ], 'SecretsManagerSecretId' => [ 'shape' => 'String', ], 'TrimSpaceInChar' => [ 'shape' => 'BooleanOptional', ], 'MapBooleanAsBoolean' => [ 'shape' => 'BooleanOptional', ], 'MapJsonbAsClob' => [ 'shape' => 'BooleanOptional', ], 'MapLongVarcharAs' => [ 'shape' => 'LongVarcharMappingType', ], 'DatabaseMode' => [ 'shape' => 'DatabaseMode', ], 'BabelfishDatabaseName' => [ 'shape' => 'String', ], ], ], 'PostgreSqlDataProviderSettings' => [ 'type' => 'structure', 'members' => [ 'ServerName' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'DatabaseName' => [ 'shape' => 'String', ], 'SslMode' => [ 'shape' => 'DmsSslModeValue', ], 'CertificateArn' => [ 'shape' => 'String', ], ], ], 'ProvisionData' => [ 'type' => 'structure', 'members' => [ 'ProvisionState' => [ 'shape' => 'String', ], 'ProvisionedCapacityUnits' => [ 'shape' => 'Integer', ], 'DateProvisioned' => [ 'shape' => 'TStamp', ], 'IsNewProvisioningAvailable' => [ 'shape' => 'Boolean', ], 'DateNewProvisioningDataAvailable' => [ 'shape' => 'TStamp', ], 'ReasonForNewProvisioningData' => [ 'shape' => 'String', ], ], ], 'RdsConfiguration' => [ 'type' => 'structure', 'members' => [ 'EngineEdition' => [ 'shape' => 'String', ], 'InstanceType' => [ 'shape' => 'String', ], 'InstanceVcpu' => [ 'shape' => 'DoubleOptional', ], 'InstanceMemory' => [ 'shape' => 'DoubleOptional', ], 'StorageType' => [ 'shape' => 'String', ], 'StorageSize' => [ 'shape' => 'IntegerOptional', ], 'StorageIops' => [ 'shape' => 'IntegerOptional', ], 'DeploymentOption' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], ], ], 'RdsRecommendation' => [ 'type' => 'structure', 'members' => [ 'RequirementsToTarget' => [ 'shape' => 'RdsRequirements', ], 'TargetConfiguration' => [ 'shape' => 'RdsConfiguration', ], ], ], 'RdsRequirements' => [ 'type' => 'structure', 'members' => [ 'EngineEdition' => [ 'shape' => 'String', ], 'InstanceVcpu' => [ 'shape' => 'DoubleOptional', ], 'InstanceMemory' => [ 'shape' => 'DoubleOptional', ], 'StorageSize' => [ 'shape' => 'IntegerOptional', ], 'StorageIops' => [ 'shape' => 'IntegerOptional', ], 'DeploymentOption' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], ], ], 'RebootReplicationInstanceMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationInstanceArn', ], 'members' => [ 'ReplicationInstanceArn' => [ 'shape' => 'String', ], 'ForceFailover' => [ 'shape' => 'BooleanOptional', ], 'ForcePlannedFailover' => [ 'shape' => 'BooleanOptional', ], ], ], 'RebootReplicationInstanceResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationInstance' => [ 'shape' => 'ReplicationInstance', ], ], ], 'Recommendation' => [ 'type' => 'structure', 'members' => [ 'DatabaseId' => [ 'shape' => 'String', ], 'EngineName' => [ 'shape' => 'String', ], 'CreatedDate' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'Preferred' => [ 'shape' => 'BooleanOptional', ], 'Settings' => [ 'shape' => 'RecommendationSettings', ], 'Data' => [ 'shape' => 'RecommendationData', ], ], ], 'RecommendationData' => [ 'type' => 'structure', 'members' => [ 'RdsEngine' => [ 'shape' => 'RdsRecommendation', ], ], ], 'RecommendationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Recommendation', ], ], 'RecommendationSettings' => [ 'type' => 'structure', 'required' => [ 'InstanceSizingType', 'WorkloadType', ], 'members' => [ 'InstanceSizingType' => [ 'shape' => 'String', ], 'WorkloadType' => [ 'shape' => 'String', ], ], ], 'RedisAuthTypeValue' => [ 'type' => 'string', 'enum' => [ 'none', 'auth-role', 'auth-token', ], ], 'RedisSettings' => [ 'type' => 'structure', 'required' => [ 'ServerName', 'Port', ], 'members' => [ 'ServerName' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'Integer', ], 'SslSecurityProtocol' => [ 'shape' => 'SslSecurityProtocolValue', ], 'AuthType' => [ 'shape' => 'RedisAuthTypeValue', ], 'AuthUserName' => [ 'shape' => 'String', ], 'AuthPassword' => [ 'shape' => 'SecretString', ], 'SslCaCertificateArn' => [ 'shape' => 'String', ], ], ], 'RedshiftDataProviderSettings' => [ 'type' => 'structure', 'members' => [ 'ServerName' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'DatabaseName' => [ 'shape' => 'String', ], ], ], 'RedshiftSettings' => [ 'type' => 'structure', 'members' => [ 'AcceptAnyDate' => [ 'shape' => 'BooleanOptional', ], 'AfterConnectScript' => [ 'shape' => 'String', ], 'BucketFolder' => [ 'shape' => 'String', ], 'BucketName' => [ 'shape' => 'String', ], 'CaseSensitiveNames' => [ 'shape' => 'BooleanOptional', ], 'CompUpdate' => [ 'shape' => 'BooleanOptional', ], 'ConnectionTimeout' => [ 'shape' => 'IntegerOptional', ], 'DatabaseName' => [ 'shape' => 'String', ], 'DateFormat' => [ 'shape' => 'String', ], 'EmptyAsNull' => [ 'shape' => 'BooleanOptional', ], 'EncryptionMode' => [ 'shape' => 'EncryptionModeValue', ], 'ExplicitIds' => [ 'shape' => 'BooleanOptional', ], 'FileTransferUploadStreams' => [ 'shape' => 'IntegerOptional', ], 'LoadTimeout' => [ 'shape' => 'IntegerOptional', ], 'MaxFileSize' => [ 'shape' => 'IntegerOptional', ], 'Password' => [ 'shape' => 'SecretString', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'RemoveQuotes' => [ 'shape' => 'BooleanOptional', ], 'ReplaceInvalidChars' => [ 'shape' => 'String', ], 'ReplaceChars' => [ 'shape' => 'String', ], 'ServerName' => [ 'shape' => 'String', ], 'ServiceAccessRoleArn' => [ 'shape' => 'String', ], 'ServerSideEncryptionKmsKeyId' => [ 'shape' => 'String', ], 'TimeFormat' => [ 'shape' => 'String', ], 'TrimBlanks' => [ 'shape' => 'BooleanOptional', ], 'TruncateColumns' => [ 'shape' => 'BooleanOptional', ], 'Username' => [ 'shape' => 'String', ], 'WriteBufferSize' => [ 'shape' => 'IntegerOptional', ], 'SecretsManagerAccessRoleArn' => [ 'shape' => 'String', ], 'SecretsManagerSecretId' => [ 'shape' => 'String', ], 'MapBooleanAsBoolean' => [ 'shape' => 'BooleanOptional', ], ], ], 'RefreshSchemasMessage' => [ 'type' => 'structure', 'required' => [ 'EndpointArn', 'ReplicationInstanceArn', ], 'members' => [ 'EndpointArn' => [ 'shape' => 'String', ], 'ReplicationInstanceArn' => [ 'shape' => 'String', ], ], ], 'RefreshSchemasResponse' => [ 'type' => 'structure', 'members' => [ 'RefreshSchemasStatus' => [ 'shape' => 'RefreshSchemasStatus', ], ], ], 'RefreshSchemasStatus' => [ 'type' => 'structure', 'members' => [ 'EndpointArn' => [ 'shape' => 'String', ], 'ReplicationInstanceArn' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'RefreshSchemasStatusTypeValue', ], 'LastRefreshDate' => [ 'shape' => 'TStamp', ], 'LastFailureMessage' => [ 'shape' => 'String', ], ], ], 'RefreshSchemasStatusTypeValue' => [ 'type' => 'string', 'enum' => [ 'successful', 'failed', 'refreshing', ], ], 'ReleaseStatusValues' => [ 'type' => 'string', 'enum' => [ 'beta', 'prod', ], ], 'ReloadOptionValue' => [ 'type' => 'string', 'enum' => [ 'data-reload', 'validate-only', ], ], 'ReloadReplicationTablesMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationConfigArn', 'TablesToReload', ], 'members' => [ 'ReplicationConfigArn' => [ 'shape' => 'String', ], 'TablesToReload' => [ 'shape' => 'TableListToReload', ], 'ReloadOption' => [ 'shape' => 'ReloadOptionValue', ], ], ], 'ReloadReplicationTablesResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationConfigArn' => [ 'shape' => 'String', ], ], ], 'ReloadTablesMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationTaskArn', 'TablesToReload', ], 'members' => [ 'ReplicationTaskArn' => [ 'shape' => 'String', ], 'TablesToReload' => [ 'shape' => 'TableListToReload', ], 'ReloadOption' => [ 'shape' => 'ReloadOptionValue', ], ], ], 'ReloadTablesResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationTaskArn' => [ 'shape' => 'String', ], ], ], 'RemoveTagsFromResourceMessage' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', ], 'TagKeys' => [ 'shape' => 'KeyList', ], ], ], 'RemoveTagsFromResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'Replication' => [ 'type' => 'structure', 'members' => [ 'ReplicationConfigIdentifier' => [ 'shape' => 'String', ], 'ReplicationConfigArn' => [ 'shape' => 'String', ], 'SourceEndpointArn' => [ 'shape' => 'String', ], 'TargetEndpointArn' => [ 'shape' => 'String', ], 'ReplicationType' => [ 'shape' => 'MigrationTypeValue', ], 'Status' => [ 'shape' => 'String', ], 'ProvisionData' => [ 'shape' => 'ProvisionData', ], 'StopReason' => [ 'shape' => 'String', ], 'FailureMessages' => [ 'shape' => 'StringList', ], 'ReplicationStats' => [ 'shape' => 'ReplicationStats', ], 'StartReplicationType' => [ 'shape' => 'String', ], 'CdcStartTime' => [ 'shape' => 'TStamp', ], 'CdcStartPosition' => [ 'shape' => 'String', ], 'CdcStopPosition' => [ 'shape' => 'String', ], 'RecoveryCheckpoint' => [ 'shape' => 'String', ], 'ReplicationCreateTime' => [ 'shape' => 'TStamp', ], 'ReplicationUpdateTime' => [ 'shape' => 'TStamp', ], 'ReplicationLastStopTime' => [ 'shape' => 'TStamp', ], 'ReplicationDeprovisionTime' => [ 'shape' => 'TStamp', ], ], ], 'ReplicationConfig' => [ 'type' => 'structure', 'members' => [ 'ReplicationConfigIdentifier' => [ 'shape' => 'String', ], 'ReplicationConfigArn' => [ 'shape' => 'String', ], 'SourceEndpointArn' => [ 'shape' => 'String', ], 'TargetEndpointArn' => [ 'shape' => 'String', ], 'ReplicationType' => [ 'shape' => 'MigrationTypeValue', ], 'ComputeConfig' => [ 'shape' => 'ComputeConfig', ], 'ReplicationSettings' => [ 'shape' => 'String', ], 'SupplementalSettings' => [ 'shape' => 'String', ], 'TableMappings' => [ 'shape' => 'String', ], 'ReplicationConfigCreateTime' => [ 'shape' => 'TStamp', ], 'ReplicationConfigUpdateTime' => [ 'shape' => 'TStamp', ], ], ], 'ReplicationConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationConfig', ], ], 'ReplicationEndpointTypeValue' => [ 'type' => 'string', 'enum' => [ 'source', 'target', ], ], 'ReplicationInstance' => [ 'type' => 'structure', 'members' => [ 'ReplicationInstanceIdentifier' => [ 'shape' => 'String', ], 'ReplicationInstanceClass' => [ 'shape' => 'String', ], 'ReplicationInstanceStatus' => [ 'shape' => 'String', ], 'AllocatedStorage' => [ 'shape' => 'Integer', ], 'InstanceCreateTime' => [ 'shape' => 'TStamp', ], 'VpcSecurityGroups' => [ 'shape' => 'VpcSecurityGroupMembershipList', ], 'AvailabilityZone' => [ 'shape' => 'String', ], 'ReplicationSubnetGroup' => [ 'shape' => 'ReplicationSubnetGroup', ], 'PreferredMaintenanceWindow' => [ 'shape' => 'String', ], 'PendingModifiedValues' => [ 'shape' => 'ReplicationPendingModifiedValues', ], 'MultiAZ' => [ 'shape' => 'Boolean', ], 'EngineVersion' => [ 'shape' => 'String', ], 'AutoMinorVersionUpgrade' => [ 'shape' => 'Boolean', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'ReplicationInstanceArn' => [ 'shape' => 'String', ], 'ReplicationInstancePublicIpAddress' => [ 'shape' => 'String', 'deprecated' => true, ], 'ReplicationInstancePrivateIpAddress' => [ 'shape' => 'String', 'deprecated' => true, ], 'ReplicationInstancePublicIpAddresses' => [ 'shape' => 'ReplicationInstancePublicIpAddressList', ], 'ReplicationInstancePrivateIpAddresses' => [ 'shape' => 'ReplicationInstancePrivateIpAddressList', ], 'ReplicationInstanceIpv6Addresses' => [ 'shape' => 'ReplicationInstanceIpv6AddressList', ], 'PubliclyAccessible' => [ 'shape' => 'Boolean', ], 'SecondaryAvailabilityZone' => [ 'shape' => 'String', ], 'FreeUntil' => [ 'shape' => 'TStamp', ], 'DnsNameServers' => [ 'shape' => 'String', ], 'NetworkType' => [ 'shape' => 'String', ], ], ], 'ReplicationInstanceIpv6AddressList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ReplicationInstanceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationInstance', ], ], 'ReplicationInstancePrivateIpAddressList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ReplicationInstancePublicIpAddressList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ReplicationInstanceTaskLog' => [ 'type' => 'structure', 'members' => [ 'ReplicationTaskName' => [ 'shape' => 'String', ], 'ReplicationTaskArn' => [ 'shape' => 'String', ], 'ReplicationInstanceTaskLogSize' => [ 'shape' => 'Long', ], ], ], 'ReplicationInstanceTaskLogsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationInstanceTaskLog', ], ], 'ReplicationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Replication', ], ], 'ReplicationPendingModifiedValues' => [ 'type' => 'structure', 'members' => [ 'ReplicationInstanceClass' => [ 'shape' => 'String', ], 'AllocatedStorage' => [ 'shape' => 'IntegerOptional', ], 'MultiAZ' => [ 'shape' => 'BooleanOptional', ], 'EngineVersion' => [ 'shape' => 'String', ], 'NetworkType' => [ 'shape' => 'String', ], ], ], 'ReplicationStats' => [ 'type' => 'structure', 'members' => [ 'FullLoadProgressPercent' => [ 'shape' => 'Integer', ], 'ElapsedTimeMillis' => [ 'shape' => 'Long', ], 'TablesLoaded' => [ 'shape' => 'Integer', ], 'TablesLoading' => [ 'shape' => 'Integer', ], 'TablesQueued' => [ 'shape' => 'Integer', ], 'TablesErrored' => [ 'shape' => 'Integer', ], 'FreshStartDate' => [ 'shape' => 'TStamp', ], 'StartDate' => [ 'shape' => 'TStamp', ], 'StopDate' => [ 'shape' => 'TStamp', ], 'FullLoadStartDate' => [ 'shape' => 'TStamp', ], 'FullLoadFinishDate' => [ 'shape' => 'TStamp', ], ], ], 'ReplicationSubnetGroup' => [ 'type' => 'structure', 'members' => [ 'ReplicationSubnetGroupIdentifier' => [ 'shape' => 'String', ], 'ReplicationSubnetGroupDescription' => [ 'shape' => 'String', ], 'VpcId' => [ 'shape' => 'String', ], 'SubnetGroupStatus' => [ 'shape' => 'String', ], 'Subnets' => [ 'shape' => 'SubnetList', ], 'SupportedNetworkTypes' => [ 'shape' => 'StringList', ], ], ], 'ReplicationSubnetGroupDoesNotCoverEnoughAZs' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'ReplicationSubnetGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationSubnetGroup', ], ], 'ReplicationTableStatisticsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TableStatistics', ], ], 'ReplicationTask' => [ 'type' => 'structure', 'members' => [ 'ReplicationTaskIdentifier' => [ 'shape' => 'String', ], 'SourceEndpointArn' => [ 'shape' => 'String', ], 'TargetEndpointArn' => [ 'shape' => 'String', ], 'ReplicationInstanceArn' => [ 'shape' => 'String', ], 'MigrationType' => [ 'shape' => 'MigrationTypeValue', ], 'TableMappings' => [ 'shape' => 'String', ], 'ReplicationTaskSettings' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'LastFailureMessage' => [ 'shape' => 'String', ], 'StopReason' => [ 'shape' => 'String', ], 'ReplicationTaskCreationDate' => [ 'shape' => 'TStamp', ], 'ReplicationTaskStartDate' => [ 'shape' => 'TStamp', ], 'CdcStartPosition' => [ 'shape' => 'String', ], 'CdcStopPosition' => [ 'shape' => 'String', ], 'RecoveryCheckpoint' => [ 'shape' => 'String', ], 'ReplicationTaskArn' => [ 'shape' => 'String', ], 'ReplicationTaskStats' => [ 'shape' => 'ReplicationTaskStats', ], 'TaskData' => [ 'shape' => 'String', ], 'TargetReplicationInstanceArn' => [ 'shape' => 'String', ], ], ], 'ReplicationTaskAssessmentResult' => [ 'type' => 'structure', 'members' => [ 'ReplicationTaskIdentifier' => [ 'shape' => 'String', ], 'ReplicationTaskArn' => [ 'shape' => 'String', ], 'ReplicationTaskLastAssessmentDate' => [ 'shape' => 'TStamp', ], 'AssessmentStatus' => [ 'shape' => 'String', ], 'AssessmentResultsFile' => [ 'shape' => 'String', ], 'AssessmentResults' => [ 'shape' => 'String', ], 'S3ObjectUrl' => [ 'shape' => 'String', ], ], ], 'ReplicationTaskAssessmentResultList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationTaskAssessmentResult', ], ], 'ReplicationTaskAssessmentRun' => [ 'type' => 'structure', 'members' => [ 'ReplicationTaskAssessmentRunArn' => [ 'shape' => 'String', ], 'ReplicationTaskArn' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'ReplicationTaskAssessmentRunCreationDate' => [ 'shape' => 'TStamp', ], 'AssessmentProgress' => [ 'shape' => 'ReplicationTaskAssessmentRunProgress', ], 'LastFailureMessage' => [ 'shape' => 'String', ], 'ServiceAccessRoleArn' => [ 'shape' => 'String', ], 'ResultLocationBucket' => [ 'shape' => 'String', ], 'ResultLocationFolder' => [ 'shape' => 'String', ], 'ResultEncryptionMode' => [ 'shape' => 'String', ], 'ResultKmsKeyArn' => [ 'shape' => 'String', ], 'AssessmentRunName' => [ 'shape' => 'String', ], ], ], 'ReplicationTaskAssessmentRunList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationTaskAssessmentRun', ], ], 'ReplicationTaskAssessmentRunProgress' => [ 'type' => 'structure', 'members' => [ 'IndividualAssessmentCount' => [ 'shape' => 'Integer', ], 'IndividualAssessmentCompletedCount' => [ 'shape' => 'Integer', ], ], ], 'ReplicationTaskIndividualAssessment' => [ 'type' => 'structure', 'members' => [ 'ReplicationTaskIndividualAssessmentArn' => [ 'shape' => 'String', ], 'ReplicationTaskAssessmentRunArn' => [ 'shape' => 'String', ], 'IndividualAssessmentName' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'ReplicationTaskIndividualAssessmentStartDate' => [ 'shape' => 'TStamp', ], ], ], 'ReplicationTaskIndividualAssessmentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationTaskIndividualAssessment', ], ], 'ReplicationTaskList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationTask', ], ], 'ReplicationTaskStats' => [ 'type' => 'structure', 'members' => [ 'FullLoadProgressPercent' => [ 'shape' => 'Integer', ], 'ElapsedTimeMillis' => [ 'shape' => 'Long', ], 'TablesLoaded' => [ 'shape' => 'Integer', ], 'TablesLoading' => [ 'shape' => 'Integer', ], 'TablesQueued' => [ 'shape' => 'Integer', ], 'TablesErrored' => [ 'shape' => 'Integer', ], 'FreshStartDate' => [ 'shape' => 'TStamp', ], 'StartDate' => [ 'shape' => 'TStamp', ], 'StopDate' => [ 'shape' => 'TStamp', ], 'FullLoadStartDate' => [ 'shape' => 'TStamp', ], 'FullLoadFinishDate' => [ 'shape' => 'TStamp', ], ], ], 'ResourceAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], 'resourceArn' => [ 'shape' => 'ResourceArn', ], ], 'exception' => true, ], 'ResourceArn' => [ 'type' => 'string', ], 'ResourceNotFoundFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'ResourcePendingMaintenanceActions' => [ 'type' => 'structure', 'members' => [ 'ResourceIdentifier' => [ 'shape' => 'String', ], 'PendingMaintenanceActionDetails' => [ 'shape' => 'PendingMaintenanceActionDetails', ], ], ], 'ResourceQuotaExceededFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'RunFleetAdvisorLsaAnalysisResponse' => [ 'type' => 'structure', 'members' => [ 'LsaAnalysisId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], ], ], 'S3AccessDeniedFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'S3ResourceNotFoundFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'S3Settings' => [ 'type' => 'structure', 'members' => [ 'ServiceAccessRoleArn' => [ 'shape' => 'String', ], 'ExternalTableDefinition' => [ 'shape' => 'String', ], 'CsvRowDelimiter' => [ 'shape' => 'String', ], 'CsvDelimiter' => [ 'shape' => 'String', ], 'BucketFolder' => [ 'shape' => 'String', ], 'BucketName' => [ 'shape' => 'String', ], 'CompressionType' => [ 'shape' => 'CompressionTypeValue', ], 'EncryptionMode' => [ 'shape' => 'EncryptionModeValue', ], 'ServerSideEncryptionKmsKeyId' => [ 'shape' => 'String', ], 'DataFormat' => [ 'shape' => 'DataFormatValue', ], 'EncodingType' => [ 'shape' => 'EncodingTypeValue', ], 'DictPageSizeLimit' => [ 'shape' => 'IntegerOptional', ], 'RowGroupLength' => [ 'shape' => 'IntegerOptional', ], 'DataPageSize' => [ 'shape' => 'IntegerOptional', ], 'ParquetVersion' => [ 'shape' => 'ParquetVersionValue', ], 'EnableStatistics' => [ 'shape' => 'BooleanOptional', ], 'IncludeOpForFullLoad' => [ 'shape' => 'BooleanOptional', ], 'CdcInsertsOnly' => [ 'shape' => 'BooleanOptional', ], 'TimestampColumnName' => [ 'shape' => 'String', ], 'ParquetTimestampInMillisecond' => [ 'shape' => 'BooleanOptional', ], 'CdcInsertsAndUpdates' => [ 'shape' => 'BooleanOptional', ], 'DatePartitionEnabled' => [ 'shape' => 'BooleanOptional', ], 'DatePartitionSequence' => [ 'shape' => 'DatePartitionSequenceValue', ], 'DatePartitionDelimiter' => [ 'shape' => 'DatePartitionDelimiterValue', ], 'UseCsvNoSupValue' => [ 'shape' => 'BooleanOptional', ], 'CsvNoSupValue' => [ 'shape' => 'String', ], 'PreserveTransactions' => [ 'shape' => 'BooleanOptional', ], 'CdcPath' => [ 'shape' => 'String', ], 'UseTaskStartTimeForFullLoadTimestamp' => [ 'shape' => 'BooleanOptional', ], 'CannedAclForObjects' => [ 'shape' => 'CannedAclForObjectsValue', ], 'AddColumnName' => [ 'shape' => 'BooleanOptional', ], 'CdcMaxBatchInterval' => [ 'shape' => 'IntegerOptional', ], 'CdcMinFileSize' => [ 'shape' => 'IntegerOptional', ], 'CsvNullValue' => [ 'shape' => 'String', ], 'IgnoreHeaderRows' => [ 'shape' => 'IntegerOptional', ], 'MaxFileSize' => [ 'shape' => 'IntegerOptional', ], 'Rfc4180' => [ 'shape' => 'BooleanOptional', ], 'DatePartitionTimezone' => [ 'shape' => 'String', ], 'AddTrailingPaddingCharacter' => [ 'shape' => 'BooleanOptional', ], 'ExpectedBucketOwner' => [ 'shape' => 'String', ], 'GlueCatalogGeneration' => [ 'shape' => 'BooleanOptional', ], ], ], 'SCApplicationAttributes' => [ 'type' => 'structure', 'members' => [ 'S3BucketPath' => [ 'shape' => 'String', ], 'S3BucketRoleArn' => [ 'shape' => 'String', ], ], ], 'SNSInvalidTopicFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'SNSNoAuthorizationFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'SafeguardPolicy' => [ 'type' => 'string', 'enum' => [ 'rely-on-sql-server-replication-agent', 'exclusive-automatic-truncation', 'shared-automatic-truncation', ], ], 'SchemaConversionRequest' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'String', ], 'RequestIdentifier' => [ 'shape' => 'String', ], 'MigrationProjectArn' => [ 'shape' => 'String', ], 'Error' => [ 'shape' => 'ErrorDetails', ], 'ExportSqlDetails' => [ 'shape' => 'ExportSqlDetails', ], ], ], 'SchemaConversionRequestList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SchemaConversionRequest', ], ], 'SchemaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'SchemaResponse' => [ 'type' => 'structure', 'members' => [ 'CodeLineCount' => [ 'shape' => 'LongOptional', ], 'CodeSize' => [ 'shape' => 'LongOptional', ], 'Complexity' => [ 'shape' => 'String', ], 'Server' => [ 'shape' => 'ServerShortInfoResponse', ], 'DatabaseInstance' => [ 'shape' => 'DatabaseShortInfoResponse', ], 'SchemaId' => [ 'shape' => 'String', ], 'SchemaName' => [ 'shape' => 'String', ], 'OriginalSchema' => [ 'shape' => 'SchemaShortInfoResponse', ], 'Similarity' => [ 'shape' => 'DoubleOptional', ], ], ], 'SchemaShortInfoResponse' => [ 'type' => 'structure', 'members' => [ 'SchemaId' => [ 'shape' => 'String', ], 'SchemaName' => [ 'shape' => 'String', ], 'DatabaseId' => [ 'shape' => 'String', ], 'DatabaseName' => [ 'shape' => 'String', ], 'DatabaseIpAddress' => [ 'shape' => 'String', ], ], ], 'SecretString' => [ 'type' => 'string', 'sensitive' => true, ], 'ServerShortInfoResponse' => [ 'type' => 'structure', 'members' => [ 'ServerId' => [ 'shape' => 'String', ], 'IpAddress' => [ 'shape' => 'String', ], 'ServerName' => [ 'shape' => 'String', ], ], ], 'SourceIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'SourceType' => [ 'type' => 'string', 'enum' => [ 'replication-instance', ], ], 'SslSecurityProtocolValue' => [ 'type' => 'string', 'enum' => [ 'plaintext', 'ssl-encryption', ], ], 'StartExtensionPackAssociationMessage' => [ 'type' => 'structure', 'required' => [ 'MigrationProjectIdentifier', ], 'members' => [ 'MigrationProjectIdentifier' => [ 'shape' => 'String', ], ], ], 'StartExtensionPackAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'RequestIdentifier' => [ 'shape' => 'String', ], ], ], 'StartMetadataModelAssessmentMessage' => [ 'type' => 'structure', 'required' => [ 'MigrationProjectIdentifier', 'SelectionRules', ], 'members' => [ 'MigrationProjectIdentifier' => [ 'shape' => 'String', ], 'SelectionRules' => [ 'shape' => 'String', ], ], ], 'StartMetadataModelAssessmentResponse' => [ 'type' => 'structure', 'members' => [ 'RequestIdentifier' => [ 'shape' => 'String', ], ], ], 'StartMetadataModelConversionMessage' => [ 'type' => 'structure', 'required' => [ 'MigrationProjectIdentifier', 'SelectionRules', ], 'members' => [ 'MigrationProjectIdentifier' => [ 'shape' => 'String', ], 'SelectionRules' => [ 'shape' => 'String', ], ], ], 'StartMetadataModelConversionResponse' => [ 'type' => 'structure', 'members' => [ 'RequestIdentifier' => [ 'shape' => 'String', ], ], ], 'StartMetadataModelExportAsScriptMessage' => [ 'type' => 'structure', 'required' => [ 'MigrationProjectIdentifier', 'SelectionRules', 'Origin', ], 'members' => [ 'MigrationProjectIdentifier' => [ 'shape' => 'String', ], 'SelectionRules' => [ 'shape' => 'String', ], 'Origin' => [ 'shape' => 'OriginTypeValue', ], 'FileName' => [ 'shape' => 'String', ], ], ], 'StartMetadataModelExportAsScriptResponse' => [ 'type' => 'structure', 'members' => [ 'RequestIdentifier' => [ 'shape' => 'String', ], ], ], 'StartMetadataModelExportToTargetMessage' => [ 'type' => 'structure', 'required' => [ 'MigrationProjectIdentifier', 'SelectionRules', ], 'members' => [ 'MigrationProjectIdentifier' => [ 'shape' => 'String', ], 'SelectionRules' => [ 'shape' => 'String', ], 'OverwriteExtensionPack' => [ 'shape' => 'BooleanOptional', ], ], ], 'StartMetadataModelExportToTargetResponse' => [ 'type' => 'structure', 'members' => [ 'RequestIdentifier' => [ 'shape' => 'String', ], ], ], 'StartMetadataModelImportMessage' => [ 'type' => 'structure', 'required' => [ 'MigrationProjectIdentifier', 'SelectionRules', 'Origin', ], 'members' => [ 'MigrationProjectIdentifier' => [ 'shape' => 'String', ], 'SelectionRules' => [ 'shape' => 'String', ], 'Origin' => [ 'shape' => 'OriginTypeValue', ], 'Refresh' => [ 'shape' => 'Boolean', ], ], ], 'StartMetadataModelImportResponse' => [ 'type' => 'structure', 'members' => [ 'RequestIdentifier' => [ 'shape' => 'String', ], ], ], 'StartRecommendationsRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseId', 'Settings', ], 'members' => [ 'DatabaseId' => [ 'shape' => 'String', ], 'Settings' => [ 'shape' => 'RecommendationSettings', ], ], ], 'StartRecommendationsRequestEntry' => [ 'type' => 'structure', 'required' => [ 'DatabaseId', 'Settings', ], 'members' => [ 'DatabaseId' => [ 'shape' => 'String', ], 'Settings' => [ 'shape' => 'RecommendationSettings', ], ], ], 'StartRecommendationsRequestEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StartRecommendationsRequestEntry', ], ], 'StartReplicationMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationConfigArn', 'StartReplicationType', ], 'members' => [ 'ReplicationConfigArn' => [ 'shape' => 'String', ], 'StartReplicationType' => [ 'shape' => 'String', ], 'CdcStartTime' => [ 'shape' => 'TStamp', ], 'CdcStartPosition' => [ 'shape' => 'String', ], 'CdcStopPosition' => [ 'shape' => 'String', ], ], ], 'StartReplicationResponse' => [ 'type' => 'structure', 'members' => [ 'Replication' => [ 'shape' => 'Replication', ], ], ], 'StartReplicationTaskAssessmentMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationTaskArn', ], 'members' => [ 'ReplicationTaskArn' => [ 'shape' => 'String', ], ], ], 'StartReplicationTaskAssessmentResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationTask' => [ 'shape' => 'ReplicationTask', ], ], ], 'StartReplicationTaskAssessmentRunMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationTaskArn', 'ServiceAccessRoleArn', 'ResultLocationBucket', 'AssessmentRunName', ], 'members' => [ 'ReplicationTaskArn' => [ 'shape' => 'String', ], 'ServiceAccessRoleArn' => [ 'shape' => 'String', ], 'ResultLocationBucket' => [ 'shape' => 'String', ], 'ResultLocationFolder' => [ 'shape' => 'String', ], 'ResultEncryptionMode' => [ 'shape' => 'String', ], 'ResultKmsKeyArn' => [ 'shape' => 'String', ], 'AssessmentRunName' => [ 'shape' => 'String', ], 'IncludeOnly' => [ 'shape' => 'IncludeTestList', ], 'Exclude' => [ 'shape' => 'ExcludeTestList', ], ], ], 'StartReplicationTaskAssessmentRunResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationTaskAssessmentRun' => [ 'shape' => 'ReplicationTaskAssessmentRun', ], ], ], 'StartReplicationTaskMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationTaskArn', 'StartReplicationTaskType', ], 'members' => [ 'ReplicationTaskArn' => [ 'shape' => 'String', ], 'StartReplicationTaskType' => [ 'shape' => 'StartReplicationTaskTypeValue', ], 'CdcStartTime' => [ 'shape' => 'TStamp', ], 'CdcStartPosition' => [ 'shape' => 'String', ], 'CdcStopPosition' => [ 'shape' => 'String', ], ], ], 'StartReplicationTaskResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationTask' => [ 'shape' => 'ReplicationTask', ], ], ], 'StartReplicationTaskTypeValue' => [ 'type' => 'string', 'enum' => [ 'start-replication', 'resume-processing', 'reload-target', ], ], 'StopReplicationMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationConfigArn', ], 'members' => [ 'ReplicationConfigArn' => [ 'shape' => 'String', ], ], ], 'StopReplicationResponse' => [ 'type' => 'structure', 'members' => [ 'Replication' => [ 'shape' => 'Replication', ], ], ], 'StopReplicationTaskMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationTaskArn', ], 'members' => [ 'ReplicationTaskArn' => [ 'shape' => 'String', ], ], ], 'StopReplicationTaskResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationTask' => [ 'shape' => 'ReplicationTask', ], ], ], 'StorageQuotaExceededFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'String' => [ 'type' => 'string', ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Subnet' => [ 'type' => 'structure', 'members' => [ 'SubnetIdentifier' => [ 'shape' => 'String', ], 'SubnetAvailabilityZone' => [ 'shape' => 'AvailabilityZone', ], 'SubnetStatus' => [ 'shape' => 'String', ], ], ], 'SubnetAlreadyInUse' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'SubnetIdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'SubnetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Subnet', ], ], 'SupportedEndpointType' => [ 'type' => 'structure', 'members' => [ 'EngineName' => [ 'shape' => 'String', ], 'SupportsCDC' => [ 'shape' => 'Boolean', ], 'EndpointType' => [ 'shape' => 'ReplicationEndpointTypeValue', ], 'ReplicationInstanceEngineMinimumVersion' => [ 'shape' => 'String', ], 'EngineDisplayName' => [ 'shape' => 'String', ], ], ], 'SupportedEndpointTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SupportedEndpointType', ], ], 'SybaseSettings' => [ 'type' => 'structure', 'members' => [ 'DatabaseName' => [ 'shape' => 'String', ], 'Password' => [ 'shape' => 'SecretString', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'ServerName' => [ 'shape' => 'String', ], 'Username' => [ 'shape' => 'String', ], 'SecretsManagerAccessRoleArn' => [ 'shape' => 'String', ], 'SecretsManagerSecretId' => [ 'shape' => 'String', ], ], ], 'TStamp' => [ 'type' => 'timestamp', ], 'TableListToReload' => [ 'type' => 'list', 'member' => [ 'shape' => 'TableToReload', ], ], 'TableStatistics' => [ 'type' => 'structure', 'members' => [ 'SchemaName' => [ 'shape' => 'String', ], 'TableName' => [ 'shape' => 'String', ], 'Inserts' => [ 'shape' => 'Long', ], 'Deletes' => [ 'shape' => 'Long', ], 'Updates' => [ 'shape' => 'Long', ], 'Ddls' => [ 'shape' => 'Long', ], 'AppliedInserts' => [ 'shape' => 'LongOptional', ], 'AppliedDeletes' => [ 'shape' => 'LongOptional', ], 'AppliedUpdates' => [ 'shape' => 'LongOptional', ], 'AppliedDdls' => [ 'shape' => 'LongOptional', ], 'FullLoadRows' => [ 'shape' => 'Long', ], 'FullLoadCondtnlChkFailedRows' => [ 'shape' => 'Long', ], 'FullLoadErrorRows' => [ 'shape' => 'Long', ], 'FullLoadStartTime' => [ 'shape' => 'TStamp', ], 'FullLoadEndTime' => [ 'shape' => 'TStamp', ], 'FullLoadReloaded' => [ 'shape' => 'BooleanOptional', ], 'LastUpdateTime' => [ 'shape' => 'TStamp', ], 'TableState' => [ 'shape' => 'String', ], 'ValidationPendingRecords' => [ 'shape' => 'Long', ], 'ValidationFailedRecords' => [ 'shape' => 'Long', ], 'ValidationSuspendedRecords' => [ 'shape' => 'Long', ], 'ValidationState' => [ 'shape' => 'String', ], 'ValidationStateDetails' => [ 'shape' => 'String', ], ], ], 'TableStatisticsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TableStatistics', ], ], 'TableToReload' => [ 'type' => 'structure', 'required' => [ 'SchemaName', 'TableName', ], 'members' => [ 'SchemaName' => [ 'shape' => 'String', ], 'TableName' => [ 'shape' => 'String', ], ], ], 'Tag' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'String', ], 'ResourceArn' => [ 'shape' => 'String', ], ], ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'TargetDbType' => [ 'type' => 'string', 'enum' => [ 'specific-database', 'multiple-databases', ], ], 'TestConnectionMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationInstanceArn', 'EndpointArn', ], 'members' => [ 'ReplicationInstanceArn' => [ 'shape' => 'String', ], 'EndpointArn' => [ 'shape' => 'String', ], ], ], 'TestConnectionResponse' => [ 'type' => 'structure', 'members' => [ 'Connection' => [ 'shape' => 'Connection', ], ], ], 'TimestreamSettings' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'MemoryDuration', 'MagneticDuration', ], 'members' => [ 'DatabaseName' => [ 'shape' => 'String', ], 'MemoryDuration' => [ 'shape' => 'IntegerOptional', ], 'MagneticDuration' => [ 'shape' => 'IntegerOptional', ], 'CdcInsertsAndUpdates' => [ 'shape' => 'BooleanOptional', ], 'EnableMagneticStoreWrites' => [ 'shape' => 'BooleanOptional', ], ], ], 'TlogAccessMode' => [ 'type' => 'string', 'enum' => [ 'BackupOnly', 'PreferBackup', 'PreferTlog', 'TlogOnly', ], ], 'UpdateSubscriptionsToEventBridgeMessage' => [ 'type' => 'structure', 'members' => [ 'ForceMove' => [ 'shape' => 'BooleanOptional', ], ], ], 'UpdateSubscriptionsToEventBridgeResponse' => [ 'type' => 'structure', 'members' => [ 'Result' => [ 'shape' => 'String', ], ], ], 'UpgradeDependencyFailureFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'VersionStatus' => [ 'type' => 'string', 'enum' => [ 'UP_TO_DATE', 'OUTDATED', 'UNSUPPORTED', ], ], 'VpcSecurityGroupIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'VpcSecurityGroupMembership' => [ 'type' => 'structure', 'members' => [ 'VpcSecurityGroupId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], ], ], 'VpcSecurityGroupMembershipList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VpcSecurityGroupMembership', ], ], ],];
