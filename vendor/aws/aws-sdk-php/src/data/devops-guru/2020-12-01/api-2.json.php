<?php
// This file was auto-generated from sdk-root/src/data/devops-guru/2020-12-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-12-01', 'endpointPrefix' => 'devops-guru', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'Amazon DevOps Guru', 'serviceId' => 'DevOps Guru', 'signatureVersion' => 'v4', 'signingName' => 'devops-guru', 'uid' => 'devops-guru-2020-12-01', ], 'operations' => [ 'AddNotificationChannel' => [ 'name' => 'AddNotificationChannel', 'http' => [ 'method' => 'PUT', 'requestUri' => '/channels', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AddNotificationChannelRequest', ], 'output' => [ 'shape' => 'AddNotificationChannelResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'DeleteInsight' => [ 'name' => 'DeleteInsight', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/insights/{Id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteInsightRequest', ], 'output' => [ 'shape' => 'DeleteInsightResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeAccountHealth' => [ 'name' => 'DescribeAccountHealth', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/health', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeAccountHealthRequest', ], 'output' => [ 'shape' => 'DescribeAccountHealthResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeAccountOverview' => [ 'name' => 'DescribeAccountOverview', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/overview', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeAccountOverviewRequest', ], 'output' => [ 'shape' => 'DescribeAccountOverviewResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeAnomaly' => [ 'name' => 'DescribeAnomaly', 'http' => [ 'method' => 'GET', 'requestUri' => '/anomalies/{Id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeAnomalyRequest', ], 'output' => [ 'shape' => 'DescribeAnomalyResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeEventSourcesConfig' => [ 'name' => 'DescribeEventSourcesConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/event-sources', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeEventSourcesConfigRequest', ], 'output' => [ 'shape' => 'DescribeEventSourcesConfigResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeFeedback' => [ 'name' => 'DescribeFeedback', 'http' => [ 'method' => 'POST', 'requestUri' => '/feedback', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeFeedbackRequest', ], 'output' => [ 'shape' => 'DescribeFeedbackResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeInsight' => [ 'name' => 'DescribeInsight', 'http' => [ 'method' => 'GET', 'requestUri' => '/insights/{Id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeInsightRequest', ], 'output' => [ 'shape' => 'DescribeInsightResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeOrganizationHealth' => [ 'name' => 'DescribeOrganizationHealth', 'http' => [ 'method' => 'POST', 'requestUri' => '/organization/health', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeOrganizationHealthRequest', ], 'output' => [ 'shape' => 'DescribeOrganizationHealthResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeOrganizationOverview' => [ 'name' => 'DescribeOrganizationOverview', 'http' => [ 'method' => 'POST', 'requestUri' => '/organization/overview', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeOrganizationOverviewRequest', ], 'output' => [ 'shape' => 'DescribeOrganizationOverviewResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeOrganizationResourceCollectionHealth' => [ 'name' => 'DescribeOrganizationResourceCollectionHealth', 'http' => [ 'method' => 'POST', 'requestUri' => '/organization/health/resource-collection', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeOrganizationResourceCollectionHealthRequest', ], 'output' => [ 'shape' => 'DescribeOrganizationResourceCollectionHealthResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeResourceCollectionHealth' => [ 'name' => 'DescribeResourceCollectionHealth', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/health/resource-collection/{ResourceCollectionType}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeResourceCollectionHealthRequest', ], 'output' => [ 'shape' => 'DescribeResourceCollectionHealthResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeServiceIntegration' => [ 'name' => 'DescribeServiceIntegration', 'http' => [ 'method' => 'GET', 'requestUri' => '/service-integrations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeServiceIntegrationRequest', ], 'output' => [ 'shape' => 'DescribeServiceIntegrationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetCostEstimation' => [ 'name' => 'GetCostEstimation', 'http' => [ 'method' => 'GET', 'requestUri' => '/cost-estimation', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCostEstimationRequest', ], 'output' => [ 'shape' => 'GetCostEstimationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetResourceCollection' => [ 'name' => 'GetResourceCollection', 'http' => [ 'method' => 'GET', 'requestUri' => '/resource-collections/{ResourceCollectionType}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetResourceCollectionRequest', ], 'output' => [ 'shape' => 'GetResourceCollectionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListAnomaliesForInsight' => [ 'name' => 'ListAnomaliesForInsight', 'http' => [ 'method' => 'POST', 'requestUri' => '/anomalies/insight/{InsightId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAnomaliesForInsightRequest', ], 'output' => [ 'shape' => 'ListAnomaliesForInsightResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListAnomalousLogGroups' => [ 'name' => 'ListAnomalousLogGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-log-anomalies', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAnomalousLogGroupsRequest', ], 'output' => [ 'shape' => 'ListAnomalousLogGroupsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListEvents' => [ 'name' => 'ListEvents', 'http' => [ 'method' => 'POST', 'requestUri' => '/events', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListEventsRequest', ], 'output' => [ 'shape' => 'ListEventsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListInsights' => [ 'name' => 'ListInsights', 'http' => [ 'method' => 'POST', 'requestUri' => '/insights', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListInsightsRequest', ], 'output' => [ 'shape' => 'ListInsightsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListMonitoredResources' => [ 'name' => 'ListMonitoredResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/monitoredResources', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMonitoredResourcesRequest', ], 'output' => [ 'shape' => 'ListMonitoredResourcesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListNotificationChannels' => [ 'name' => 'ListNotificationChannels', 'http' => [ 'method' => 'POST', 'requestUri' => '/channels', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListNotificationChannelsRequest', ], 'output' => [ 'shape' => 'ListNotificationChannelsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListOrganizationInsights' => [ 'name' => 'ListOrganizationInsights', 'http' => [ 'method' => 'POST', 'requestUri' => '/organization/insights', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListOrganizationInsightsRequest', ], 'output' => [ 'shape' => 'ListOrganizationInsightsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListRecommendations' => [ 'name' => 'ListRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/recommendations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListRecommendationsRequest', ], 'output' => [ 'shape' => 'ListRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'PutFeedback' => [ 'name' => 'PutFeedback', 'http' => [ 'method' => 'PUT', 'requestUri' => '/feedback', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutFeedbackRequest', ], 'output' => [ 'shape' => 'PutFeedbackResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'RemoveNotificationChannel' => [ 'name' => 'RemoveNotificationChannel', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/channels/{Id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RemoveNotificationChannelRequest', ], 'output' => [ 'shape' => 'RemoveNotificationChannelResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'SearchInsights' => [ 'name' => 'SearchInsights', 'http' => [ 'method' => 'POST', 'requestUri' => '/insights/search', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchInsightsRequest', ], 'output' => [ 'shape' => 'SearchInsightsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'SearchOrganizationInsights' => [ 'name' => 'SearchOrganizationInsights', 'http' => [ 'method' => 'POST', 'requestUri' => '/organization/insights/search', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchOrganizationInsightsRequest', ], 'output' => [ 'shape' => 'SearchOrganizationInsightsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'StartCostEstimation' => [ 'name' => 'StartCostEstimation', 'http' => [ 'method' => 'PUT', 'requestUri' => '/cost-estimation', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartCostEstimationRequest', ], 'output' => [ 'shape' => 'StartCostEstimationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateEventSourcesConfig' => [ 'name' => 'UpdateEventSourcesConfig', 'http' => [ 'method' => 'PUT', 'requestUri' => '/event-sources', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateEventSourcesConfigRequest', ], 'output' => [ 'shape' => 'UpdateEventSourcesConfigResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateResourceCollection' => [ 'name' => 'UpdateResourceCollection', 'http' => [ 'method' => 'PUT', 'requestUri' => '/resource-collections', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateResourceCollectionRequest', ], 'output' => [ 'shape' => 'UpdateResourceCollectionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateServiceIntegration' => [ 'name' => 'UpdateServiceIntegration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/service-integrations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateServiceIntegrationRequest', ], 'output' => [ 'shape' => 'UpdateServiceIntegrationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'ErrorMessageString', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'AccountHealth' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AwsAccountId', ], 'Insight' => [ 'shape' => 'AccountInsightHealth', ], ], ], 'AccountHealths' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountHealth', ], ], 'AccountIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsAccountId', ], 'max' => 5, 'min' => 0, ], 'AccountInsightHealth' => [ 'type' => 'structure', 'members' => [ 'OpenProactiveInsights' => [ 'shape' => 'NumOpenProactiveInsights', ], 'OpenReactiveInsights' => [ 'shape' => 'NumOpenReactiveInsights', ], ], ], 'AddNotificationChannelRequest' => [ 'type' => 'structure', 'required' => [ 'Config', ], 'members' => [ 'Config' => [ 'shape' => 'NotificationChannelConfig', ], ], ], 'AddNotificationChannelResponse' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'NotificationChannelId', ], ], ], 'AmazonCodeGuruProfilerIntegration' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'EventSourceOptInStatus', ], ], ], 'AnalyzedResourceCount' => [ 'type' => 'long', ], 'AnomalousLogGroup' => [ 'type' => 'structure', 'members' => [ 'LogGroupName' => [ 'shape' => 'LogGroupName', ], 'ImpactStartTime' => [ 'shape' => 'Timestamp', ], 'ImpactEndTime' => [ 'shape' => 'Timestamp', ], 'NumberOfLogLinesScanned' => [ 'shape' => 'NumberOfLogLinesScanned', ], 'LogAnomalyShowcases' => [ 'shape' => 'LogAnomalyShowcases', ], ], ], 'AnomalousLogGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnomalousLogGroup', ], ], 'AnomalyDescription' => [ 'type' => 'string', ], 'AnomalyId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[\\w~.-]*$', ], 'AnomalyLimit' => [ 'type' => 'double', 'box' => true, ], 'AnomalyName' => [ 'type' => 'string', ], 'AnomalyReportedTimeRange' => [ 'type' => 'structure', 'required' => [ 'OpenTime', ], 'members' => [ 'OpenTime' => [ 'shape' => 'Timestamp', ], 'CloseTime' => [ 'shape' => 'Timestamp', ], ], ], 'AnomalyResource' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'ResourceName', ], 'Type' => [ 'shape' => 'ResourceType', ], ], ], 'AnomalyResources' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnomalyResource', ], ], 'AnomalySeverity' => [ 'type' => 'string', 'enum' => [ 'LOW', 'MEDIUM', 'HIGH', ], ], 'AnomalySource' => [ 'type' => 'string', ], 'AnomalySourceDetails' => [ 'type' => 'structure', 'members' => [ 'CloudWatchMetrics' => [ 'shape' => 'CloudWatchMetricsDetails', ], 'PerformanceInsightsMetrics' => [ 'shape' => 'PerformanceInsightsMetricsDetails', ], ], ], 'AnomalySourceMetadata' => [ 'type' => 'structure', 'members' => [ 'Source' => [ 'shape' => 'AnomalySource', ], 'SourceResourceName' => [ 'shape' => 'ResourceName', ], 'SourceResourceType' => [ 'shape' => 'ResourceType', ], ], ], 'AnomalyStatus' => [ 'type' => 'string', 'enum' => [ 'ONGOING', 'CLOSED', ], ], 'AnomalyTimeRange' => [ 'type' => 'structure', 'required' => [ 'StartTime', ], 'members' => [ 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], ], ], 'AnomalyType' => [ 'type' => 'string', 'enum' => [ 'CAUSAL', 'CONTEXTUAL', ], ], 'AppBoundaryKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'AssociatedResourceArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceArn', ], ], 'AwsAccountId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '^\\d{12}$', ], 'Channels' => [ 'type' => 'list', 'member' => [ 'shape' => 'NotificationChannel', ], ], 'ClientToken' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[a-zA-Z0-9]+[a-zA-Z0-9-]*$', ], 'CloudFormationCollection' => [ 'type' => 'structure', 'members' => [ 'StackNames' => [ 'shape' => 'StackNames', ], ], ], 'CloudFormationCollectionFilter' => [ 'type' => 'structure', 'members' => [ 'StackNames' => [ 'shape' => 'StackNames', ], ], ], 'CloudFormationCostEstimationResourceCollectionFilter' => [ 'type' => 'structure', 'members' => [ 'StackNames' => [ 'shape' => 'CostEstimationStackNames', ], ], ], 'CloudFormationHealth' => [ 'type' => 'structure', 'members' => [ 'StackName' => [ 'shape' => 'StackName', ], 'Insight' => [ 'shape' => 'InsightHealth', ], 'AnalyzedResourceCount' => [ 'shape' => 'AnalyzedResourceCount', ], ], ], 'CloudFormationHealths' => [ 'type' => 'list', 'member' => [ 'shape' => 'CloudFormationHealth', ], ], 'CloudWatchMetricDataStatusCode' => [ 'type' => 'string', 'enum' => [ 'Complete', 'InternalError', 'PartialData', ], ], 'CloudWatchMetricsDataSummary' => [ 'type' => 'structure', 'members' => [ 'TimestampMetricValuePairList' => [ 'shape' => 'TimestampMetricValuePairList', ], 'StatusCode' => [ 'shape' => 'CloudWatchMetricDataStatusCode', ], ], ], 'CloudWatchMetricsDetail' => [ 'type' => 'structure', 'members' => [ 'MetricName' => [ 'shape' => 'CloudWatchMetricsMetricName', ], 'Namespace' => [ 'shape' => 'CloudWatchMetricsNamespace', ], 'Dimensions' => [ 'shape' => 'CloudWatchMetricsDimensions', ], 'Stat' => [ 'shape' => 'CloudWatchMetricsStat', ], 'Unit' => [ 'shape' => 'CloudWatchMetricsUnit', ], 'Period' => [ 'shape' => 'CloudWatchMetricsPeriod', ], 'MetricDataSummary' => [ 'shape' => 'CloudWatchMetricsDataSummary', ], ], ], 'CloudWatchMetricsDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'CloudWatchMetricsDetail', ], ], 'CloudWatchMetricsDimension' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'CloudWatchMetricsDimensionName', ], 'Value' => [ 'shape' => 'CloudWatchMetricsDimensionValue', ], ], ], 'CloudWatchMetricsDimensionName' => [ 'type' => 'string', ], 'CloudWatchMetricsDimensionValue' => [ 'type' => 'string', ], 'CloudWatchMetricsDimensions' => [ 'type' => 'list', 'member' => [ 'shape' => 'CloudWatchMetricsDimension', ], ], 'CloudWatchMetricsMetricName' => [ 'type' => 'string', ], 'CloudWatchMetricsNamespace' => [ 'type' => 'string', ], 'CloudWatchMetricsPeriod' => [ 'type' => 'integer', ], 'CloudWatchMetricsStat' => [ 'type' => 'string', 'enum' => [ 'Sum', 'Average', 'SampleCount', 'Minimum', 'Maximum', 'p99', 'p90', 'p50', ], ], 'CloudWatchMetricsUnit' => [ 'type' => 'string', ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'Message', 'ResourceId', 'ResourceType', ], 'members' => [ 'Message' => [ 'shape' => 'ErrorMessageString', ], 'ResourceId' => [ 'shape' => 'ResourceIdString', ], 'ResourceType' => [ 'shape' => 'ResourceIdType', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'Cost' => [ 'type' => 'double', ], 'CostEstimationResourceCollectionFilter' => [ 'type' => 'structure', 'members' => [ 'CloudFormation' => [ 'shape' => 'CloudFormationCostEstimationResourceCollectionFilter', ], 'Tags' => [ 'shape' => 'TagCostEstimationResourceCollectionFilters', ], ], ], 'CostEstimationServiceResourceCount' => [ 'type' => 'integer', ], 'CostEstimationServiceResourceState' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', ], ], 'CostEstimationStackNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackName', ], 'max' => 1, 'min' => 1, ], 'CostEstimationStatus' => [ 'type' => 'string', 'enum' => [ 'ONGOING', 'COMPLETED', ], ], 'CostEstimationTagValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagValue', ], 'max' => 1, 'min' => 1, ], 'CostEstimationTimeRange' => [ 'type' => 'structure', 'members' => [ 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], ], ], 'DeleteInsightRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'InsightId', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'DeleteInsightResponse' => [ 'type' => 'structure', 'members' => [], ], 'DescribeAccountHealthRequest' => [ 'type' => 'structure', 'members' => [], ], 'DescribeAccountHealthResponse' => [ 'type' => 'structure', 'required' => [ 'OpenReactiveInsights', 'OpenProactiveInsights', 'MetricsAnalyzed', 'ResourceHours', ], 'members' => [ 'OpenReactiveInsights' => [ 'shape' => 'NumOpenReactiveInsights', ], 'OpenProactiveInsights' => [ 'shape' => 'NumOpenProactiveInsights', ], 'MetricsAnalyzed' => [ 'shape' => 'NumMetricsAnalyzed', ], 'ResourceHours' => [ 'shape' => 'ResourceHours', ], 'AnalyzedResourceCount' => [ 'shape' => 'AnalyzedResourceCount', ], ], ], 'DescribeAccountOverviewRequest' => [ 'type' => 'structure', 'required' => [ 'FromTime', ], 'members' => [ 'FromTime' => [ 'shape' => 'Timestamp', ], 'ToTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeAccountOverviewResponse' => [ 'type' => 'structure', 'required' => [ 'ReactiveInsights', 'ProactiveInsights', 'MeanTimeToRecoverInMilliseconds', ], 'members' => [ 'ReactiveInsights' => [ 'shape' => 'NumReactiveInsights', ], 'ProactiveInsights' => [ 'shape' => 'NumProactiveInsights', ], 'MeanTimeToRecoverInMilliseconds' => [ 'shape' => 'MeanTimeToRecoverInMilliseconds', ], ], ], 'DescribeAnomalyRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'AnomalyId', 'location' => 'uri', 'locationName' => 'Id', ], 'AccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'querystring', 'locationName' => 'AccountId', ], ], ], 'DescribeAnomalyResponse' => [ 'type' => 'structure', 'members' => [ 'ProactiveAnomaly' => [ 'shape' => 'ProactiveAnomaly', ], 'ReactiveAnomaly' => [ 'shape' => 'ReactiveAnomaly', ], ], ], 'DescribeEventSourcesConfigRequest' => [ 'type' => 'structure', 'members' => [], ], 'DescribeEventSourcesConfigResponse' => [ 'type' => 'structure', 'members' => [ 'EventSources' => [ 'shape' => 'EventSourcesConfig', ], ], ], 'DescribeFeedbackRequest' => [ 'type' => 'structure', 'members' => [ 'InsightId' => [ 'shape' => 'InsightId', ], ], ], 'DescribeFeedbackResponse' => [ 'type' => 'structure', 'members' => [ 'InsightFeedback' => [ 'shape' => 'InsightFeedback', ], ], ], 'DescribeInsightRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'InsightId', 'location' => 'uri', 'locationName' => 'Id', ], 'AccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'querystring', 'locationName' => 'AccountId', ], ], ], 'DescribeInsightResponse' => [ 'type' => 'structure', 'members' => [ 'ProactiveInsight' => [ 'shape' => 'ProactiveInsight', ], 'ReactiveInsight' => [ 'shape' => 'ReactiveInsight', ], ], ], 'DescribeOrganizationHealthRequest' => [ 'type' => 'structure', 'members' => [ 'AccountIds' => [ 'shape' => 'AccountIdList', ], 'OrganizationalUnitIds' => [ 'shape' => 'OrganizationalUnitIdList', ], ], ], 'DescribeOrganizationHealthResponse' => [ 'type' => 'structure', 'required' => [ 'OpenReactiveInsights', 'OpenProactiveInsights', 'MetricsAnalyzed', 'ResourceHours', ], 'members' => [ 'OpenReactiveInsights' => [ 'shape' => 'NumOpenReactiveInsights', ], 'OpenProactiveInsights' => [ 'shape' => 'NumOpenProactiveInsights', ], 'MetricsAnalyzed' => [ 'shape' => 'NumMetricsAnalyzed', ], 'ResourceHours' => [ 'shape' => 'ResourceHours', ], ], ], 'DescribeOrganizationOverviewRequest' => [ 'type' => 'structure', 'required' => [ 'FromTime', ], 'members' => [ 'FromTime' => [ 'shape' => 'Timestamp', ], 'ToTime' => [ 'shape' => 'Timestamp', ], 'AccountIds' => [ 'shape' => 'AccountIdList', ], 'OrganizationalUnitIds' => [ 'shape' => 'OrganizationalUnitIdList', ], ], ], 'DescribeOrganizationOverviewResponse' => [ 'type' => 'structure', 'required' => [ 'ReactiveInsights', 'ProactiveInsights', ], 'members' => [ 'ReactiveInsights' => [ 'shape' => 'NumReactiveInsights', ], 'ProactiveInsights' => [ 'shape' => 'NumProactiveInsights', ], ], ], 'DescribeOrganizationResourceCollectionHealthRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationResourceCollectionType', ], 'members' => [ 'OrganizationResourceCollectionType' => [ 'shape' => 'OrganizationResourceCollectionType', ], 'AccountIds' => [ 'shape' => 'AccountIdList', ], 'OrganizationalUnitIds' => [ 'shape' => 'OrganizationalUnitIdList', ], 'NextToken' => [ 'shape' => 'UuidNextToken', ], 'MaxResults' => [ 'shape' => 'OrganizationResourceCollectionMaxResults', ], ], ], 'DescribeOrganizationResourceCollectionHealthResponse' => [ 'type' => 'structure', 'members' => [ 'CloudFormation' => [ 'shape' => 'CloudFormationHealths', ], 'Service' => [ 'shape' => 'ServiceHealths', ], 'Account' => [ 'shape' => 'AccountHealths', ], 'NextToken' => [ 'shape' => 'UuidNextToken', ], 'Tags' => [ 'shape' => 'TagHealths', ], ], ], 'DescribeResourceCollectionHealthRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceCollectionType', ], 'members' => [ 'ResourceCollectionType' => [ 'shape' => 'ResourceCollectionType', 'location' => 'uri', 'locationName' => 'ResourceCollectionType', ], 'NextToken' => [ 'shape' => 'UuidNextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'DescribeResourceCollectionHealthResponse' => [ 'type' => 'structure', 'members' => [ 'CloudFormation' => [ 'shape' => 'CloudFormationHealths', ], 'Service' => [ 'shape' => 'ServiceHealths', ], 'NextToken' => [ 'shape' => 'UuidNextToken', ], 'Tags' => [ 'shape' => 'TagHealths', ], ], ], 'DescribeServiceIntegrationRequest' => [ 'type' => 'structure', 'members' => [], ], 'DescribeServiceIntegrationResponse' => [ 'type' => 'structure', 'members' => [ 'ServiceIntegration' => [ 'shape' => 'ServiceIntegrationConfig', ], ], ], 'EndTimeRange' => [ 'type' => 'structure', 'members' => [ 'FromTime' => [ 'shape' => 'Timestamp', ], 'ToTime' => [ 'shape' => 'Timestamp', ], ], ], 'ErrorMessageString' => [ 'type' => 'string', ], 'ErrorNameString' => [ 'type' => 'string', ], 'ErrorQuotaCodeString' => [ 'type' => 'string', ], 'ErrorServiceCodeString' => [ 'type' => 'string', ], 'Event' => [ 'type' => 'structure', 'members' => [ 'ResourceCollection' => [ 'shape' => 'ResourceCollection', ], 'Id' => [ 'shape' => 'EventId', ], 'Time' => [ 'shape' => 'Timestamp', ], 'EventSource' => [ 'shape' => 'EventSource', ], 'Name' => [ 'shape' => 'EventName', ], 'DataSource' => [ 'shape' => 'EventDataSource', ], 'EventClass' => [ 'shape' => 'EventClass', ], 'Resources' => [ 'shape' => 'EventResources', ], ], ], 'EventClass' => [ 'type' => 'string', 'enum' => [ 'INFRASTRUCTURE', 'DEPLOYMENT', 'SECURITY_CHANGE', 'CONFIG_CHANGE', 'SCHEMA_CHANGE', ], ], 'EventDataSource' => [ 'type' => 'string', 'enum' => [ 'AWS_CLOUD_TRAIL', 'AWS_CODE_DEPLOY', ], ], 'EventId' => [ 'type' => 'string', ], 'EventName' => [ 'type' => 'string', 'max' => 50, 'min' => 0, ], 'EventResource' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'EventResourceType', ], 'Name' => [ 'shape' => 'EventResourceName', ], 'Arn' => [ 'shape' => 'EventResourceArn', ], ], ], 'EventResourceArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 36, 'pattern' => '^arn:aws[-a-z]*:[a-z0-9-]*:[a-z0-9-]*:\\d{12}:.*$', ], 'EventResourceName' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^.*$', ], 'EventResourceType' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^.*$', ], 'EventResources' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventResource', ], ], 'EventSource' => [ 'type' => 'string', 'max' => 50, 'min' => 10, 'pattern' => '^[a-z]+[a-z0-9]*\\.amazonaws\\.com|aws\\.events$', ], 'EventSourceOptInStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'EventSourcesConfig' => [ 'type' => 'structure', 'members' => [ 'AmazonCodeGuruProfiler' => [ 'shape' => 'AmazonCodeGuruProfilerIntegration', ], ], ], 'EventTimeRange' => [ 'type' => 'structure', 'required' => [ 'FromTime', 'ToTime', ], 'members' => [ 'FromTime' => [ 'shape' => 'Timestamp', ], 'ToTime' => [ 'shape' => 'Timestamp', ], ], ], 'Events' => [ 'type' => 'list', 'member' => [ 'shape' => 'Event', ], ], 'Explanation' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'GetCostEstimationRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'UuidNextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'GetCostEstimationResponse' => [ 'type' => 'structure', 'members' => [ 'ResourceCollection' => [ 'shape' => 'CostEstimationResourceCollectionFilter', ], 'Status' => [ 'shape' => 'CostEstimationStatus', ], 'Costs' => [ 'shape' => 'ServiceResourceCosts', ], 'TimeRange' => [ 'shape' => 'CostEstimationTimeRange', ], 'TotalCost' => [ 'shape' => 'Cost', ], 'NextToken' => [ 'shape' => 'UuidNextToken', ], ], ], 'GetResourceCollectionRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceCollectionType', ], 'members' => [ 'ResourceCollectionType' => [ 'shape' => 'ResourceCollectionType', 'location' => 'uri', 'locationName' => 'ResourceCollectionType', ], 'NextToken' => [ 'shape' => 'UuidNextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'GetResourceCollectionResponse' => [ 'type' => 'structure', 'members' => [ 'ResourceCollection' => [ 'shape' => 'ResourceCollectionFilter', ], 'NextToken' => [ 'shape' => 'UuidNextToken', ], ], ], 'InsightDescription' => [ 'type' => 'string', ], 'InsightFeedback' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'InsightId', ], 'Feedback' => [ 'shape' => 'InsightFeedbackOption', ], ], ], 'InsightFeedbackOption' => [ 'type' => 'string', 'enum' => [ 'VALID_COLLECTION', 'RECOMMENDATION_USEFUL', 'ALERT_TOO_SENSITIVE', 'DATA_NOISY_ANOMALY', 'DATA_INCORRECT', ], ], 'InsightHealth' => [ 'type' => 'structure', 'members' => [ 'OpenProactiveInsights' => [ 'shape' => 'NumOpenProactiveInsights', ], 'OpenReactiveInsights' => [ 'shape' => 'NumOpenReactiveInsights', ], 'MeanTimeToRecoverInMilliseconds' => [ 'shape' => 'MeanTimeToRecoverInMilliseconds', ], ], ], 'InsightId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[\\w-]*$', ], 'InsightName' => [ 'type' => 'string', 'max' => 530, 'min' => 1, 'pattern' => '^[\\s\\S]*$', ], 'InsightSeverities' => [ 'type' => 'list', 'member' => [ 'shape' => 'InsightSeverity', ], 'max' => 3, 'min' => 0, ], 'InsightSeverity' => [ 'type' => 'string', 'enum' => [ 'LOW', 'MEDIUM', 'HIGH', ], ], 'InsightStatus' => [ 'type' => 'string', 'enum' => [ 'ONGOING', 'CLOSED', ], ], 'InsightStatuses' => [ 'type' => 'list', 'member' => [ 'shape' => 'InsightStatus', ], 'max' => 2, 'min' => 0, ], 'InsightTimeRange' => [ 'type' => 'structure', 'required' => [ 'StartTime', ], 'members' => [ 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], ], ], 'InsightType' => [ 'type' => 'string', 'enum' => [ 'REACTIVE', 'PROACTIVE', ], ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'ErrorMessageString', ], 'RetryAfterSeconds' => [ 'shape' => 'RetryAfterSeconds', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'KMSKeyId' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^.*$', ], 'KMSServerSideEncryptionIntegration' => [ 'type' => 'structure', 'members' => [ 'KMSKeyId' => [ 'shape' => 'KMSKeyId', ], 'OptInStatus' => [ 'shape' => 'OptInStatus', ], 'Type' => [ 'shape' => 'ServerSideEncryptionType', ], ], ], 'KMSServerSideEncryptionIntegrationConfig' => [ 'type' => 'structure', 'members' => [ 'KMSKeyId' => [ 'shape' => 'KMSKeyId', ], 'OptInStatus' => [ 'shape' => 'OptInStatus', ], 'Type' => [ 'shape' => 'ServerSideEncryptionType', ], ], ], 'ListAnomaliesForInsightFilters' => [ 'type' => 'structure', 'members' => [ 'ServiceCollection' => [ 'shape' => 'ServiceCollection', ], ], ], 'ListAnomaliesForInsightMaxResults' => [ 'type' => 'integer', 'max' => 500, 'min' => 1, ], 'ListAnomaliesForInsightRequest' => [ 'type' => 'structure', 'required' => [ 'InsightId', ], 'members' => [ 'InsightId' => [ 'shape' => 'InsightId', 'location' => 'uri', 'locationName' => 'InsightId', ], 'StartTimeRange' => [ 'shape' => 'StartTimeRange', ], 'MaxResults' => [ 'shape' => 'ListAnomaliesForInsightMaxResults', ], 'NextToken' => [ 'shape' => 'UuidNextToken', ], 'AccountId' => [ 'shape' => 'AwsAccountId', ], 'Filters' => [ 'shape' => 'ListAnomaliesForInsightFilters', ], ], ], 'ListAnomaliesForInsightResponse' => [ 'type' => 'structure', 'members' => [ 'ProactiveAnomalies' => [ 'shape' => 'ProactiveAnomalies', ], 'ReactiveAnomalies' => [ 'shape' => 'ReactiveAnomalies', ], 'NextToken' => [ 'shape' => 'UuidNextToken', ], ], ], 'ListAnomalousLogGroupsMaxResults' => [ 'type' => 'integer', 'max' => 200, 'min' => 1, ], 'ListAnomalousLogGroupsRequest' => [ 'type' => 'structure', 'required' => [ 'InsightId', ], 'members' => [ 'InsightId' => [ 'shape' => 'InsightId', ], 'MaxResults' => [ 'shape' => 'ListAnomalousLogGroupsMaxResults', ], 'NextToken' => [ 'shape' => 'UuidNextToken', ], ], ], 'ListAnomalousLogGroupsResponse' => [ 'type' => 'structure', 'required' => [ 'InsightId', 'AnomalousLogGroups', ], 'members' => [ 'InsightId' => [ 'shape' => 'InsightId', ], 'AnomalousLogGroups' => [ 'shape' => 'AnomalousLogGroups', ], 'NextToken' => [ 'shape' => 'UuidNextToken', ], ], ], 'ListEventsFilters' => [ 'type' => 'structure', 'members' => [ 'InsightId' => [ 'shape' => 'InsightId', ], 'EventTimeRange' => [ 'shape' => 'EventTimeRange', ], 'EventClass' => [ 'shape' => 'EventClass', ], 'EventSource' => [ 'shape' => 'EventSource', ], 'DataSource' => [ 'shape' => 'EventDataSource', ], 'ResourceCollection' => [ 'shape' => 'ResourceCollection', ], ], ], 'ListEventsMaxResults' => [ 'type' => 'integer', 'max' => 200, 'min' => 1, ], 'ListEventsRequest' => [ 'type' => 'structure', 'required' => [ 'Filters', ], 'members' => [ 'Filters' => [ 'shape' => 'ListEventsFilters', ], 'MaxResults' => [ 'shape' => 'ListEventsMaxResults', ], 'NextToken' => [ 'shape' => 'UuidNextToken', ], 'AccountId' => [ 'shape' => 'AwsAccountId', ], ], ], 'ListEventsResponse' => [ 'type' => 'structure', 'required' => [ 'Events', ], 'members' => [ 'Events' => [ 'shape' => 'Events', ], 'NextToken' => [ 'shape' => 'UuidNextToken', ], ], ], 'ListInsightsAccountIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsAccountId', ], 'max' => 1, 'min' => 0, ], 'ListInsightsAnyStatusFilter' => [ 'type' => 'structure', 'required' => [ 'Type', 'StartTimeRange', ], 'members' => [ 'Type' => [ 'shape' => 'InsightType', ], 'StartTimeRange' => [ 'shape' => 'StartTimeRange', ], ], ], 'ListInsightsClosedStatusFilter' => [ 'type' => 'structure', 'required' => [ 'Type', 'EndTimeRange', ], 'members' => [ 'Type' => [ 'shape' => 'InsightType', ], 'EndTimeRange' => [ 'shape' => 'EndTimeRange', ], ], ], 'ListInsightsMaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'ListInsightsOngoingStatusFilter' => [ 'type' => 'structure', 'required' => [ 'Type', ], 'members' => [ 'Type' => [ 'shape' => 'InsightType', ], ], ], 'ListInsightsOrganizationalUnitIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrganizationalUnitId', ], 'max' => 1, 'min' => 0, ], 'ListInsightsRequest' => [ 'type' => 'structure', 'required' => [ 'StatusFilter', ], 'members' => [ 'StatusFilter' => [ 'shape' => 'ListInsightsStatusFilter', ], 'MaxResults' => [ 'shape' => 'ListInsightsMaxResults', ], 'NextToken' => [ 'shape' => 'UuidNextToken', ], ], ], 'ListInsightsResponse' => [ 'type' => 'structure', 'members' => [ 'ProactiveInsights' => [ 'shape' => 'ProactiveInsights', ], 'ReactiveInsights' => [ 'shape' => 'ReactiveInsights', ], 'NextToken' => [ 'shape' => 'UuidNextToken', ], ], ], 'ListInsightsStatusFilter' => [ 'type' => 'structure', 'members' => [ 'Ongoing' => [ 'shape' => 'ListInsightsOngoingStatusFilter', ], 'Closed' => [ 'shape' => 'ListInsightsClosedStatusFilter', ], 'Any' => [ 'shape' => 'ListInsightsAnyStatusFilter', ], ], ], 'ListMonitoredResourcesFilters' => [ 'type' => 'structure', 'required' => [ 'ResourcePermission', 'ResourceTypeFilters', ], 'members' => [ 'ResourcePermission' => [ 'shape' => 'ResourcePermission', ], 'ResourceTypeFilters' => [ 'shape' => 'ResourceTypeFilters', ], ], ], 'ListMonitoredResourcesMaxResults' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'ListMonitoredResourcesRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'ListMonitoredResourcesFilters', ], 'MaxResults' => [ 'shape' => 'ListMonitoredResourcesMaxResults', ], 'NextToken' => [ 'shape' => 'UuidNextToken', ], ], ], 'ListMonitoredResourcesResponse' => [ 'type' => 'structure', 'required' => [ 'MonitoredResourceIdentifiers', ], 'members' => [ 'MonitoredResourceIdentifiers' => [ 'shape' => 'MonitoredResourceIdentifiers', ], 'NextToken' => [ 'shape' => 'UuidNextToken', ], ], ], 'ListNotificationChannelsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'UuidNextToken', ], ], ], 'ListNotificationChannelsResponse' => [ 'type' => 'structure', 'members' => [ 'Channels' => [ 'shape' => 'Channels', ], 'NextToken' => [ 'shape' => 'UuidNextToken', ], ], ], 'ListOrganizationInsightsRequest' => [ 'type' => 'structure', 'required' => [ 'StatusFilter', ], 'members' => [ 'StatusFilter' => [ 'shape' => 'ListInsightsStatusFilter', ], 'MaxResults' => [ 'shape' => 'ListInsightsMaxResults', ], 'AccountIds' => [ 'shape' => 'ListInsightsAccountIdList', ], 'OrganizationalUnitIds' => [ 'shape' => 'ListInsightsOrganizationalUnitIdList', ], 'NextToken' => [ 'shape' => 'UuidNextToken', ], ], ], 'ListOrganizationInsightsResponse' => [ 'type' => 'structure', 'members' => [ 'ProactiveInsights' => [ 'shape' => 'ProactiveOrganizationInsights', ], 'ReactiveInsights' => [ 'shape' => 'ReactiveOrganizationInsights', ], 'NextToken' => [ 'shape' => 'UuidNextToken', ], ], ], 'ListRecommendationsRequest' => [ 'type' => 'structure', 'required' => [ 'InsightId', ], 'members' => [ 'InsightId' => [ 'shape' => 'InsightId', ], 'NextToken' => [ 'shape' => 'UuidNextToken', ], 'Locale' => [ 'shape' => 'Locale', ], 'AccountId' => [ 'shape' => 'AwsAccountId', ], ], ], 'ListRecommendationsResponse' => [ 'type' => 'structure', 'members' => [ 'Recommendations' => [ 'shape' => 'Recommendations', ], 'NextToken' => [ 'shape' => 'UuidNextToken', ], ], ], 'Locale' => [ 'type' => 'string', 'enum' => [ 'DE_DE', 'EN_US', 'EN_GB', 'ES_ES', 'FR_FR', 'IT_IT', 'JA_JP', 'KO_KR', 'PT_BR', 'ZH_CN', 'ZH_TW', ], ], 'LogAnomalyClass' => [ 'type' => 'structure', 'members' => [ 'LogStreamName' => [ 'shape' => 'LogStreamName', ], 'LogAnomalyType' => [ 'shape' => 'LogAnomalyType', ], 'LogAnomalyToken' => [ 'shape' => 'LogAnomalyToken', ], 'LogEventId' => [ 'shape' => 'LogEventId', ], 'Explanation' => [ 'shape' => 'Explanation', ], 'NumberOfLogLinesOccurrences' => [ 'shape' => 'NumberOfLogLinesOccurrences', ], 'LogEventTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'LogAnomalyClasses' => [ 'type' => 'list', 'member' => [ 'shape' => 'LogAnomalyClass', ], 'max' => 10, 'min' => 0, ], 'LogAnomalyShowcase' => [ 'type' => 'structure', 'members' => [ 'LogAnomalyClasses' => [ 'shape' => 'LogAnomalyClasses', ], ], ], 'LogAnomalyShowcases' => [ 'type' => 'list', 'member' => [ 'shape' => 'LogAnomalyShowcase', ], 'max' => 20, 'min' => 0, ], 'LogAnomalyToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'LogAnomalyType' => [ 'type' => 'string', 'enum' => [ 'KEYWORD', 'KEYWORD_TOKEN', 'FORMAT', 'HTTP_CODE', 'BLOCK_FORMAT', 'NUMERICAL_POINT', 'NUMERICAL_NAN', 'NEW_FIELD_NAME', ], ], 'LogEventId' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'LogGroupName' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'LogStreamName' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'LogsAnomalyDetectionIntegration' => [ 'type' => 'structure', 'members' => [ 'OptInStatus' => [ 'shape' => 'OptInStatus', ], ], ], 'LogsAnomalyDetectionIntegrationConfig' => [ 'type' => 'structure', 'members' => [ 'OptInStatus' => [ 'shape' => 'OptInStatus', ], ], ], 'MeanTimeToRecoverInMilliseconds' => [ 'type' => 'long', ], 'MetricValue' => [ 'type' => 'double', ], 'MonitoredResourceIdentifier' => [ 'type' => 'structure', 'members' => [ 'MonitoredResourceName' => [ 'shape' => 'MonitoredResourceName', ], 'Type' => [ 'shape' => 'ResourceType', ], 'ResourcePermission' => [ 'shape' => 'ResourcePermission', ], 'LastUpdated' => [ 'shape' => 'Timestamp', ], 'ResourceCollection' => [ 'shape' => 'ResourceCollection', ], ], ], 'MonitoredResourceIdentifiers' => [ 'type' => 'list', 'member' => [ 'shape' => 'MonitoredResourceIdentifier', ], ], 'MonitoredResourceName' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '[\\.\\-_\\/#A-Za-z0-9]+', ], 'NotificationChannel' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'NotificationChannelId', ], 'Config' => [ 'shape' => 'NotificationChannelConfig', ], ], ], 'NotificationChannelConfig' => [ 'type' => 'structure', 'required' => [ 'Sns', ], 'members' => [ 'Sns' => [ 'shape' => 'SnsChannelConfig', ], 'Filters' => [ 'shape' => 'NotificationFilterConfig', ], ], ], 'NotificationChannelId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$', ], 'NotificationFilterConfig' => [ 'type' => 'structure', 'members' => [ 'Severities' => [ 'shape' => 'InsightSeverities', ], 'MessageTypes' => [ 'shape' => 'NotificationMessageTypes', ], ], ], 'NotificationMessageType' => [ 'type' => 'string', 'enum' => [ 'NEW_INSIGHT', 'CLOSED_INSIGHT', 'NEW_ASSOCIATION', 'SEVERITY_UPGRADED', 'NEW_RECOMMENDATION', ], ], 'NotificationMessageTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'NotificationMessageType', ], 'max' => 5, 'min' => 0, ], 'NumMetricsAnalyzed' => [ 'type' => 'integer', ], 'NumOpenProactiveInsights' => [ 'type' => 'integer', ], 'NumOpenReactiveInsights' => [ 'type' => 'integer', ], 'NumProactiveInsights' => [ 'type' => 'integer', ], 'NumReactiveInsights' => [ 'type' => 'integer', ], 'NumberOfLogLinesOccurrences' => [ 'type' => 'integer', ], 'NumberOfLogLinesScanned' => [ 'type' => 'integer', ], 'OpsCenterIntegration' => [ 'type' => 'structure', 'members' => [ 'OptInStatus' => [ 'shape' => 'OptInStatus', ], ], ], 'OpsCenterIntegrationConfig' => [ 'type' => 'structure', 'members' => [ 'OptInStatus' => [ 'shape' => 'OptInStatus', ], ], ], 'OptInStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'OrganizationResourceCollectionMaxResults' => [ 'type' => 'integer', 'max' => 500, 'min' => 1, ], 'OrganizationResourceCollectionType' => [ 'type' => 'string', 'enum' => [ 'AWS_CLOUD_FORMATION', 'AWS_SERVICE', 'AWS_ACCOUNT', 'AWS_TAGS', ], ], 'OrganizationalUnitId' => [ 'type' => 'string', 'max' => 68, 'pattern' => '^ou-[0-9a-z]{4,32}-[a-z0-9]{8,32}$', ], 'OrganizationalUnitIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrganizationalUnitId', ], 'max' => 5, 'min' => 0, ], 'PerformanceInsightsMetricDimension' => [ 'type' => 'string', ], 'PerformanceInsightsMetricDimensionGroup' => [ 'type' => 'structure', 'members' => [ 'Group' => [ 'shape' => 'PerformanceInsightsMetricGroup', ], 'Dimensions' => [ 'shape' => 'PerformanceInsightsMetricDimensions', ], 'Limit' => [ 'shape' => 'PerformanceInsightsMetricLimitInteger', ], ], ], 'PerformanceInsightsMetricDimensions' => [ 'type' => 'list', 'member' => [ 'shape' => 'PerformanceInsightsMetricDimension', ], ], 'PerformanceInsightsMetricDisplayName' => [ 'type' => 'string', ], 'PerformanceInsightsMetricFilterKey' => [ 'type' => 'string', ], 'PerformanceInsightsMetricFilterMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'PerformanceInsightsMetricFilterKey', ], 'value' => [ 'shape' => 'PerformanceInsightsMetricFilterValue', ], ], 'PerformanceInsightsMetricFilterValue' => [ 'type' => 'string', ], 'PerformanceInsightsMetricGroup' => [ 'type' => 'string', ], 'PerformanceInsightsMetricLimitInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 10, 'min' => 1, ], 'PerformanceInsightsMetricName' => [ 'type' => 'string', ], 'PerformanceInsightsMetricQuery' => [ 'type' => 'structure', 'members' => [ 'Metric' => [ 'shape' => 'PerformanceInsightsMetricName', ], 'GroupBy' => [ 'shape' => 'PerformanceInsightsMetricDimensionGroup', ], 'Filter' => [ 'shape' => 'PerformanceInsightsMetricFilterMap', ], ], ], 'PerformanceInsightsMetricUnit' => [ 'type' => 'string', ], 'PerformanceInsightsMetricsDetail' => [ 'type' => 'structure', 'members' => [ 'MetricDisplayName' => [ 'shape' => 'PerformanceInsightsMetricDisplayName', ], 'Unit' => [ 'shape' => 'PerformanceInsightsMetricUnit', ], 'MetricQuery' => [ 'shape' => 'PerformanceInsightsMetricQuery', ], 'ReferenceData' => [ 'shape' => 'PerformanceInsightsReferenceDataList', ], 'StatsAtAnomaly' => [ 'shape' => 'PerformanceInsightsStats', ], 'StatsAtBaseline' => [ 'shape' => 'PerformanceInsightsStats', ], ], ], 'PerformanceInsightsMetricsDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'PerformanceInsightsMetricsDetail', ], ], 'PerformanceInsightsReferenceComparisonValues' => [ 'type' => 'structure', 'members' => [ 'ReferenceScalar' => [ 'shape' => 'PerformanceInsightsReferenceScalar', ], 'ReferenceMetric' => [ 'shape' => 'PerformanceInsightsReferenceMetric', ], ], ], 'PerformanceInsightsReferenceData' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'PerformanceInsightsReferenceName', ], 'ComparisonValues' => [ 'shape' => 'PerformanceInsightsReferenceComparisonValues', ], ], ], 'PerformanceInsightsReferenceDataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PerformanceInsightsReferenceData', ], ], 'PerformanceInsightsReferenceMetric' => [ 'type' => 'structure', 'members' => [ 'MetricQuery' => [ 'shape' => 'PerformanceInsightsMetricQuery', ], ], ], 'PerformanceInsightsReferenceName' => [ 'type' => 'string', ], 'PerformanceInsightsReferenceScalar' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'PerformanceInsightsValueDouble', ], ], ], 'PerformanceInsightsStat' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'PerformanceInsightsStatType', ], 'Value' => [ 'shape' => 'PerformanceInsightsValueDouble', ], ], ], 'PerformanceInsightsStatType' => [ 'type' => 'string', ], 'PerformanceInsightsStats' => [ 'type' => 'list', 'member' => [ 'shape' => 'PerformanceInsightsStat', ], ], 'PerformanceInsightsValueDouble' => [ 'type' => 'double', 'box' => true, ], 'PredictionTimeRange' => [ 'type' => 'structure', 'required' => [ 'StartTime', ], 'members' => [ 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], ], ], 'ProactiveAnomalies' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProactiveAnomalySummary', ], ], 'ProactiveAnomaly' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'AnomalyId', ], 'Severity' => [ 'shape' => 'AnomalySeverity', ], 'Status' => [ 'shape' => 'AnomalyStatus', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], 'AnomalyTimeRange' => [ 'shape' => 'AnomalyTimeRange', ], 'AnomalyReportedTimeRange' => [ 'shape' => 'AnomalyReportedTimeRange', ], 'PredictionTimeRange' => [ 'shape' => 'PredictionTimeRange', ], 'SourceDetails' => [ 'shape' => 'AnomalySourceDetails', ], 'AssociatedInsightId' => [ 'shape' => 'InsightId', ], 'ResourceCollection' => [ 'shape' => 'ResourceCollection', ], 'Limit' => [ 'shape' => 'AnomalyLimit', ], 'SourceMetadata' => [ 'shape' => 'AnomalySourceMetadata', ], 'AnomalyResources' => [ 'shape' => 'AnomalyResources', ], 'Description' => [ 'shape' => 'AnomalyDescription', ], ], ], 'ProactiveAnomalySummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'AnomalyId', ], 'Severity' => [ 'shape' => 'AnomalySeverity', ], 'Status' => [ 'shape' => 'AnomalyStatus', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], 'AnomalyTimeRange' => [ 'shape' => 'AnomalyTimeRange', ], 'AnomalyReportedTimeRange' => [ 'shape' => 'AnomalyReportedTimeRange', ], 'PredictionTimeRange' => [ 'shape' => 'PredictionTimeRange', ], 'SourceDetails' => [ 'shape' => 'AnomalySourceDetails', ], 'AssociatedInsightId' => [ 'shape' => 'InsightId', ], 'ResourceCollection' => [ 'shape' => 'ResourceCollection', ], 'Limit' => [ 'shape' => 'AnomalyLimit', ], 'SourceMetadata' => [ 'shape' => 'AnomalySourceMetadata', ], 'AnomalyResources' => [ 'shape' => 'AnomalyResources', ], 'Description' => [ 'shape' => 'AnomalyDescription', ], ], ], 'ProactiveInsight' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'InsightId', ], 'Name' => [ 'shape' => 'InsightName', ], 'Severity' => [ 'shape' => 'InsightSeverity', ], 'Status' => [ 'shape' => 'InsightStatus', ], 'InsightTimeRange' => [ 'shape' => 'InsightTimeRange', ], 'PredictionTimeRange' => [ 'shape' => 'PredictionTimeRange', ], 'ResourceCollection' => [ 'shape' => 'ResourceCollection', ], 'SsmOpsItemId' => [ 'shape' => 'SsmOpsItemId', ], 'Description' => [ 'shape' => 'InsightDescription', ], ], ], 'ProactiveInsightSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'InsightId', ], 'Name' => [ 'shape' => 'InsightName', ], 'Severity' => [ 'shape' => 'InsightSeverity', ], 'Status' => [ 'shape' => 'InsightStatus', ], 'InsightTimeRange' => [ 'shape' => 'InsightTimeRange', ], 'PredictionTimeRange' => [ 'shape' => 'PredictionTimeRange', ], 'ResourceCollection' => [ 'shape' => 'ResourceCollection', ], 'ServiceCollection' => [ 'shape' => 'ServiceCollection', ], 'AssociatedResourceArns' => [ 'shape' => 'AssociatedResourceArns', ], ], ], 'ProactiveInsights' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProactiveInsightSummary', ], ], 'ProactiveOrganizationInsightSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'InsightId', ], 'AccountId' => [ 'shape' => 'AwsAccountId', ], 'OrganizationalUnitId' => [ 'shape' => 'OrganizationalUnitId', ], 'Name' => [ 'shape' => 'InsightName', ], 'Severity' => [ 'shape' => 'InsightSeverity', ], 'Status' => [ 'shape' => 'InsightStatus', ], 'InsightTimeRange' => [ 'shape' => 'InsightTimeRange', ], 'PredictionTimeRange' => [ 'shape' => 'PredictionTimeRange', ], 'ResourceCollection' => [ 'shape' => 'ResourceCollection', ], 'ServiceCollection' => [ 'shape' => 'ServiceCollection', ], ], ], 'ProactiveOrganizationInsights' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProactiveOrganizationInsightSummary', ], ], 'PutFeedbackRequest' => [ 'type' => 'structure', 'members' => [ 'InsightFeedback' => [ 'shape' => 'InsightFeedback', ], ], ], 'PutFeedbackResponse' => [ 'type' => 'structure', 'members' => [], ], 'ReactiveAnomalies' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReactiveAnomalySummary', ], ], 'ReactiveAnomaly' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'AnomalyId', ], 'Severity' => [ 'shape' => 'AnomalySeverity', ], 'Status' => [ 'shape' => 'AnomalyStatus', ], 'AnomalyTimeRange' => [ 'shape' => 'AnomalyTimeRange', ], 'AnomalyReportedTimeRange' => [ 'shape' => 'AnomalyReportedTimeRange', ], 'SourceDetails' => [ 'shape' => 'AnomalySourceDetails', ], 'AssociatedInsightId' => [ 'shape' => 'InsightId', ], 'ResourceCollection' => [ 'shape' => 'ResourceCollection', ], 'Type' => [ 'shape' => 'AnomalyType', ], 'Name' => [ 'shape' => 'AnomalyName', ], 'Description' => [ 'shape' => 'AnomalyDescription', ], 'CausalAnomalyId' => [ 'shape' => 'AnomalyId', ], 'AnomalyResources' => [ 'shape' => 'AnomalyResources', ], ], ], 'ReactiveAnomalySummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'AnomalyId', ], 'Severity' => [ 'shape' => 'AnomalySeverity', ], 'Status' => [ 'shape' => 'AnomalyStatus', ], 'AnomalyTimeRange' => [ 'shape' => 'AnomalyTimeRange', ], 'AnomalyReportedTimeRange' => [ 'shape' => 'AnomalyReportedTimeRange', ], 'SourceDetails' => [ 'shape' => 'AnomalySourceDetails', ], 'AssociatedInsightId' => [ 'shape' => 'InsightId', ], 'ResourceCollection' => [ 'shape' => 'ResourceCollection', ], 'Type' => [ 'shape' => 'AnomalyType', ], 'Name' => [ 'shape' => 'AnomalyName', ], 'Description' => [ 'shape' => 'AnomalyDescription', ], 'CausalAnomalyId' => [ 'shape' => 'AnomalyId', ], 'AnomalyResources' => [ 'shape' => 'AnomalyResources', ], ], ], 'ReactiveInsight' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'InsightId', ], 'Name' => [ 'shape' => 'InsightName', ], 'Severity' => [ 'shape' => 'InsightSeverity', ], 'Status' => [ 'shape' => 'InsightStatus', ], 'InsightTimeRange' => [ 'shape' => 'InsightTimeRange', ], 'ResourceCollection' => [ 'shape' => 'ResourceCollection', ], 'SsmOpsItemId' => [ 'shape' => 'SsmOpsItemId', ], 'Description' => [ 'shape' => 'InsightDescription', ], ], ], 'ReactiveInsightSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'InsightId', ], 'Name' => [ 'shape' => 'InsightName', ], 'Severity' => [ 'shape' => 'InsightSeverity', ], 'Status' => [ 'shape' => 'InsightStatus', ], 'InsightTimeRange' => [ 'shape' => 'InsightTimeRange', ], 'ResourceCollection' => [ 'shape' => 'ResourceCollection', ], 'ServiceCollection' => [ 'shape' => 'ServiceCollection', ], 'AssociatedResourceArns' => [ 'shape' => 'AssociatedResourceArns', ], ], ], 'ReactiveInsights' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReactiveInsightSummary', ], ], 'ReactiveOrganizationInsightSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'InsightId', ], 'AccountId' => [ 'shape' => 'AwsAccountId', ], 'OrganizationalUnitId' => [ 'shape' => 'OrganizationalUnitId', ], 'Name' => [ 'shape' => 'InsightName', ], 'Severity' => [ 'shape' => 'InsightSeverity', ], 'Status' => [ 'shape' => 'InsightStatus', ], 'InsightTimeRange' => [ 'shape' => 'InsightTimeRange', ], 'ResourceCollection' => [ 'shape' => 'ResourceCollection', ], 'ServiceCollection' => [ 'shape' => 'ServiceCollection', ], ], ], 'ReactiveOrganizationInsights' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReactiveOrganizationInsightSummary', ], ], 'Recommendation' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => 'RecommendationDescription', ], 'Link' => [ 'shape' => 'RecommendationLink', ], 'Name' => [ 'shape' => 'RecommendationName', ], 'Reason' => [ 'shape' => 'RecommendationReason', ], 'RelatedEvents' => [ 'shape' => 'RecommendationRelatedEvents', ], 'RelatedAnomalies' => [ 'shape' => 'RecommendationRelatedAnomalies', ], 'Category' => [ 'shape' => 'RecommendationCategory', ], ], ], 'RecommendationCategory' => [ 'type' => 'string', ], 'RecommendationDescription' => [ 'type' => 'string', ], 'RecommendationLink' => [ 'type' => 'string', ], 'RecommendationName' => [ 'type' => 'string', ], 'RecommendationReason' => [ 'type' => 'string', ], 'RecommendationRelatedAnomalies' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendationRelatedAnomaly', ], ], 'RecommendationRelatedAnomaly' => [ 'type' => 'structure', 'members' => [ 'Resources' => [ 'shape' => 'RecommendationRelatedAnomalyResources', ], 'SourceDetails' => [ 'shape' => 'RelatedAnomalySourceDetails', ], 'AnomalyId' => [ 'shape' => 'AnomalyId', ], ], ], 'RecommendationRelatedAnomalyResource' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'RecommendationRelatedAnomalyResourceName', ], 'Type' => [ 'shape' => 'RecommendationRelatedAnomalyResourceType', ], ], ], 'RecommendationRelatedAnomalyResourceName' => [ 'type' => 'string', ], 'RecommendationRelatedAnomalyResourceType' => [ 'type' => 'string', ], 'RecommendationRelatedAnomalyResources' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendationRelatedAnomalyResource', ], ], 'RecommendationRelatedAnomalySourceDetail' => [ 'type' => 'structure', 'members' => [ 'CloudWatchMetrics' => [ 'shape' => 'RecommendationRelatedCloudWatchMetricsSourceDetails', ], ], ], 'RecommendationRelatedCloudWatchMetricsSourceDetail' => [ 'type' => 'structure', 'members' => [ 'MetricName' => [ 'shape' => 'RecommendationRelatedCloudWatchMetricsSourceMetricName', ], 'Namespace' => [ 'shape' => 'RecommendationRelatedCloudWatchMetricsSourceNamespace', ], ], ], 'RecommendationRelatedCloudWatchMetricsSourceDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendationRelatedCloudWatchMetricsSourceDetail', ], ], 'RecommendationRelatedCloudWatchMetricsSourceMetricName' => [ 'type' => 'string', ], 'RecommendationRelatedCloudWatchMetricsSourceNamespace' => [ 'type' => 'string', ], 'RecommendationRelatedEvent' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'RecommendationRelatedEventName', ], 'Resources' => [ 'shape' => 'RecommendationRelatedEventResources', ], ], ], 'RecommendationRelatedEventName' => [ 'type' => 'string', ], 'RecommendationRelatedEventResource' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'RecommendationRelatedEventResourceName', ], 'Type' => [ 'shape' => 'RecommendationRelatedEventResourceType', ], ], ], 'RecommendationRelatedEventResourceName' => [ 'type' => 'string', ], 'RecommendationRelatedEventResourceType' => [ 'type' => 'string', ], 'RecommendationRelatedEventResources' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendationRelatedEventResource', ], ], 'RecommendationRelatedEvents' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendationRelatedEvent', ], ], 'Recommendations' => [ 'type' => 'list', 'member' => [ 'shape' => 'Recommendation', ], 'max' => 10, 'min' => 0, ], 'RelatedAnomalySourceDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendationRelatedAnomalySourceDetail', ], ], 'RemoveNotificationChannelRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'NotificationChannelId', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'RemoveNotificationChannelResponse' => [ 'type' => 'structure', 'members' => [], ], 'ResourceArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, ], 'ResourceCollection' => [ 'type' => 'structure', 'members' => [ 'CloudFormation' => [ 'shape' => 'CloudFormationCollection', ], 'Tags' => [ 'shape' => 'TagCollections', ], ], ], 'ResourceCollectionFilter' => [ 'type' => 'structure', 'members' => [ 'CloudFormation' => [ 'shape' => 'CloudFormationCollectionFilter', ], 'Tags' => [ 'shape' => 'TagCollectionFilters', ], ], ], 'ResourceCollectionType' => [ 'type' => 'string', 'enum' => [ 'AWS_CLOUD_FORMATION', 'AWS_SERVICE', 'AWS_TAGS', ], ], 'ResourceHours' => [ 'type' => 'long', ], 'ResourceIdString' => [ 'type' => 'string', ], 'ResourceIdType' => [ 'type' => 'string', ], 'ResourceName' => [ 'type' => 'string', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'Message', 'ResourceId', 'ResourceType', ], 'members' => [ 'Message' => [ 'shape' => 'ErrorMessageString', ], 'ResourceId' => [ 'shape' => 'ResourceIdString', ], 'ResourceType' => [ 'shape' => 'ResourceIdType', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'ResourcePermission' => [ 'type' => 'string', 'enum' => [ 'FULL_PERMISSION', 'MISSING_PERMISSION', ], ], 'ResourceType' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^[a-zA-Z]+[a-zA-Z0-9-_:]*$', ], 'ResourceTypeFilter' => [ 'type' => 'string', 'enum' => [ 'LOG_GROUPS', 'CLOUDFRONT_DISTRIBUTION', 'DYNAMODB_TABLE', 'EC2_NAT_GATEWAY', 'ECS_CLUSTER', 'ECS_SERVICE', 'EKS_CLUSTER', 'ELASTIC_BEANSTALK_ENVIRONMENT', 'ELASTIC_LOAD_BALANCER_LOAD_BALANCER', 'ELASTIC_LOAD_BALANCING_V2_LOAD_BALANCER', 'ELASTIC_LOAD_BALANCING_V2_TARGET_GROUP', 'ELASTICACHE_CACHE_CLUSTER', 'ELASTICSEARCH_DOMAIN', 'KINESIS_STREAM', 'LAMBDA_FUNCTION', 'OPEN_SEARCH_SERVICE_DOMAIN', 'RDS_DB_INSTANCE', 'RDS_DB_CLUSTER', 'REDSHIFT_CLUSTER', 'ROUTE53_HOSTED_ZONE', 'ROUTE53_HEALTH_CHECK', 'S3_BUCKET', 'SAGEMAKER_ENDPOINT', 'SNS_TOPIC', 'SQS_QUEUE', 'STEP_FUNCTIONS_ACTIVITY', 'STEP_FUNCTIONS_STATE_MACHINE', ], ], 'ResourceTypeFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceTypeFilter', ], ], 'RetryAfterSeconds' => [ 'type' => 'integer', ], 'SearchInsightsAccountIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsAccountId', ], 'max' => 1, 'min' => 1, ], 'SearchInsightsFilters' => [ 'type' => 'structure', 'members' => [ 'Severities' => [ 'shape' => 'InsightSeverities', ], 'Statuses' => [ 'shape' => 'InsightStatuses', ], 'ResourceCollection' => [ 'shape' => 'ResourceCollection', ], 'ServiceCollection' => [ 'shape' => 'ServiceCollection', ], ], ], 'SearchInsightsMaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'SearchInsightsRequest' => [ 'type' => 'structure', 'required' => [ 'StartTimeRange', 'Type', ], 'members' => [ 'StartTimeRange' => [ 'shape' => 'StartTimeRange', ], 'Filters' => [ 'shape' => 'SearchInsightsFilters', ], 'MaxResults' => [ 'shape' => 'SearchInsightsMaxResults', ], 'NextToken' => [ 'shape' => 'UuidNextToken', ], 'Type' => [ 'shape' => 'InsightType', ], ], ], 'SearchInsightsResponse' => [ 'type' => 'structure', 'members' => [ 'ProactiveInsights' => [ 'shape' => 'ProactiveInsights', ], 'ReactiveInsights' => [ 'shape' => 'ReactiveInsights', ], 'NextToken' => [ 'shape' => 'UuidNextToken', ], ], ], 'SearchOrganizationInsightsFilters' => [ 'type' => 'structure', 'members' => [ 'Severities' => [ 'shape' => 'InsightSeverities', ], 'Statuses' => [ 'shape' => 'InsightStatuses', ], 'ResourceCollection' => [ 'shape' => 'ResourceCollection', ], 'ServiceCollection' => [ 'shape' => 'ServiceCollection', ], ], ], 'SearchOrganizationInsightsMaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'SearchOrganizationInsightsRequest' => [ 'type' => 'structure', 'required' => [ 'AccountIds', 'StartTimeRange', 'Type', ], 'members' => [ 'AccountIds' => [ 'shape' => 'SearchInsightsAccountIdList', ], 'StartTimeRange' => [ 'shape' => 'StartTimeRange', ], 'Filters' => [ 'shape' => 'SearchOrganizationInsightsFilters', ], 'MaxResults' => [ 'shape' => 'SearchOrganizationInsightsMaxResults', ], 'NextToken' => [ 'shape' => 'UuidNextToken', ], 'Type' => [ 'shape' => 'InsightType', ], ], ], 'SearchOrganizationInsightsResponse' => [ 'type' => 'structure', 'members' => [ 'ProactiveInsights' => [ 'shape' => 'ProactiveInsights', ], 'ReactiveInsights' => [ 'shape' => 'ReactiveInsights', ], 'NextToken' => [ 'shape' => 'UuidNextToken', ], ], ], 'ServerSideEncryptionType' => [ 'type' => 'string', 'enum' => [ 'CUSTOMER_MANAGED_KEY', 'AWS_OWNED_KMS_KEY', ], ], 'ServiceCollection' => [ 'type' => 'structure', 'members' => [ 'ServiceNames' => [ 'shape' => 'ServiceNames', ], ], ], 'ServiceHealth' => [ 'type' => 'structure', 'members' => [ 'ServiceName' => [ 'shape' => 'ServiceName', ], 'Insight' => [ 'shape' => 'ServiceInsightHealth', ], 'AnalyzedResourceCount' => [ 'shape' => 'AnalyzedResourceCount', ], ], ], 'ServiceHealths' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServiceHealth', ], ], 'ServiceInsightHealth' => [ 'type' => 'structure', 'members' => [ 'OpenProactiveInsights' => [ 'shape' => 'NumOpenProactiveInsights', ], 'OpenReactiveInsights' => [ 'shape' => 'NumOpenReactiveInsights', ], ], ], 'ServiceIntegrationConfig' => [ 'type' => 'structure', 'members' => [ 'OpsCenter' => [ 'shape' => 'OpsCenterIntegration', ], 'LogsAnomalyDetection' => [ 'shape' => 'LogsAnomalyDetectionIntegration', ], 'KMSServerSideEncryption' => [ 'shape' => 'KMSServerSideEncryptionIntegration', ], ], ], 'ServiceName' => [ 'type' => 'string', 'enum' => [ 'API_GATEWAY', 'APPLICATION_ELB', 'AUTO_SCALING_GROUP', 'CLOUD_FRONT', 'DYNAMO_DB', 'EC2', 'ECS', 'EKS', 'ELASTIC_BEANSTALK', 'ELASTI_CACHE', 'ELB', 'ES', 'KINESIS', 'LAMBDA', 'NAT_GATEWAY', 'NETWORK_ELB', 'RDS', 'REDSHIFT', 'ROUTE_53', 'S3', 'SAGE_MAKER', 'SNS', 'SQS', 'STEP_FUNCTIONS', 'SWF', ], ], 'ServiceNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServiceName', ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessageString', ], ], 'error' => [ 'httpStatusCode' => 402, ], 'exception' => true, ], 'ServiceResourceCost' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'ResourceType', ], 'State' => [ 'shape' => 'CostEstimationServiceResourceState', ], 'Count' => [ 'shape' => 'CostEstimationServiceResourceCount', ], 'UnitCost' => [ 'shape' => 'Cost', ], 'Cost' => [ 'shape' => 'Cost', ], ], ], 'ServiceResourceCosts' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServiceResourceCost', ], ], 'SnsChannelConfig' => [ 'type' => 'structure', 'members' => [ 'TopicArn' => [ 'shape' => 'TopicArn', ], ], ], 'SsmOpsItemId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^.*$', ], 'StackName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z*]+[a-zA-Z0-9-]*$', ], 'StackNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackName', ], ], 'StartCostEstimationRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceCollection', ], 'members' => [ 'ResourceCollection' => [ 'shape' => 'CostEstimationResourceCollectionFilter', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'StartCostEstimationResponse' => [ 'type' => 'structure', 'members' => [], ], 'StartTimeRange' => [ 'type' => 'structure', 'members' => [ 'FromTime' => [ 'shape' => 'Timestamp', ], 'ToTime' => [ 'shape' => 'Timestamp', ], ], ], 'TagCollection' => [ 'type' => 'structure', 'required' => [ 'AppBoundaryKey', 'TagValues', ], 'members' => [ 'AppBoundaryKey' => [ 'shape' => 'AppBoundaryKey', ], 'TagValues' => [ 'shape' => 'TagValues', ], ], ], 'TagCollectionFilter' => [ 'type' => 'structure', 'required' => [ 'AppBoundaryKey', 'TagValues', ], 'members' => [ 'AppBoundaryKey' => [ 'shape' => 'AppBoundaryKey', ], 'TagValues' => [ 'shape' => 'TagValues', ], ], ], 'TagCollectionFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagCollectionFilter', ], ], 'TagCollections' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagCollection', ], ], 'TagCostEstimationResourceCollectionFilter' => [ 'type' => 'structure', 'required' => [ 'AppBoundaryKey', 'TagValues', ], 'members' => [ 'AppBoundaryKey' => [ 'shape' => 'AppBoundaryKey', ], 'TagValues' => [ 'shape' => 'CostEstimationTagValues', ], ], ], 'TagCostEstimationResourceCollectionFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagCostEstimationResourceCollectionFilter', ], ], 'TagHealth' => [ 'type' => 'structure', 'members' => [ 'AppBoundaryKey' => [ 'shape' => 'AppBoundaryKey', ], 'TagValue' => [ 'shape' => 'TagValue', ], 'Insight' => [ 'shape' => 'InsightHealth', ], 'AnalyzedResourceCount' => [ 'shape' => 'AnalyzedResourceCount', ], ], ], 'TagHealths' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagHealth', ], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*|\\*)$', ], 'TagValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagValue', ], ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'ErrorMessageString', ], 'QuotaCode' => [ 'shape' => 'ErrorQuotaCodeString', ], 'ServiceCode' => [ 'shape' => 'ErrorServiceCodeString', ], 'RetryAfterSeconds' => [ 'shape' => 'RetryAfterSeconds', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TimestampMetricValuePair' => [ 'type' => 'structure', 'members' => [ 'Timestamp' => [ 'shape' => 'Timestamp', ], 'MetricValue' => [ 'shape' => 'MetricValue', ], ], ], 'TimestampMetricValuePairList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TimestampMetricValuePair', ], ], 'TopicArn' => [ 'type' => 'string', 'max' => 1024, 'min' => 36, 'pattern' => '^arn:aws[a-z0-9-]*:sns:[a-z0-9-]+:\\d{12}:[^:]+$', ], 'UpdateCloudFormationCollectionFilter' => [ 'type' => 'structure', 'members' => [ 'StackNames' => [ 'shape' => 'UpdateStackNames', ], ], ], 'UpdateEventSourcesConfigRequest' => [ 'type' => 'structure', 'members' => [ 'EventSources' => [ 'shape' => 'EventSourcesConfig', ], ], ], 'UpdateEventSourcesConfigResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateResourceCollectionAction' => [ 'type' => 'string', 'enum' => [ 'ADD', 'REMOVE', ], ], 'UpdateResourceCollectionFilter' => [ 'type' => 'structure', 'members' => [ 'CloudFormation' => [ 'shape' => 'UpdateCloudFormationCollectionFilter', ], 'Tags' => [ 'shape' => 'UpdateTagCollectionFilters', ], ], ], 'UpdateResourceCollectionRequest' => [ 'type' => 'structure', 'required' => [ 'Action', 'ResourceCollection', ], 'members' => [ 'Action' => [ 'shape' => 'UpdateResourceCollectionAction', ], 'ResourceCollection' => [ 'shape' => 'UpdateResourceCollectionFilter', ], ], ], 'UpdateResourceCollectionResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateServiceIntegrationConfig' => [ 'type' => 'structure', 'members' => [ 'OpsCenter' => [ 'shape' => 'OpsCenterIntegrationConfig', ], 'LogsAnomalyDetection' => [ 'shape' => 'LogsAnomalyDetectionIntegrationConfig', ], 'KMSServerSideEncryption' => [ 'shape' => 'KMSServerSideEncryptionIntegrationConfig', ], ], ], 'UpdateServiceIntegrationRequest' => [ 'type' => 'structure', 'required' => [ 'ServiceIntegration', ], 'members' => [ 'ServiceIntegration' => [ 'shape' => 'UpdateServiceIntegrationConfig', ], ], ], 'UpdateServiceIntegrationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateStackNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackName', ], 'max' => 100, 'min' => 0, ], 'UpdateTagCollectionFilter' => [ 'type' => 'structure', 'required' => [ 'AppBoundaryKey', 'TagValues', ], 'members' => [ 'AppBoundaryKey' => [ 'shape' => 'AppBoundaryKey', ], 'TagValues' => [ 'shape' => 'UpdateTagValues', ], ], ], 'UpdateTagCollectionFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'UpdateTagCollectionFilter', ], ], 'UpdateTagValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagValue', ], 'max' => 100, 'min' => 0, ], 'UuidNextToken' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$', ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'ErrorMessageString', ], 'Reason' => [ 'shape' => 'ValidationExceptionReason', ], 'Fields' => [ 'shape' => 'ValidationExceptionFields', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'Name', 'Message', ], 'members' => [ 'Name' => [ 'shape' => 'ErrorNameString', ], 'Message' => [ 'shape' => 'ErrorMessageString', ], ], ], 'ValidationExceptionFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'UNKNOWN_OPERATION', 'CANNOT_PARSE', 'FIELD_VALIDATION_FAILED', 'OTHER', 'INVALID_PARAMETER_COMBINATION', 'PARAMETER_INCONSISTENT_WITH_SERVICE_STATE', ], ], ],];
