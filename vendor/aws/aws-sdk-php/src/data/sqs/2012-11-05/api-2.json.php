<?php
// This file was auto-generated from sdk-root/src/data/sqs/2012-11-05/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2012-11-05', 'awsQueryCompatible' => [], 'endpointPrefix' => 'sqs', 'jsonVersion' => '1.0', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceAbbreviation' => 'Amazon SQS', 'serviceFullName' => 'Amazon Simple Queue Service', 'serviceId' => 'SQS', 'signatureVersion' => 'v4', 'targetPrefix' => 'AmazonSQS', 'uid' => 'sqs-2012-11-05', ], 'operations' => [ 'AddPermission' => [ 'name' => 'AddPermission', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddPermissionRequest', ], 'errors' => [ [ 'shape' => 'OverLimit', ], [ 'shape' => 'RequestThrottled', ], [ 'shape' => 'QueueDoesNotExist', ], [ 'shape' => 'InvalidAddress', ], [ 'shape' => 'InvalidSecurity', ], [ 'shape' => 'UnsupportedOperation', ], ], ], 'CancelMessageMoveTask' => [ 'name' => 'CancelMessageMoveTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CancelMessageMoveTaskRequest', ], 'output' => [ 'shape' => 'CancelMessageMoveTaskResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'RequestThrottled', ], [ 'shape' => 'InvalidAddress', ], [ 'shape' => 'InvalidSecurity', ], [ 'shape' => 'UnsupportedOperation', ], ], ], 'ChangeMessageVisibility' => [ 'name' => 'ChangeMessageVisibility', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ChangeMessageVisibilityRequest', ], 'errors' => [ [ 'shape' => 'MessageNotInflight', ], [ 'shape' => 'ReceiptHandleIsInvalid', ], [ 'shape' => 'RequestThrottled', ], [ 'shape' => 'QueueDoesNotExist', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'InvalidAddress', ], [ 'shape' => 'InvalidSecurity', ], ], ], 'ChangeMessageVisibilityBatch' => [ 'name' => 'ChangeMessageVisibilityBatch', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ChangeMessageVisibilityBatchRequest', ], 'output' => [ 'shape' => 'ChangeMessageVisibilityBatchResult', ], 'errors' => [ [ 'shape' => 'TooManyEntriesInBatchRequest', ], [ 'shape' => 'EmptyBatchRequest', ], [ 'shape' => 'BatchEntryIdsNotDistinct', ], [ 'shape' => 'InvalidBatchEntryId', ], [ 'shape' => 'RequestThrottled', ], [ 'shape' => 'QueueDoesNotExist', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'InvalidAddress', ], [ 'shape' => 'InvalidSecurity', ], ], ], 'CreateQueue' => [ 'name' => 'CreateQueue', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateQueueRequest', ], 'output' => [ 'shape' => 'CreateQueueResult', ], 'errors' => [ [ 'shape' => 'QueueDeletedRecently', ], [ 'shape' => 'QueueNameExists', ], [ 'shape' => 'RequestThrottled', ], [ 'shape' => 'InvalidAddress', ], [ 'shape' => 'InvalidAttributeName', ], [ 'shape' => 'InvalidAttributeValue', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'InvalidSecurity', ], ], ], 'DeleteMessage' => [ 'name' => 'DeleteMessage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteMessageRequest', ], 'errors' => [ [ 'shape' => 'InvalidIdFormat', ], [ 'shape' => 'ReceiptHandleIsInvalid', ], [ 'shape' => 'RequestThrottled', ], [ 'shape' => 'QueueDoesNotExist', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'InvalidSecurity', ], [ 'shape' => 'InvalidAddress', ], ], ], 'DeleteMessageBatch' => [ 'name' => 'DeleteMessageBatch', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteMessageBatchRequest', ], 'output' => [ 'shape' => 'DeleteMessageBatchResult', ], 'errors' => [ [ 'shape' => 'TooManyEntriesInBatchRequest', ], [ 'shape' => 'EmptyBatchRequest', ], [ 'shape' => 'BatchEntryIdsNotDistinct', ], [ 'shape' => 'InvalidBatchEntryId', ], [ 'shape' => 'RequestThrottled', ], [ 'shape' => 'QueueDoesNotExist', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'InvalidAddress', ], [ 'shape' => 'InvalidSecurity', ], ], ], 'DeleteQueue' => [ 'name' => 'DeleteQueue', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteQueueRequest', ], 'errors' => [ [ 'shape' => 'RequestThrottled', ], [ 'shape' => 'QueueDoesNotExist', ], [ 'shape' => 'InvalidAddress', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'InvalidSecurity', ], ], ], 'GetQueueAttributes' => [ 'name' => 'GetQueueAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetQueueAttributesRequest', ], 'output' => [ 'shape' => 'GetQueueAttributesResult', ], 'errors' => [ [ 'shape' => 'InvalidAttributeName', ], [ 'shape' => 'RequestThrottled', ], [ 'shape' => 'QueueDoesNotExist', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'InvalidSecurity', ], [ 'shape' => 'InvalidAddress', ], ], ], 'GetQueueUrl' => [ 'name' => 'GetQueueUrl', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetQueueUrlRequest', ], 'output' => [ 'shape' => 'GetQueueUrlResult', ], 'errors' => [ [ 'shape' => 'RequestThrottled', ], [ 'shape' => 'QueueDoesNotExist', ], [ 'shape' => 'InvalidAddress', ], [ 'shape' => 'InvalidSecurity', ], [ 'shape' => 'UnsupportedOperation', ], ], ], 'ListDeadLetterSourceQueues' => [ 'name' => 'ListDeadLetterSourceQueues', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDeadLetterSourceQueuesRequest', ], 'output' => [ 'shape' => 'ListDeadLetterSourceQueuesResult', ], 'errors' => [ [ 'shape' => 'QueueDoesNotExist', ], [ 'shape' => 'RequestThrottled', ], [ 'shape' => 'InvalidSecurity', ], [ 'shape' => 'InvalidAddress', ], [ 'shape' => 'UnsupportedOperation', ], ], ], 'ListMessageMoveTasks' => [ 'name' => 'ListMessageMoveTasks', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListMessageMoveTasksRequest', ], 'output' => [ 'shape' => 'ListMessageMoveTasksResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'RequestThrottled', ], [ 'shape' => 'InvalidAddress', ], [ 'shape' => 'InvalidSecurity', ], [ 'shape' => 'UnsupportedOperation', ], ], ], 'ListQueueTags' => [ 'name' => 'ListQueueTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListQueueTagsRequest', ], 'output' => [ 'shape' => 'ListQueueTagsResult', ], 'errors' => [ [ 'shape' => 'RequestThrottled', ], [ 'shape' => 'QueueDoesNotExist', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'InvalidAddress', ], [ 'shape' => 'InvalidSecurity', ], ], ], 'ListQueues' => [ 'name' => 'ListQueues', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListQueuesRequest', ], 'output' => [ 'shape' => 'ListQueuesResult', ], 'errors' => [ [ 'shape' => 'RequestThrottled', ], [ 'shape' => 'InvalidSecurity', ], [ 'shape' => 'InvalidAddress', ], [ 'shape' => 'UnsupportedOperation', ], ], ], 'PurgeQueue' => [ 'name' => 'PurgeQueue', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PurgeQueueRequest', ], 'errors' => [ [ 'shape' => 'QueueDoesNotExist', ], [ 'shape' => 'PurgeQueueInProgress', ], [ 'shape' => 'RequestThrottled', ], [ 'shape' => 'InvalidAddress', ], [ 'shape' => 'InvalidSecurity', ], [ 'shape' => 'UnsupportedOperation', ], ], ], 'ReceiveMessage' => [ 'name' => 'ReceiveMessage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ReceiveMessageRequest', ], 'output' => [ 'shape' => 'ReceiveMessageResult', ], 'errors' => [ [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'OverLimit', ], [ 'shape' => 'RequestThrottled', ], [ 'shape' => 'QueueDoesNotExist', ], [ 'shape' => 'InvalidSecurity', ], [ 'shape' => 'KmsDisabled', ], [ 'shape' => 'KmsInvalidState', ], [ 'shape' => 'KmsNotFound', ], [ 'shape' => 'KmsOptInRequired', ], [ 'shape' => 'KmsThrottled', ], [ 'shape' => 'KmsAccessDenied', ], [ 'shape' => 'KmsInvalidKeyUsage', ], [ 'shape' => 'InvalidAddress', ], ], ], 'RemovePermission' => [ 'name' => 'RemovePermission', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemovePermissionRequest', ], 'errors' => [ [ 'shape' => 'InvalidAddress', ], [ 'shape' => 'RequestThrottled', ], [ 'shape' => 'QueueDoesNotExist', ], [ 'shape' => 'InvalidSecurity', ], [ 'shape' => 'UnsupportedOperation', ], ], ], 'SendMessage' => [ 'name' => 'SendMessage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SendMessageRequest', ], 'output' => [ 'shape' => 'SendMessageResult', ], 'errors' => [ [ 'shape' => 'InvalidMessageContents', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'RequestThrottled', ], [ 'shape' => 'QueueDoesNotExist', ], [ 'shape' => 'InvalidSecurity', ], [ 'shape' => 'KmsDisabled', ], [ 'shape' => 'KmsInvalidState', ], [ 'shape' => 'KmsNotFound', ], [ 'shape' => 'KmsOptInRequired', ], [ 'shape' => 'KmsThrottled', ], [ 'shape' => 'KmsAccessDenied', ], [ 'shape' => 'KmsInvalidKeyUsage', ], [ 'shape' => 'InvalidAddress', ], ], ], 'SendMessageBatch' => [ 'name' => 'SendMessageBatch', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SendMessageBatchRequest', ], 'output' => [ 'shape' => 'SendMessageBatchResult', ], 'errors' => [ [ 'shape' => 'TooManyEntriesInBatchRequest', ], [ 'shape' => 'EmptyBatchRequest', ], [ 'shape' => 'BatchEntryIdsNotDistinct', ], [ 'shape' => 'BatchRequestTooLong', ], [ 'shape' => 'InvalidBatchEntryId', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'RequestThrottled', ], [ 'shape' => 'QueueDoesNotExist', ], [ 'shape' => 'InvalidSecurity', ], [ 'shape' => 'KmsDisabled', ], [ 'shape' => 'KmsInvalidState', ], [ 'shape' => 'KmsNotFound', ], [ 'shape' => 'KmsOptInRequired', ], [ 'shape' => 'KmsThrottled', ], [ 'shape' => 'KmsAccessDenied', ], [ 'shape' => 'KmsInvalidKeyUsage', ], [ 'shape' => 'InvalidAddress', ], ], ], 'SetQueueAttributes' => [ 'name' => 'SetQueueAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetQueueAttributesRequest', ], 'errors' => [ [ 'shape' => 'InvalidAttributeName', ], [ 'shape' => 'InvalidAttributeValue', ], [ 'shape' => 'RequestThrottled', ], [ 'shape' => 'QueueDoesNotExist', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'OverLimit', ], [ 'shape' => 'InvalidAddress', ], [ 'shape' => 'InvalidSecurity', ], ], ], 'StartMessageMoveTask' => [ 'name' => 'StartMessageMoveTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartMessageMoveTaskRequest', ], 'output' => [ 'shape' => 'StartMessageMoveTaskResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'RequestThrottled', ], [ 'shape' => 'InvalidAddress', ], [ 'shape' => 'InvalidSecurity', ], [ 'shape' => 'UnsupportedOperation', ], ], ], 'TagQueue' => [ 'name' => 'TagQueue', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagQueueRequest', ], 'errors' => [ [ 'shape' => 'InvalidAddress', ], [ 'shape' => 'RequestThrottled', ], [ 'shape' => 'QueueDoesNotExist', ], [ 'shape' => 'InvalidSecurity', ], [ 'shape' => 'UnsupportedOperation', ], ], ], 'UntagQueue' => [ 'name' => 'UntagQueue', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagQueueRequest', ], 'errors' => [ [ 'shape' => 'InvalidAddress', ], [ 'shape' => 'RequestThrottled', ], [ 'shape' => 'QueueDoesNotExist', ], [ 'shape' => 'InvalidSecurity', ], [ 'shape' => 'UnsupportedOperation', ], ], ], ], 'shapes' => [ 'AWSAccountIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'flattened' => true, ], 'ActionNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'flattened' => true, ], 'AddPermissionRequest' => [ 'type' => 'structure', 'required' => [ 'QueueUrl', 'Label', 'AWSAccountIds', 'Actions', ], 'members' => [ 'QueueUrl' => [ 'shape' => 'String', ], 'Label' => [ 'shape' => 'String', ], 'AWSAccountIds' => [ 'shape' => 'AWSAccountIdList', ], 'Actions' => [ 'shape' => 'ActionNameList', ], ], ], 'AttributeNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueueAttributeName', ], 'flattened' => true, ], 'BatchEntryIdsNotDistinct' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'BatchRequestTooLong' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'BatchResultErrorEntry' => [ 'type' => 'structure', 'required' => [ 'Id', 'SenderFault', 'Code', ], 'members' => [ 'Id' => [ 'shape' => 'String', ], 'SenderFault' => [ 'shape' => 'Boolean', ], 'Code' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], ], 'BatchResultErrorEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchResultErrorEntry', ], 'flattened' => true, ], 'Binary' => [ 'type' => 'blob', ], 'BinaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Binary', ], ], 'Boolean' => [ 'type' => 'boolean', ], 'BoxedInteger' => [ 'type' => 'integer', 'box' => true, ], 'CancelMessageMoveTaskRequest' => [ 'type' => 'structure', 'required' => [ 'TaskHandle', ], 'members' => [ 'TaskHandle' => [ 'shape' => 'String', ], ], ], 'CancelMessageMoveTaskResult' => [ 'type' => 'structure', 'members' => [ 'ApproximateNumberOfMessagesMoved' => [ 'shape' => 'Long', ], ], ], 'ChangeMessageVisibilityBatchRequest' => [ 'type' => 'structure', 'required' => [ 'QueueUrl', 'Entries', ], 'members' => [ 'QueueUrl' => [ 'shape' => 'String', ], 'Entries' => [ 'shape' => 'ChangeMessageVisibilityBatchRequestEntryList', ], ], ], 'ChangeMessageVisibilityBatchRequestEntry' => [ 'type' => 'structure', 'required' => [ 'Id', 'ReceiptHandle', ], 'members' => [ 'Id' => [ 'shape' => 'String', ], 'ReceiptHandle' => [ 'shape' => 'String', ], 'VisibilityTimeout' => [ 'shape' => 'NullableInteger', ], ], ], 'ChangeMessageVisibilityBatchRequestEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChangeMessageVisibilityBatchRequestEntry', ], 'flattened' => true, ], 'ChangeMessageVisibilityBatchResult' => [ 'type' => 'structure', 'required' => [ 'Successful', 'Failed', ], 'members' => [ 'Successful' => [ 'shape' => 'ChangeMessageVisibilityBatchResultEntryList', ], 'Failed' => [ 'shape' => 'BatchResultErrorEntryList', ], ], ], 'ChangeMessageVisibilityBatchResultEntry' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'String', ], ], ], 'ChangeMessageVisibilityBatchResultEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChangeMessageVisibilityBatchResultEntry', ], 'flattened' => true, ], 'ChangeMessageVisibilityRequest' => [ 'type' => 'structure', 'required' => [ 'QueueUrl', 'ReceiptHandle', 'VisibilityTimeout', ], 'members' => [ 'QueueUrl' => [ 'shape' => 'String', ], 'ReceiptHandle' => [ 'shape' => 'String', ], 'VisibilityTimeout' => [ 'shape' => 'NullableInteger', ], ], ], 'CreateQueueRequest' => [ 'type' => 'structure', 'required' => [ 'QueueName', ], 'members' => [ 'QueueName' => [ 'shape' => 'String', ], 'Attributes' => [ 'shape' => 'QueueAttributeMap', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateQueueResult' => [ 'type' => 'structure', 'members' => [ 'QueueUrl' => [ 'shape' => 'String', ], ], ], 'DeleteMessageBatchRequest' => [ 'type' => 'structure', 'required' => [ 'QueueUrl', 'Entries', ], 'members' => [ 'QueueUrl' => [ 'shape' => 'String', ], 'Entries' => [ 'shape' => 'DeleteMessageBatchRequestEntryList', ], ], ], 'DeleteMessageBatchRequestEntry' => [ 'type' => 'structure', 'required' => [ 'Id', 'ReceiptHandle', ], 'members' => [ 'Id' => [ 'shape' => 'String', ], 'ReceiptHandle' => [ 'shape' => 'String', ], ], ], 'DeleteMessageBatchRequestEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeleteMessageBatchRequestEntry', ], 'flattened' => true, ], 'DeleteMessageBatchResult' => [ 'type' => 'structure', 'required' => [ 'Successful', 'Failed', ], 'members' => [ 'Successful' => [ 'shape' => 'DeleteMessageBatchResultEntryList', ], 'Failed' => [ 'shape' => 'BatchResultErrorEntryList', ], ], ], 'DeleteMessageBatchResultEntry' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'String', ], ], ], 'DeleteMessageBatchResultEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeleteMessageBatchResultEntry', ], 'flattened' => true, ], 'DeleteMessageRequest' => [ 'type' => 'structure', 'required' => [ 'QueueUrl', 'ReceiptHandle', ], 'members' => [ 'QueueUrl' => [ 'shape' => 'String', ], 'ReceiptHandle' => [ 'shape' => 'String', ], ], ], 'DeleteQueueRequest' => [ 'type' => 'structure', 'required' => [ 'QueueUrl', ], 'members' => [ 'QueueUrl' => [ 'shape' => 'String', ], ], ], 'EmptyBatchRequest' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'ExceptionMessage' => [ 'type' => 'string', ], 'GetQueueAttributesRequest' => [ 'type' => 'structure', 'required' => [ 'QueueUrl', ], 'members' => [ 'QueueUrl' => [ 'shape' => 'String', ], 'AttributeNames' => [ 'shape' => 'AttributeNameList', ], ], ], 'GetQueueAttributesResult' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'QueueAttributeMap', ], ], ], 'GetQueueUrlRequest' => [ 'type' => 'structure', 'required' => [ 'QueueName', ], 'members' => [ 'QueueName' => [ 'shape' => 'String', ], 'QueueOwnerAWSAccountId' => [ 'shape' => 'String', ], ], ], 'GetQueueUrlResult' => [ 'type' => 'structure', 'members' => [ 'QueueUrl' => [ 'shape' => 'String', ], ], ], 'InvalidAddress' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'InvalidAttributeName' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'InvalidAttributeValue' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'InvalidBatchEntryId' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'InvalidIdFormat' => [ 'type' => 'structure', 'members' => [], 'deprecated' => true, 'deprecatedMessage' => 'exception has been included in ReceiptHandleIsInvalid', 'exception' => true, ], 'InvalidMessageContents' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'InvalidSecurity' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'KmsAccessDenied' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'KmsDisabled' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'KmsInvalidKeyUsage' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'KmsInvalidState' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'KmsNotFound' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'KmsOptInRequired' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'KmsThrottled' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'ListDeadLetterSourceQueuesRequest' => [ 'type' => 'structure', 'required' => [ 'QueueUrl', ], 'members' => [ 'QueueUrl' => [ 'shape' => 'String', ], 'NextToken' => [ 'shape' => 'Token', ], 'MaxResults' => [ 'shape' => 'BoxedInteger', ], ], ], 'ListDeadLetterSourceQueuesResult' => [ 'type' => 'structure', 'required' => [ 'queueUrls', ], 'members' => [ 'queueUrls' => [ 'shape' => 'QueueUrlList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListMessageMoveTasksRequest' => [ 'type' => 'structure', 'required' => [ 'SourceArn', ], 'members' => [ 'SourceArn' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'NullableInteger', ], ], ], 'ListMessageMoveTasksResult' => [ 'type' => 'structure', 'members' => [ 'Results' => [ 'shape' => 'ListMessageMoveTasksResultEntryList', 'flattened' => true, ], ], ], 'ListMessageMoveTasksResultEntry' => [ 'type' => 'structure', 'members' => [ 'TaskHandle' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'SourceArn' => [ 'shape' => 'String', ], 'DestinationArn' => [ 'shape' => 'String', ], 'MaxNumberOfMessagesPerSecond' => [ 'shape' => 'NullableInteger', ], 'ApproximateNumberOfMessagesMoved' => [ 'shape' => 'Long', ], 'ApproximateNumberOfMessagesToMove' => [ 'shape' => 'NullableLong', ], 'FailureReason' => [ 'shape' => 'String', ], 'StartedTimestamp' => [ 'shape' => 'Long', ], ], ], 'ListMessageMoveTasksResultEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListMessageMoveTasksResultEntry', ], 'flattened' => true, ], 'ListQueueTagsRequest' => [ 'type' => 'structure', 'required' => [ 'QueueUrl', ], 'members' => [ 'QueueUrl' => [ 'shape' => 'String', ], ], ], 'ListQueueTagsResult' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'ListQueuesRequest' => [ 'type' => 'structure', 'members' => [ 'QueueNamePrefix' => [ 'shape' => 'String', ], 'NextToken' => [ 'shape' => 'Token', ], 'MaxResults' => [ 'shape' => 'BoxedInteger', ], ], ], 'ListQueuesResult' => [ 'type' => 'structure', 'members' => [ 'QueueUrls' => [ 'shape' => 'QueueUrlList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'Long' => [ 'type' => 'long', ], 'Message' => [ 'type' => 'structure', 'members' => [ 'MessageId' => [ 'shape' => 'String', ], 'ReceiptHandle' => [ 'shape' => 'String', ], 'MD5OfBody' => [ 'shape' => 'String', ], 'Body' => [ 'shape' => 'String', ], 'Attributes' => [ 'shape' => 'MessageSystemAttributeMap', ], 'MD5OfMessageAttributes' => [ 'shape' => 'String', ], 'MessageAttributes' => [ 'shape' => 'MessageBodyAttributeMap', ], ], ], 'MessageAttributeName' => [ 'type' => 'string', ], 'MessageAttributeNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MessageAttributeName', ], 'flattened' => true, ], 'MessageAttributeValue' => [ 'type' => 'structure', 'required' => [ 'DataType', ], 'members' => [ 'StringValue' => [ 'shape' => 'String', ], 'BinaryValue' => [ 'shape' => 'Binary', ], 'StringListValues' => [ 'shape' => 'StringList', 'flattened' => true, ], 'BinaryListValues' => [ 'shape' => 'BinaryList', 'flattened' => true, ], 'DataType' => [ 'shape' => 'String', ], ], ], 'MessageBodyAttributeMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'MessageAttributeValue', ], 'flattened' => true, ], 'MessageBodySystemAttributeMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'MessageSystemAttributeNameForSends', ], 'value' => [ 'shape' => 'MessageSystemAttributeValue', ], 'flattened' => true, ], 'MessageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Message', ], 'flattened' => true, ], 'MessageNotInflight' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'MessageSystemAttributeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MessageSystemAttributeName', ], 'flattened' => true, ], 'MessageSystemAttributeMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'MessageSystemAttributeName', ], 'value' => [ 'shape' => 'String', ], 'flattened' => true, ], 'MessageSystemAttributeName' => [ 'type' => 'string', 'enum' => [ 'All', 'SenderId', 'SentTimestamp', 'ApproximateReceiveCount', 'ApproximateFirstReceiveTimestamp', 'SequenceNumber', 'MessageDeduplicationId', 'MessageGroupId', 'AWSTraceHeader', 'DeadLetterQueueSourceArn', ], ], 'MessageSystemAttributeNameForSends' => [ 'type' => 'string', 'enum' => [ 'AWSTraceHeader', ], ], 'MessageSystemAttributeValue' => [ 'type' => 'structure', 'required' => [ 'DataType', ], 'members' => [ 'StringValue' => [ 'shape' => 'String', ], 'BinaryValue' => [ 'shape' => 'Binary', ], 'StringListValues' => [ 'shape' => 'StringList', 'flattened' => true, ], 'BinaryListValues' => [ 'shape' => 'BinaryList', 'flattened' => true, ], 'DataType' => [ 'shape' => 'String', ], ], ], 'NullableInteger' => [ 'type' => 'integer', ], 'NullableLong' => [ 'type' => 'long', ], 'OverLimit' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'PurgeQueueInProgress' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'PurgeQueueRequest' => [ 'type' => 'structure', 'required' => [ 'QueueUrl', ], 'members' => [ 'QueueUrl' => [ 'shape' => 'String', ], ], ], 'QueueAttributeMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'QueueAttributeName', ], 'value' => [ 'shape' => 'String', ], 'flattened' => true, ], 'QueueAttributeName' => [ 'type' => 'string', 'enum' => [ 'All', 'Policy', 'VisibilityTimeout', 'MaximumMessageSize', 'MessageRetentionPeriod', 'ApproximateNumberOfMessages', 'ApproximateNumberOfMessagesNotVisible', 'CreatedTimestamp', 'LastModifiedTimestamp', 'QueueArn', 'ApproximateNumberOfMessagesDelayed', 'DelaySeconds', 'ReceiveMessageWaitTimeSeconds', 'RedrivePolicy', 'FifoQueue', 'ContentBasedDeduplication', 'KmsMasterKeyId', 'KmsDataKeyReusePeriodSeconds', 'DeduplicationScope', 'FifoThroughputLimit', 'RedriveAllowPolicy', 'SqsManagedSseEnabled', ], ], 'QueueDeletedRecently' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'QueueDoesNotExist' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'QueueNameExists' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'QueueUrlList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'flattened' => true, ], 'ReceiptHandleIsInvalid' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'ReceiveMessageRequest' => [ 'type' => 'structure', 'required' => [ 'QueueUrl', ], 'members' => [ 'QueueUrl' => [ 'shape' => 'String', ], 'AttributeNames' => [ 'shape' => 'AttributeNameList', 'deprecated' => true, 'deprecatedMessage' => 'AttributeNames has been replaced by MessageSystemAttributeNames', ], 'MessageSystemAttributeNames' => [ 'shape' => 'MessageSystemAttributeList', ], 'MessageAttributeNames' => [ 'shape' => 'MessageAttributeNameList', ], 'MaxNumberOfMessages' => [ 'shape' => 'NullableInteger', ], 'VisibilityTimeout' => [ 'shape' => 'NullableInteger', ], 'WaitTimeSeconds' => [ 'shape' => 'NullableInteger', ], 'ReceiveRequestAttemptId' => [ 'shape' => 'String', ], ], ], 'ReceiveMessageResult' => [ 'type' => 'structure', 'members' => [ 'Messages' => [ 'shape' => 'MessageList', ], ], ], 'RemovePermissionRequest' => [ 'type' => 'structure', 'required' => [ 'QueueUrl', 'Label', ], 'members' => [ 'QueueUrl' => [ 'shape' => 'String', ], 'Label' => [ 'shape' => 'String', ], ], ], 'RequestThrottled' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'SendMessageBatchRequest' => [ 'type' => 'structure', 'required' => [ 'QueueUrl', 'Entries', ], 'members' => [ 'QueueUrl' => [ 'shape' => 'String', ], 'Entries' => [ 'shape' => 'SendMessageBatchRequestEntryList', ], ], ], 'SendMessageBatchRequestEntry' => [ 'type' => 'structure', 'required' => [ 'Id', 'MessageBody', ], 'members' => [ 'Id' => [ 'shape' => 'String', ], 'MessageBody' => [ 'shape' => 'String', ], 'DelaySeconds' => [ 'shape' => 'NullableInteger', ], 'MessageAttributes' => [ 'shape' => 'MessageBodyAttributeMap', ], 'MessageSystemAttributes' => [ 'shape' => 'MessageBodySystemAttributeMap', ], 'MessageDeduplicationId' => [ 'shape' => 'String', ], 'MessageGroupId' => [ 'shape' => 'String', ], ], ], 'SendMessageBatchRequestEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SendMessageBatchRequestEntry', ], 'flattened' => true, ], 'SendMessageBatchResult' => [ 'type' => 'structure', 'required' => [ 'Successful', 'Failed', ], 'members' => [ 'Successful' => [ 'shape' => 'SendMessageBatchResultEntryList', ], 'Failed' => [ 'shape' => 'BatchResultErrorEntryList', ], ], ], 'SendMessageBatchResultEntry' => [ 'type' => 'structure', 'required' => [ 'Id', 'MessageId', 'MD5OfMessageBody', ], 'members' => [ 'Id' => [ 'shape' => 'String', ], 'MessageId' => [ 'shape' => 'String', ], 'MD5OfMessageBody' => [ 'shape' => 'String', ], 'MD5OfMessageAttributes' => [ 'shape' => 'String', ], 'MD5OfMessageSystemAttributes' => [ 'shape' => 'String', ], 'SequenceNumber' => [ 'shape' => 'String', ], ], ], 'SendMessageBatchResultEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SendMessageBatchResultEntry', ], 'flattened' => true, ], 'SendMessageRequest' => [ 'type' => 'structure', 'required' => [ 'QueueUrl', 'MessageBody', ], 'members' => [ 'QueueUrl' => [ 'shape' => 'String', ], 'MessageBody' => [ 'shape' => 'String', ], 'DelaySeconds' => [ 'shape' => 'NullableInteger', ], 'MessageAttributes' => [ 'shape' => 'MessageBodyAttributeMap', ], 'MessageSystemAttributes' => [ 'shape' => 'MessageBodySystemAttributeMap', ], 'MessageDeduplicationId' => [ 'shape' => 'String', ], 'MessageGroupId' => [ 'shape' => 'String', ], ], ], 'SendMessageResult' => [ 'type' => 'structure', 'members' => [ 'MD5OfMessageBody' => [ 'shape' => 'String', ], 'MD5OfMessageAttributes' => [ 'shape' => 'String', ], 'MD5OfMessageSystemAttributes' => [ 'shape' => 'String', ], 'MessageId' => [ 'shape' => 'String', ], 'SequenceNumber' => [ 'shape' => 'String', ], ], ], 'SetQueueAttributesRequest' => [ 'type' => 'structure', 'required' => [ 'QueueUrl', 'Attributes', ], 'members' => [ 'QueueUrl' => [ 'shape' => 'String', ], 'Attributes' => [ 'shape' => 'QueueAttributeMap', ], ], ], 'StartMessageMoveTaskRequest' => [ 'type' => 'structure', 'required' => [ 'SourceArn', ], 'members' => [ 'SourceArn' => [ 'shape' => 'String', ], 'DestinationArn' => [ 'shape' => 'String', ], 'MaxNumberOfMessagesPerSecond' => [ 'shape' => 'NullableInteger', ], ], ], 'StartMessageMoveTaskResult' => [ 'type' => 'structure', 'members' => [ 'TaskHandle' => [ 'shape' => 'String', ], ], ], 'String' => [ 'type' => 'string', ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'TagKey' => [ 'type' => 'string', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'flattened' => true, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'flattened' => true, ], 'TagQueueRequest' => [ 'type' => 'structure', 'required' => [ 'QueueUrl', 'Tags', ], 'members' => [ 'QueueUrl' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'TagValue' => [ 'type' => 'string', ], 'Token' => [ 'type' => 'string', ], 'TooManyEntriesInBatchRequest' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'UnsupportedOperation' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'UntagQueueRequest' => [ 'type' => 'structure', 'required' => [ 'QueueUrl', 'TagKeys', ], 'members' => [ 'QueueUrl' => [ 'shape' => 'String', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], ],];
