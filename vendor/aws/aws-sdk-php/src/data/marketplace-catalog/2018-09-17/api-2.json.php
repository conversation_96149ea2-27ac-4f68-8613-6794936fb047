<?php
// This file was auto-generated from sdk-root/src/data/marketplace-catalog/2018-09-17/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-09-17', 'endpointPrefix' => 'catalog.marketplace', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceAbbreviation' => 'AWS Marketplace Catalog', 'serviceFullName' => 'AWS Marketplace Catalog Service', 'serviceId' => 'Marketplace Catalog', 'signatureVersion' => 'v4', 'signingName' => 'aws-marketplace', 'uid' => 'marketplace-catalog-2018-09-17', ], 'operations' => [ 'BatchDescribeEntities' => [ 'name' => 'BatchDescribeEntities', 'http' => [ 'method' => 'POST', 'requestUri' => '/BatchDescribeEntities', ], 'input' => [ 'shape' => 'BatchDescribeEntitiesRequest', ], 'output' => [ 'shape' => 'BatchDescribeEntitiesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'CancelChangeSet' => [ 'name' => 'CancelChangeSet', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/CancelChangeSet', ], 'input' => [ 'shape' => 'CancelChangeSetRequest', ], 'output' => [ 'shape' => 'CancelChangeSetResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteResourcePolicy' => [ 'name' => 'DeleteResourcePolicy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/DeleteResourcePolicy', ], 'input' => [ 'shape' => 'DeleteResourcePolicyRequest', ], 'output' => [ 'shape' => 'DeleteResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DescribeChangeSet' => [ 'name' => 'DescribeChangeSet', 'http' => [ 'method' => 'GET', 'requestUri' => '/DescribeChangeSet', ], 'input' => [ 'shape' => 'DescribeChangeSetRequest', ], 'output' => [ 'shape' => 'DescribeChangeSetResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DescribeEntity' => [ 'name' => 'DescribeEntity', 'http' => [ 'method' => 'GET', 'requestUri' => '/DescribeEntity', ], 'input' => [ 'shape' => 'DescribeEntityRequest', ], 'output' => [ 'shape' => 'DescribeEntityResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotSupportedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetResourcePolicy' => [ 'name' => 'GetResourcePolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/GetResourcePolicy', ], 'input' => [ 'shape' => 'GetResourcePolicyRequest', ], 'output' => [ 'shape' => 'GetResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListChangeSets' => [ 'name' => 'ListChangeSets', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListChangeSets', ], 'input' => [ 'shape' => 'ListChangeSetsRequest', ], 'output' => [ 'shape' => 'ListChangeSetsResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListEntities' => [ 'name' => 'ListEntities', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListEntities', ], 'input' => [ 'shape' => 'ListEntitiesRequest', ], 'output' => [ 'shape' => 'ListEntitiesResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListTagsForResource', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'PutResourcePolicy' => [ 'name' => 'PutResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/PutResourcePolicy', ], 'input' => [ 'shape' => 'PutResourcePolicyRequest', ], 'output' => [ 'shape' => 'PutResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'StartChangeSet' => [ 'name' => 'StartChangeSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/StartChangeSet', ], 'input' => [ 'shape' => 'StartChangeSetRequest', ], 'output' => [ 'shape' => 'StartChangeSetResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/TagResource', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/UntagResource', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], ], ], 'shapes' => [ 'ARN' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^[a-zA-Z0-9:*/-]+$', ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessageContent', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, 'synthetic' => true, ], 'AmiProductEntityIdFilter' => [ 'type' => 'structure', 'members' => [ 'ValueList' => [ 'shape' => 'AmiProductEntityIdFilterValueList', ], ], ], 'AmiProductEntityIdFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AmiProductEntityIdString', ], 'max' => 10, 'min' => 1, ], 'AmiProductEntityIdString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9][.a-zA-Z0-9/-]+[a-zA-Z0-9]$', ], 'AmiProductFilters' => [ 'type' => 'structure', 'members' => [ 'EntityId' => [ 'shape' => 'AmiProductEntityIdFilter', ], 'LastModifiedDate' => [ 'shape' => 'AmiProductLastModifiedDateFilter', ], 'ProductTitle' => [ 'shape' => 'AmiProductTitleFilter', ], 'Visibility' => [ 'shape' => 'AmiProductVisibilityFilter', ], ], ], 'AmiProductLastModifiedDateFilter' => [ 'type' => 'structure', 'members' => [ 'DateRange' => [ 'shape' => 'AmiProductLastModifiedDateFilterDateRange', ], ], ], 'AmiProductLastModifiedDateFilterDateRange' => [ 'type' => 'structure', 'members' => [ 'AfterValue' => [ 'shape' => 'DateTimeISO8601', ], 'BeforeValue' => [ 'shape' => 'DateTimeISO8601', ], ], ], 'AmiProductSort' => [ 'type' => 'structure', 'members' => [ 'SortBy' => [ 'shape' => 'AmiProductSortBy', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'AmiProductSortBy' => [ 'type' => 'string', 'enum' => [ 'EntityId', 'LastModifiedDate', 'ProductTitle', 'Visibility', ], ], 'AmiProductSummary' => [ 'type' => 'structure', 'members' => [ 'ProductTitle' => [ 'shape' => 'AmiProductTitleString', ], 'Visibility' => [ 'shape' => 'AmiProductVisibilityString', ], ], ], 'AmiProductTitleFilter' => [ 'type' => 'structure', 'members' => [ 'ValueList' => [ 'shape' => 'AmiProductTitleFilterValueList', ], 'WildCardValue' => [ 'shape' => 'AmiProductTitleString', ], ], ], 'AmiProductTitleFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AmiProductTitleString', ], 'max' => 10, 'min' => 1, ], 'AmiProductTitleString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^(.)+$', ], 'AmiProductVisibilityFilter' => [ 'type' => 'structure', 'members' => [ 'ValueList' => [ 'shape' => 'AmiProductVisibilityFilterValueList', ], ], ], 'AmiProductVisibilityFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AmiProductVisibilityString', ], 'max' => 10, 'min' => 1, ], 'AmiProductVisibilityString' => [ 'type' => 'string', 'enum' => [ 'Limited', 'Public', 'Restricted', 'Draft', ], ], 'BatchDescribeEntitiesRequest' => [ 'type' => 'structure', 'required' => [ 'EntityRequestList', ], 'members' => [ 'EntityRequestList' => [ 'shape' => 'EntityRequestList', ], ], ], 'BatchDescribeEntitiesResponse' => [ 'type' => 'structure', 'members' => [ 'EntityDetails' => [ 'shape' => 'EntityDetails', ], 'Errors' => [ 'shape' => 'Errors', ], ], ], 'BatchDescribeErrorCodeString' => [ 'type' => 'string', 'max' => 72, 'min' => 1, 'pattern' => '^[a-zA-Z_]+$', ], 'BatchDescribeErrorDetail' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => 'BatchDescribeErrorCodeString', ], 'ErrorMessage' => [ 'shape' => 'BatchDescribeErrorMessageContent', ], ], ], 'BatchDescribeErrorMessageContent' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^(.)+$', ], 'CancelChangeSetRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', 'ChangeSetId', ], 'members' => [ 'Catalog' => [ 'shape' => 'Catalog', 'location' => 'querystring', 'locationName' => 'catalog', ], 'ChangeSetId' => [ 'shape' => 'ResourceId', 'location' => 'querystring', 'locationName' => 'changeSetId', ], ], ], 'CancelChangeSetResponse' => [ 'type' => 'structure', 'members' => [ 'ChangeSetId' => [ 'shape' => 'ResourceId', ], 'ChangeSetArn' => [ 'shape' => 'ARN', ], ], ], 'Catalog' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z]+$', ], 'Change' => [ 'type' => 'structure', 'required' => [ 'ChangeType', 'Entity', ], 'members' => [ 'ChangeType' => [ 'shape' => 'ChangeType', ], 'Entity' => [ 'shape' => 'Entity', ], 'EntityTags' => [ 'shape' => 'TagList', ], 'Details' => [ 'shape' => 'Json', ], 'DetailsDocument' => [ 'shape' => 'JsonDocumentType', ], 'ChangeName' => [ 'shape' => 'ChangeName', ], ], ], 'ChangeName' => [ 'type' => 'string', 'max' => 72, 'min' => 1, 'pattern' => '^[a-zA-Z]$', ], 'ChangeSetDescription' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChangeSummary', ], ], 'ChangeSetName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[\\w\\s+=.:@-]+$', ], 'ChangeSetSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChangeSetSummaryListItem', ], ], 'ChangeSetSummaryListItem' => [ 'type' => 'structure', 'members' => [ 'ChangeSetId' => [ 'shape' => 'ResourceId', ], 'ChangeSetArn' => [ 'shape' => 'ARN', ], 'ChangeSetName' => [ 'shape' => 'ChangeSetName', ], 'StartTime' => [ 'shape' => 'DateTimeISO8601', ], 'EndTime' => [ 'shape' => 'DateTimeISO8601', ], 'Status' => [ 'shape' => 'ChangeStatus', ], 'EntityIdList' => [ 'shape' => 'ResourceIdList', ], 'FailureCode' => [ 'shape' => 'FailureCode', ], ], ], 'ChangeStatus' => [ 'type' => 'string', 'enum' => [ 'PREPARING', 'APPLYING', 'SUCCEEDED', 'CANCELLED', 'FAILED', ], ], 'ChangeSummary' => [ 'type' => 'structure', 'members' => [ 'ChangeType' => [ 'shape' => 'ChangeType', ], 'Entity' => [ 'shape' => 'Entity', ], 'Details' => [ 'shape' => 'Json', ], 'DetailsDocument' => [ 'shape' => 'JsonDocumentType', ], 'ErrorDetailList' => [ 'shape' => 'ErrorDetailList', ], 'ChangeName' => [ 'shape' => 'ChangeName', ], ], ], 'ChangeType' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[A-Z][\\w]*$', ], 'ClientRequestToken' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[!-~]+$', ], 'ContainerProductEntityIdFilter' => [ 'type' => 'structure', 'members' => [ 'ValueList' => [ 'shape' => 'ContainerProductEntityIdFilterValueList', ], ], ], 'ContainerProductEntityIdFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContainerProductEntityIdString', ], 'max' => 10, 'min' => 1, ], 'ContainerProductEntityIdString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9][.a-zA-Z0-9/-]+[a-zA-Z0-9]$', ], 'ContainerProductFilters' => [ 'type' => 'structure', 'members' => [ 'EntityId' => [ 'shape' => 'ContainerProductEntityIdFilter', ], 'LastModifiedDate' => [ 'shape' => 'ContainerProductLastModifiedDateFilter', ], 'ProductTitle' => [ 'shape' => 'ContainerProductTitleFilter', ], 'Visibility' => [ 'shape' => 'ContainerProductVisibilityFilter', ], ], ], 'ContainerProductLastModifiedDateFilter' => [ 'type' => 'structure', 'members' => [ 'DateRange' => [ 'shape' => 'ContainerProductLastModifiedDateFilterDateRange', ], ], ], 'ContainerProductLastModifiedDateFilterDateRange' => [ 'type' => 'structure', 'members' => [ 'AfterValue' => [ 'shape' => 'DateTimeISO8601', ], 'BeforeValue' => [ 'shape' => 'DateTimeISO8601', ], ], ], 'ContainerProductSort' => [ 'type' => 'structure', 'members' => [ 'SortBy' => [ 'shape' => 'ContainerProductSortBy', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'ContainerProductSortBy' => [ 'type' => 'string', 'enum' => [ 'EntityId', 'LastModifiedDate', 'ProductTitle', 'Visibility', ], ], 'ContainerProductSummary' => [ 'type' => 'structure', 'members' => [ 'ProductTitle' => [ 'shape' => 'ContainerProductTitleString', ], 'Visibility' => [ 'shape' => 'ContainerProductVisibilityString', ], ], ], 'ContainerProductTitleFilter' => [ 'type' => 'structure', 'members' => [ 'ValueList' => [ 'shape' => 'ContainerProductTitleFilterValueList', ], 'WildCardValue' => [ 'shape' => 'ContainerProductTitleString', ], ], ], 'ContainerProductTitleFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContainerProductTitleString', ], 'max' => 10, 'min' => 1, ], 'ContainerProductTitleString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^(.)+$', ], 'ContainerProductVisibilityFilter' => [ 'type' => 'structure', 'members' => [ 'ValueList' => [ 'shape' => 'ContainerProductVisibilityFilterValueList', ], ], ], 'ContainerProductVisibilityFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContainerProductVisibilityString', ], 'max' => 10, 'min' => 1, ], 'ContainerProductVisibilityString' => [ 'type' => 'string', 'enum' => [ 'Limited', 'Public', 'Restricted', 'Draft', ], ], 'DataProductEntityIdFilter' => [ 'type' => 'structure', 'members' => [ 'ValueList' => [ 'shape' => 'DataProductEntityIdFilterValueList', ], ], ], 'DataProductEntityIdFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataProductEntityIdString', ], 'max' => 10, 'min' => 1, ], 'DataProductEntityIdString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9][.a-zA-Z0-9/-]+[a-zA-Z0-9]$', ], 'DataProductFilters' => [ 'type' => 'structure', 'members' => [ 'EntityId' => [ 'shape' => 'DataProductEntityIdFilter', ], 'ProductTitle' => [ 'shape' => 'DataProductTitleFilter', ], 'Visibility' => [ 'shape' => 'DataProductVisibilityFilter', ], 'LastModifiedDate' => [ 'shape' => 'DataProductLastModifiedDateFilter', ], ], ], 'DataProductLastModifiedDateFilter' => [ 'type' => 'structure', 'members' => [ 'DateRange' => [ 'shape' => 'DataProductLastModifiedDateFilterDateRange', ], ], ], 'DataProductLastModifiedDateFilterDateRange' => [ 'type' => 'structure', 'members' => [ 'AfterValue' => [ 'shape' => 'DateTimeISO8601', ], 'BeforeValue' => [ 'shape' => 'DateTimeISO8601', ], ], ], 'DataProductSort' => [ 'type' => 'structure', 'members' => [ 'SortBy' => [ 'shape' => 'DataProductSortBy', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'DataProductSortBy' => [ 'type' => 'string', 'enum' => [ 'EntityId', 'ProductTitle', 'Visibility', 'LastModifiedDate', ], ], 'DataProductSummary' => [ 'type' => 'structure', 'members' => [ 'ProductTitle' => [ 'shape' => 'DataProductTitleString', ], 'Visibility' => [ 'shape' => 'DataProductVisibilityString', ], ], ], 'DataProductTitleFilter' => [ 'type' => 'structure', 'members' => [ 'ValueList' => [ 'shape' => 'DataProductTitleFilterValueList', ], 'WildCardValue' => [ 'shape' => 'DataProductTitleString', ], ], ], 'DataProductTitleFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataProductTitleString', ], 'max' => 10, 'min' => 1, ], 'DataProductTitleString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^(.)+$', ], 'DataProductVisibilityFilter' => [ 'type' => 'structure', 'members' => [ 'ValueList' => [ 'shape' => 'DataProductVisibilityFilterValueList', ], ], ], 'DataProductVisibilityFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataProductVisibilityString', ], 'max' => 10, 'min' => 1, ], 'DataProductVisibilityString' => [ 'type' => 'string', 'enum' => [ 'Limited', 'Public', 'Restricted', 'Unavailable', 'Draft', ], ], 'DateTimeISO8601' => [ 'type' => 'string', 'max' => 20, 'min' => 20, 'pattern' => '^([\\d]{4})\\-(1[0-2]|0[1-9])\\-(3[01]|0[1-9]|[12][\\d])T(2[0-3]|[01][\\d]):([0-5][\\d]):([0-5][\\d])Z$', ], 'DeleteResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceARN', 'location' => 'querystring', 'locationName' => 'resourceArn', ], ], ], 'DeleteResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'DescribeChangeSetRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', 'ChangeSetId', ], 'members' => [ 'Catalog' => [ 'shape' => 'Catalog', 'location' => 'querystring', 'locationName' => 'catalog', ], 'ChangeSetId' => [ 'shape' => 'ResourceId', 'location' => 'querystring', 'locationName' => 'changeSetId', ], ], ], 'DescribeChangeSetResponse' => [ 'type' => 'structure', 'members' => [ 'ChangeSetId' => [ 'shape' => 'ResourceId', ], 'ChangeSetArn' => [ 'shape' => 'ARN', ], 'ChangeSetName' => [ 'shape' => 'ChangeSetName', ], 'Intent' => [ 'shape' => 'Intent', ], 'StartTime' => [ 'shape' => 'DateTimeISO8601', ], 'EndTime' => [ 'shape' => 'DateTimeISO8601', ], 'Status' => [ 'shape' => 'ChangeStatus', ], 'FailureCode' => [ 'shape' => 'FailureCode', ], 'FailureDescription' => [ 'shape' => 'ExceptionMessageContent', ], 'ChangeSet' => [ 'shape' => 'ChangeSetDescription', ], ], ], 'DescribeEntityRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', 'EntityId', ], 'members' => [ 'Catalog' => [ 'shape' => 'Catalog', 'location' => 'querystring', 'locationName' => 'catalog', ], 'EntityId' => [ 'shape' => 'ResourceId', 'location' => 'querystring', 'locationName' => 'entityId', ], ], ], 'DescribeEntityResponse' => [ 'type' => 'structure', 'members' => [ 'EntityType' => [ 'shape' => 'EntityType', ], 'EntityIdentifier' => [ 'shape' => 'Identifier', ], 'EntityArn' => [ 'shape' => 'ARN', ], 'LastModifiedDate' => [ 'shape' => 'DateTimeISO8601', ], 'Details' => [ 'shape' => 'Json', ], 'DetailsDocument' => [ 'shape' => 'JsonDocumentType', ], ], ], 'Entity' => [ 'type' => 'structure', 'required' => [ 'Type', ], 'members' => [ 'Type' => [ 'shape' => 'EntityType', ], 'Identifier' => [ 'shape' => 'Identifier', ], ], ], 'EntityDetail' => [ 'type' => 'structure', 'members' => [ 'EntityType' => [ 'shape' => 'EntityType', ], 'EntityArn' => [ 'shape' => 'ARN', ], 'EntityIdentifier' => [ 'shape' => 'Identifier', ], 'LastModifiedDate' => [ 'shape' => 'DateTimeISO8601', ], 'DetailsDocument' => [ 'shape' => 'JsonDocumentType', ], ], ], 'EntityDetails' => [ 'type' => 'map', 'key' => [ 'shape' => 'EntityId', ], 'value' => [ 'shape' => 'EntityDetail', ], ], 'EntityId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9][.a-zA-Z0-9/-]+[a-zA-Z0-9]$', ], 'EntityNameString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^\\\\S+[\\\\S\\\\s]*', ], 'EntityRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', 'EntityId', ], 'members' => [ 'Catalog' => [ 'shape' => 'Catalog', ], 'EntityId' => [ 'shape' => 'EntityId', ], ], ], 'EntityRequestList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EntityRequest', ], 'max' => 20, 'min' => 1, ], 'EntitySummary' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'EntityNameString', ], 'EntityType' => [ 'shape' => 'EntityType', ], 'EntityId' => [ 'shape' => 'ResourceId', ], 'EntityArn' => [ 'shape' => 'ARN', ], 'LastModifiedDate' => [ 'shape' => 'DateTimeISO8601', ], 'Visibility' => [ 'shape' => 'VisibilityValue', ], 'AmiProductSummary' => [ 'shape' => 'AmiProductSummary', ], 'ContainerProductSummary' => [ 'shape' => 'ContainerProductSummary', ], 'DataProductSummary' => [ 'shape' => 'DataProductSummary', ], 'SaaSProductSummary' => [ 'shape' => 'SaaSProductSummary', ], 'OfferSummary' => [ 'shape' => 'OfferSummary', ], 'ResaleAuthorizationSummary' => [ 'shape' => 'ResaleAuthorizationSummary', ], ], ], 'EntitySummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EntitySummary', ], ], 'EntityType' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z]+$', ], 'EntityTypeFilters' => [ 'type' => 'structure', 'members' => [ 'DataProductFilters' => [ 'shape' => 'DataProductFilters', ], 'SaaSProductFilters' => [ 'shape' => 'SaaSProductFilters', ], 'AmiProductFilters' => [ 'shape' => 'AmiProductFilters', ], 'OfferFilters' => [ 'shape' => 'OfferFilters', ], 'ContainerProductFilters' => [ 'shape' => 'ContainerProductFilters', ], 'ResaleAuthorizationFilters' => [ 'shape' => 'ResaleAuthorizationFilters', ], ], 'union' => true, ], 'EntityTypeSort' => [ 'type' => 'structure', 'members' => [ 'DataProductSort' => [ 'shape' => 'DataProductSort', ], 'SaaSProductSort' => [ 'shape' => 'SaaSProductSort', ], 'AmiProductSort' => [ 'shape' => 'AmiProductSort', ], 'OfferSort' => [ 'shape' => 'OfferSort', ], 'ContainerProductSort' => [ 'shape' => 'ContainerProductSort', ], 'ResaleAuthorizationSort' => [ 'shape' => 'ResaleAuthorizationSort', ], ], 'union' => true, ], 'ErrorCodeString' => [ 'type' => 'string', 'max' => 72, 'min' => 1, 'pattern' => '^[a-zA-Z_]+$', ], 'ErrorDetail' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => 'ErrorCodeString', ], 'ErrorMessage' => [ 'shape' => 'ExceptionMessageContent', ], ], ], 'ErrorDetailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ErrorDetail', ], ], 'Errors' => [ 'type' => 'map', 'key' => [ 'shape' => 'EntityId', ], 'value' => [ 'shape' => 'BatchDescribeErrorDetail', ], ], 'ExceptionMessageContent' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^(.)+$', ], 'FailureCode' => [ 'type' => 'string', 'enum' => [ 'CLIENT_ERROR', 'SERVER_FAULT', ], ], 'Filter' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'FilterName', ], 'ValueList' => [ 'shape' => 'ValueList', ], ], ], 'FilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], 'max' => 8, 'min' => 1, ], 'FilterName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z]+$', ], 'FilterValueContent' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^(.)+$', ], 'GetResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceARN', 'location' => 'querystring', 'locationName' => 'resourceArn', ], ], ], 'GetResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'Policy' => [ 'shape' => 'ResourcePolicyJson', ], ], ], 'Identifier' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[\\w\\-@]+$', ], 'Intent' => [ 'type' => 'string', 'enum' => [ 'VALIDATE', 'APPLY', ], ], 'InternalServiceException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessageContent', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'synthetic' => true, ], 'Json' => [ 'type' => 'string', 'max' => 16384, 'min' => 2, 'pattern' => '^[\\s]*\\{[\\s\\S]*\\}[\\s]*$', ], 'JsonDocumentType' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'ListChangeSetsMaxResultInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 20, 'min' => 1, ], 'ListChangeSetsRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', ], 'members' => [ 'Catalog' => [ 'shape' => 'Catalog', ], 'FilterList' => [ 'shape' => 'FilterList', ], 'Sort' => [ 'shape' => 'Sort', ], 'MaxResults' => [ 'shape' => 'ListChangeSetsMaxResultInteger', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListChangeSetsResponse' => [ 'type' => 'structure', 'members' => [ 'ChangeSetSummaryList' => [ 'shape' => 'ChangeSetSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListEntitiesMaxResultInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 1, ], 'ListEntitiesRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', 'EntityType', ], 'members' => [ 'Catalog' => [ 'shape' => 'Catalog', ], 'EntityType' => [ 'shape' => 'EntityType', ], 'FilterList' => [ 'shape' => 'FilterList', ], 'Sort' => [ 'shape' => 'Sort', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'ListEntitiesMaxResultInteger', ], 'OwnershipType' => [ 'shape' => 'OwnershipType', ], 'EntityTypeFilters' => [ 'shape' => 'EntityTypeFilters', ], 'EntityTypeSort' => [ 'shape' => 'EntityTypeSort', ], ], ], 'ListEntitiesResponse' => [ 'type' => 'structure', 'members' => [ 'EntitySummaryList' => [ 'shape' => 'EntitySummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceARN', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceARN', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'NextToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^[\\w+=.:@\\-\\/]$', ], 'OfferAvailabilityEndDateFilter' => [ 'type' => 'structure', 'members' => [ 'DateRange' => [ 'shape' => 'OfferAvailabilityEndDateFilterDateRange', ], ], ], 'OfferAvailabilityEndDateFilterDateRange' => [ 'type' => 'structure', 'members' => [ 'AfterValue' => [ 'shape' => 'DateTimeISO8601', ], 'BeforeValue' => [ 'shape' => 'DateTimeISO8601', ], ], ], 'OfferBuyerAccountsFilter' => [ 'type' => 'structure', 'members' => [ 'WildCardValue' => [ 'shape' => 'OfferBuyerAccountsFilterWildcard', ], ], ], 'OfferBuyerAccountsFilterWildcard' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^(.)+$', ], 'OfferBuyerAccountsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OfferBuyerAccountsString', ], 'max' => 26, 'min' => 0, ], 'OfferBuyerAccountsString' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '^\\d{12}$', ], 'OfferEntityIdFilter' => [ 'type' => 'structure', 'members' => [ 'ValueList' => [ 'shape' => 'OfferEntityIdFilterValueList', ], ], ], 'OfferEntityIdFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OfferEntityIdString', ], 'max' => 10, 'min' => 1, ], 'OfferEntityIdString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9][.a-zA-Z0-9/-]+[a-zA-Z0-9]$', ], 'OfferFilters' => [ 'type' => 'structure', 'members' => [ 'EntityId' => [ 'shape' => 'OfferEntityIdFilter', ], 'Name' => [ 'shape' => 'OfferNameFilter', ], 'ProductId' => [ 'shape' => 'OfferProductIdFilter', ], 'ResaleAuthorizationId' => [ 'shape' => 'OfferResaleAuthorizationIdFilter', ], 'ReleaseDate' => [ 'shape' => 'OfferReleaseDateFilter', ], 'AvailabilityEndDate' => [ 'shape' => 'OfferAvailabilityEndDateFilter', ], 'BuyerAccounts' => [ 'shape' => 'OfferBuyerAccountsFilter', ], 'State' => [ 'shape' => 'OfferStateFilter', ], 'Targeting' => [ 'shape' => 'OfferTargetingFilter', ], 'LastModifiedDate' => [ 'shape' => 'OfferLastModifiedDateFilter', ], ], ], 'OfferLastModifiedDateFilter' => [ 'type' => 'structure', 'members' => [ 'DateRange' => [ 'shape' => 'OfferLastModifiedDateFilterDateRange', ], ], ], 'OfferLastModifiedDateFilterDateRange' => [ 'type' => 'structure', 'members' => [ 'AfterValue' => [ 'shape' => 'DateTimeISO8601', ], 'BeforeValue' => [ 'shape' => 'DateTimeISO8601', ], ], ], 'OfferNameFilter' => [ 'type' => 'structure', 'members' => [ 'ValueList' => [ 'shape' => 'OfferNameFilterValueList', ], 'WildCardValue' => [ 'shape' => 'OfferNameString', ], ], ], 'OfferNameFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OfferNameString', ], 'max' => 10, 'min' => 1, ], 'OfferNameString' => [ 'type' => 'string', 'max' => 150, 'min' => 1, 'pattern' => '^(.)+$', ], 'OfferProductIdFilter' => [ 'type' => 'structure', 'members' => [ 'ValueList' => [ 'shape' => 'OfferProductIdFilterValueList', ], ], ], 'OfferProductIdFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OfferProductIdString', ], 'max' => 10, 'min' => 1, ], 'OfferProductIdString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^(.)+$', ], 'OfferReleaseDateFilter' => [ 'type' => 'structure', 'members' => [ 'DateRange' => [ 'shape' => 'OfferReleaseDateFilterDateRange', ], ], ], 'OfferReleaseDateFilterDateRange' => [ 'type' => 'structure', 'members' => [ 'AfterValue' => [ 'shape' => 'DateTimeISO8601', ], 'BeforeValue' => [ 'shape' => 'DateTimeISO8601', ], ], ], 'OfferResaleAuthorizationIdFilter' => [ 'type' => 'structure', 'members' => [ 'ValueList' => [ 'shape' => 'OfferResaleAuthorizationIdFilterValueList', ], ], ], 'OfferResaleAuthorizationIdFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OfferResaleAuthorizationIdString', ], 'max' => 10, 'min' => 1, ], 'OfferResaleAuthorizationIdString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9][.a-zA-Z0-9/-]+[a-zA-Z0-9]$', ], 'OfferSort' => [ 'type' => 'structure', 'members' => [ 'SortBy' => [ 'shape' => 'OfferSortBy', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'OfferSortBy' => [ 'type' => 'string', 'enum' => [ 'EntityId', 'Name', 'ProductId', 'ResaleAuthorizationId', 'ReleaseDate', 'AvailabilityEndDate', 'BuyerAccounts', 'State', 'Targeting', 'LastModifiedDate', ], ], 'OfferStateFilter' => [ 'type' => 'structure', 'members' => [ 'ValueList' => [ 'shape' => 'OfferStateFilterValueList', ], ], ], 'OfferStateFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OfferStateString', ], 'max' => 2, 'min' => 1, ], 'OfferStateString' => [ 'type' => 'string', 'enum' => [ 'Draft', 'Released', ], ], 'OfferSummary' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'OfferNameString', ], 'ProductId' => [ 'shape' => 'OfferProductIdString', ], 'ResaleAuthorizationId' => [ 'shape' => 'OfferResaleAuthorizationIdString', ], 'ReleaseDate' => [ 'shape' => 'DateTimeISO8601', ], 'AvailabilityEndDate' => [ 'shape' => 'DateTimeISO8601', ], 'BuyerAccounts' => [ 'shape' => 'OfferBuyerAccountsList', ], 'State' => [ 'shape' => 'OfferStateString', ], 'Targeting' => [ 'shape' => 'OfferTargetingList', ], ], ], 'OfferTargetingFilter' => [ 'type' => 'structure', 'members' => [ 'ValueList' => [ 'shape' => 'OfferTargetingFilterValueList', ], ], ], 'OfferTargetingFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OfferTargetingString', ], 'max' => 4, 'min' => 1, ], 'OfferTargetingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OfferTargetingString', ], 'max' => 4, 'min' => 0, ], 'OfferTargetingString' => [ 'type' => 'string', 'enum' => [ 'BuyerAccounts', 'ParticipatingPrograms', 'CountryCodes', 'None', ], ], 'OwnershipType' => [ 'type' => 'string', 'enum' => [ 'SELF', 'SHARED', ], ], 'PutResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Policy', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceARN', ], 'Policy' => [ 'shape' => 'ResourcePolicyJson', ], ], ], 'PutResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'RequestedChangeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Change', ], 'max' => 20, 'min' => 1, ], 'ResaleAuthorizationAvailabilityEndDateFilter' => [ 'type' => 'structure', 'members' => [ 'DateRange' => [ 'shape' => 'ResaleAuthorizationAvailabilityEndDateFilterDateRange', ], 'ValueList' => [ 'shape' => 'ResaleAuthorizationAvailabilityEndDateFilterValueList', ], ], ], 'ResaleAuthorizationAvailabilityEndDateFilterDateRange' => [ 'type' => 'structure', 'members' => [ 'AfterValue' => [ 'shape' => 'DateTimeISO8601', ], 'BeforeValue' => [ 'shape' => 'DateTimeISO8601', ], ], ], 'ResaleAuthorizationAvailabilityEndDateFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DateTimeISO8601', ], 'max' => 10, 'min' => 1, ], 'ResaleAuthorizationCreatedDateFilter' => [ 'type' => 'structure', 'members' => [ 'DateRange' => [ 'shape' => 'ResaleAuthorizationCreatedDateFilterDateRange', ], 'ValueList' => [ 'shape' => 'ResaleAuthorizationCreatedDateFilterValueList', ], ], ], 'ResaleAuthorizationCreatedDateFilterDateRange' => [ 'type' => 'structure', 'members' => [ 'AfterValue' => [ 'shape' => 'DateTimeISO8601', ], 'BeforeValue' => [ 'shape' => 'DateTimeISO8601', ], ], ], 'ResaleAuthorizationCreatedDateFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DateTimeISO8601', ], 'max' => 10, 'min' => 1, ], 'ResaleAuthorizationEntityIdFilter' => [ 'type' => 'structure', 'members' => [ 'ValueList' => [ 'shape' => 'ResaleAuthorizationEntityIdFilterValueList', ], ], ], 'ResaleAuthorizationEntityIdFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResaleAuthorizationEntityIdString', ], 'max' => 10, 'min' => 1, ], 'ResaleAuthorizationEntityIdString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9][.a-zA-Z0-9/-]+[a-zA-Z0-9]$', ], 'ResaleAuthorizationFilters' => [ 'type' => 'structure', 'members' => [ 'EntityId' => [ 'shape' => 'ResaleAuthorizationEntityIdFilter', ], 'Name' => [ 'shape' => 'ResaleAuthorizationNameFilter', ], 'ProductId' => [ 'shape' => 'ResaleAuthorizationProductIdFilter', ], 'CreatedDate' => [ 'shape' => 'ResaleAuthorizationCreatedDateFilter', ], 'AvailabilityEndDate' => [ 'shape' => 'ResaleAuthorizationAvailabilityEndDateFilter', ], 'ManufacturerAccountId' => [ 'shape' => 'ResaleAuthorizationManufacturerAccountIdFilter', ], 'ProductName' => [ 'shape' => 'ResaleAuthorizationProductNameFilter', ], 'ManufacturerLegalName' => [ 'shape' => 'ResaleAuthorizationManufacturerLegalNameFilter', ], 'ResellerAccountID' => [ 'shape' => 'ResaleAuthorizationResellerAccountIDFilter', ], 'ResellerLegalName' => [ 'shape' => 'ResaleAuthorizationResellerLegalNameFilter', ], 'Status' => [ 'shape' => 'ResaleAuthorizationStatusFilter', ], 'OfferExtendedStatus' => [ 'shape' => 'ResaleAuthorizationOfferExtendedStatusFilter', ], 'LastModifiedDate' => [ 'shape' => 'ResaleAuthorizationLastModifiedDateFilter', ], ], ], 'ResaleAuthorizationLastModifiedDateFilter' => [ 'type' => 'structure', 'members' => [ 'DateRange' => [ 'shape' => 'ResaleAuthorizationLastModifiedDateFilterDateRange', ], ], ], 'ResaleAuthorizationLastModifiedDateFilterDateRange' => [ 'type' => 'structure', 'members' => [ 'AfterValue' => [ 'shape' => 'DateTimeISO8601', ], 'BeforeValue' => [ 'shape' => 'DateTimeISO8601', ], ], ], 'ResaleAuthorizationManufacturerAccountIdFilter' => [ 'type' => 'structure', 'members' => [ 'ValueList' => [ 'shape' => 'ResaleAuthorizationManufacturerAccountIdFilterValueList', ], 'WildCardValue' => [ 'shape' => 'ResaleAuthorizationManufacturerAccountIdFilterWildcard', ], ], ], 'ResaleAuthorizationManufacturerAccountIdFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResaleAuthorizationManufacturerAccountIdString', ], 'max' => 10, 'min' => 1, ], 'ResaleAuthorizationManufacturerAccountIdFilterWildcard' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '^\\d{12}$', ], 'ResaleAuthorizationManufacturerAccountIdString' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '^\\d{12}$', ], 'ResaleAuthorizationManufacturerLegalNameFilter' => [ 'type' => 'structure', 'members' => [ 'ValueList' => [ 'shape' => 'ResaleAuthorizationManufacturerLegalNameFilterValueList', ], 'WildCardValue' => [ 'shape' => 'ResaleAuthorizationManufacturerLegalNameFilterWildcard', ], ], ], 'ResaleAuthorizationManufacturerLegalNameFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResaleAuthorizationManufacturerLegalNameString', ], 'max' => 10, 'min' => 1, ], 'ResaleAuthorizationManufacturerLegalNameFilterWildcard' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^(.)+$', ], 'ResaleAuthorizationManufacturerLegalNameString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^(.)+$', ], 'ResaleAuthorizationNameFilter' => [ 'type' => 'structure', 'members' => [ 'ValueList' => [ 'shape' => 'ResaleAuthorizationNameFilterValueList', ], 'WildCardValue' => [ 'shape' => 'ResaleAuthorizationNameFilterWildcard', ], ], ], 'ResaleAuthorizationNameFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResaleAuthorizationNameString', ], 'max' => 10, 'min' => 1, ], 'ResaleAuthorizationNameFilterWildcard' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^(.)+$', ], 'ResaleAuthorizationNameString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^(.)+$', ], 'ResaleAuthorizationOfferExtendedStatusFilter' => [ 'type' => 'structure', 'members' => [ 'ValueList' => [ 'shape' => 'ResaleAuthorizationOfferExtendedStatusFilterValueList', ], ], ], 'ResaleAuthorizationOfferExtendedStatusFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResaleAuthorizationOfferExtendedStatusString', ], 'max' => 10, 'min' => 1, ], 'ResaleAuthorizationOfferExtendedStatusString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^(.)+$', ], 'ResaleAuthorizationProductIdFilter' => [ 'type' => 'structure', 'members' => [ 'ValueList' => [ 'shape' => 'ResaleAuthorizationProductIdFilterValueList', ], 'WildCardValue' => [ 'shape' => 'ResaleAuthorizationProductIdFilterWildcard', ], ], ], 'ResaleAuthorizationProductIdFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResaleAuthorizationProductIdString', ], 'max' => 10, 'min' => 1, ], 'ResaleAuthorizationProductIdFilterWildcard' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^(.)+$', ], 'ResaleAuthorizationProductIdString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^(.)+$', ], 'ResaleAuthorizationProductNameFilter' => [ 'type' => 'structure', 'members' => [ 'ValueList' => [ 'shape' => 'ResaleAuthorizationProductNameFilterValueList', ], 'WildCardValue' => [ 'shape' => 'ResaleAuthorizationProductNameFilterWildcard', ], ], ], 'ResaleAuthorizationProductNameFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResaleAuthorizationProductNameString', ], 'max' => 10, 'min' => 1, ], 'ResaleAuthorizationProductNameFilterWildcard' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^(.)+$', ], 'ResaleAuthorizationProductNameString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^(.)+$', ], 'ResaleAuthorizationResellerAccountIDFilter' => [ 'type' => 'structure', 'members' => [ 'ValueList' => [ 'shape' => 'ResaleAuthorizationResellerAccountIDFilterValueList', ], 'WildCardValue' => [ 'shape' => 'ResaleAuthorizationResellerAccountIDFilterWildcard', ], ], ], 'ResaleAuthorizationResellerAccountIDFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResaleAuthorizationResellerAccountIDString', ], 'max' => 10, 'min' => 1, ], 'ResaleAuthorizationResellerAccountIDFilterWildcard' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '^\\d{12}$', ], 'ResaleAuthorizationResellerAccountIDString' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '^\\d{12}$', ], 'ResaleAuthorizationResellerLegalNameFilter' => [ 'type' => 'structure', 'members' => [ 'ValueList' => [ 'shape' => 'ResaleAuthorizationResellerLegalNameFilterValueList', ], 'WildCardValue' => [ 'shape' => 'ResaleAuthorizationResellerLegalNameFilterWildcard', ], ], ], 'ResaleAuthorizationResellerLegalNameFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResaleAuthorizationResellerLegalNameString', ], 'max' => 10, 'min' => 1, ], 'ResaleAuthorizationResellerLegalNameFilterWildcard' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^(.)+$', ], 'ResaleAuthorizationResellerLegalNameString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^(.)+$', ], 'ResaleAuthorizationSort' => [ 'type' => 'structure', 'members' => [ 'SortBy' => [ 'shape' => 'ResaleAuthorizationSortBy', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'ResaleAuthorizationSortBy' => [ 'type' => 'string', 'enum' => [ 'EntityId', 'Name', 'ProductId', 'ProductName', 'ManufacturerAccountId', 'ManufacturerLegalName', 'ResellerAccountID', 'ResellerLegalName', 'Status', 'OfferExtendedStatus', 'CreatedDate', 'AvailabilityEndDate', 'LastModifiedDate', ], ], 'ResaleAuthorizationStatusFilter' => [ 'type' => 'structure', 'members' => [ 'ValueList' => [ 'shape' => 'ResaleAuthorizationStatusFilterValueList', ], ], ], 'ResaleAuthorizationStatusFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResaleAuthorizationStatusString', ], 'max' => 10, 'min' => 1, ], 'ResaleAuthorizationStatusString' => [ 'type' => 'string', 'enum' => [ 'Draft', 'Active', 'Restricted', ], ], 'ResaleAuthorizationSummary' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'ResaleAuthorizationNameString', ], 'ProductId' => [ 'shape' => 'ResaleAuthorizationProductIdString', ], 'ProductName' => [ 'shape' => 'ResaleAuthorizationProductNameString', ], 'ManufacturerAccountId' => [ 'shape' => 'ResaleAuthorizationManufacturerAccountIdString', ], 'ManufacturerLegalName' => [ 'shape' => 'ResaleAuthorizationManufacturerLegalNameString', ], 'ResellerAccountID' => [ 'shape' => 'ResaleAuthorizationResellerAccountIDString', ], 'ResellerLegalName' => [ 'shape' => 'ResaleAuthorizationResellerLegalNameString', ], 'Status' => [ 'shape' => 'ResaleAuthorizationStatusString', ], 'OfferExtendedStatus' => [ 'shape' => 'ResaleAuthorizationOfferExtendedStatusString', ], 'CreatedDate' => [ 'shape' => 'DateTimeISO8601', ], 'AvailabilityEndDate' => [ 'shape' => 'DateTimeISO8601', ], ], ], 'ResourceARN' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^arn:[\\w+=/,.@-]+:aws-marketplace:[\\w+=/,.@-]*:[0-9]+:[\\w+=,.@-]+(/[\\w+=,.@-]+)*$', ], 'ResourceId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[\\w\\-]+$', ], 'ResourceIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceId', ], ], 'ResourceInUseException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessageContent', ], ], 'error' => [ 'httpStatusCode' => 423, ], 'exception' => true, 'synthetic' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessageContent', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, 'synthetic' => true, ], 'ResourceNotSupportedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessageContent', ], ], 'error' => [ 'httpStatusCode' => 415, ], 'exception' => true, 'synthetic' => true, ], 'ResourcePolicyJson' => [ 'type' => 'string', 'max' => 10240, 'min' => 1, 'pattern' => '^[\\u0009\\u000A\\u000D\\u0020-\\u00FF]+$', ], 'SaaSProductEntityIdFilter' => [ 'type' => 'structure', 'members' => [ 'ValueList' => [ 'shape' => 'SaaSProductEntityIdFilterValueList', ], ], ], 'SaaSProductEntityIdFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SaaSProductEntityIdString', ], 'max' => 10, 'min' => 1, ], 'SaaSProductEntityIdString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9][.a-zA-Z0-9/-]+[a-zA-Z0-9]$', ], 'SaaSProductFilters' => [ 'type' => 'structure', 'members' => [ 'EntityId' => [ 'shape' => 'SaaSProductEntityIdFilter', ], 'ProductTitle' => [ 'shape' => 'SaaSProductTitleFilter', ], 'Visibility' => [ 'shape' => 'SaaSProductVisibilityFilter', ], 'LastModifiedDate' => [ 'shape' => 'SaaSProductLastModifiedDateFilter', ], ], ], 'SaaSProductLastModifiedDateFilter' => [ 'type' => 'structure', 'members' => [ 'DateRange' => [ 'shape' => 'SaaSProductLastModifiedDateFilterDateRange', ], ], ], 'SaaSProductLastModifiedDateFilterDateRange' => [ 'type' => 'structure', 'members' => [ 'AfterValue' => [ 'shape' => 'DateTimeISO8601', ], 'BeforeValue' => [ 'shape' => 'DateTimeISO8601', ], ], ], 'SaaSProductSort' => [ 'type' => 'structure', 'members' => [ 'SortBy' => [ 'shape' => 'SaaSProductSortBy', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'SaaSProductSortBy' => [ 'type' => 'string', 'enum' => [ 'EntityId', 'ProductTitle', 'Visibility', 'LastModifiedDate', ], ], 'SaaSProductSummary' => [ 'type' => 'structure', 'members' => [ 'ProductTitle' => [ 'shape' => 'SaaSProductTitleString', ], 'Visibility' => [ 'shape' => 'SaaSProductVisibilityString', ], ], ], 'SaaSProductTitleFilter' => [ 'type' => 'structure', 'members' => [ 'ValueList' => [ 'shape' => 'SaaSProductTitleFilterValueList', ], 'WildCardValue' => [ 'shape' => 'SaaSProductTitleString', ], ], ], 'SaaSProductTitleFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SaaSProductTitleString', ], 'max' => 10, 'min' => 1, ], 'SaaSProductTitleString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^(.)+$', ], 'SaaSProductVisibilityFilter' => [ 'type' => 'structure', 'members' => [ 'ValueList' => [ 'shape' => 'SaaSProductVisibilityFilterValueList', ], ], ], 'SaaSProductVisibilityFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SaaSProductVisibilityString', ], 'max' => 10, 'min' => 1, ], 'SaaSProductVisibilityString' => [ 'type' => 'string', 'enum' => [ 'Limited', 'Public', 'Restricted', 'Draft', ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessageContent', ], ], 'error' => [ 'httpStatusCode' => 402, ], 'exception' => true, 'synthetic' => true, ], 'Sort' => [ 'type' => 'structure', 'members' => [ 'SortBy' => [ 'shape' => 'SortBy', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'SortBy' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z]+$', ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'ASCENDING', 'DESCENDING', ], ], 'StartChangeSetRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', 'ChangeSet', ], 'members' => [ 'Catalog' => [ 'shape' => 'Catalog', ], 'ChangeSet' => [ 'shape' => 'RequestedChangeList', ], 'ChangeSetName' => [ 'shape' => 'ChangeSetName', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'ChangeSetTags' => [ 'shape' => 'TagList', ], 'Intent' => [ 'shape' => 'Intent', ], ], ], 'StartChangeSetResponse' => [ 'type' => 'structure', 'members' => [ 'ChangeSetId' => [ 'shape' => 'ResourceId', ], 'ChangeSetArn' => [ 'shape' => 'ARN', ], ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 1, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceARN', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessageContent', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, 'synthetic' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceARN', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessageContent', ], ], 'error' => [ 'httpStatusCode' => 422, ], 'exception' => true, 'synthetic' => true, ], 'ValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterValueContent', ], 'max' => 10, 'min' => 1, ], 'VisibilityValue' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z]+$', ], ],];
