<?php
// This file was auto-generated from sdk-root/src/data/clouddirectory/2017-01-11/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2017-01-11', 'endpointPrefix' => 'clouddirectory', 'protocol' => 'rest-json', 'serviceFullName' => 'Amazon CloudDirectory', 'serviceId' => 'CloudDirectory', 'signatureVersion' => 'v4', 'signingName' => 'clouddirectory', 'uid' => 'clouddirectory-2017-01-11', ], 'operations' => [ 'AddFacetToObject' => [ 'name' => 'AddFacetToObject', 'http' => [ 'method' => 'PUT', 'requestUri' => '/amazonclouddirectory/2017-01-11/object/facets', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AddFacetToObjectRequest', ], 'output' => [ 'shape' => 'AddFacetToObjectResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'DirectoryNotEnabledException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'FacetValidationException', ], ], ], 'ApplySchema' => [ 'name' => 'ApplySchema', 'http' => [ 'method' => 'PUT', 'requestUri' => '/amazonclouddirectory/2017-01-11/schema/apply', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ApplySchemaRequest', ], 'output' => [ 'shape' => 'ApplySchemaResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'SchemaAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidAttachmentException', ], ], ], 'AttachObject' => [ 'name' => 'AttachObject', 'http' => [ 'method' => 'PUT', 'requestUri' => '/amazonclouddirectory/2017-01-11/object/attach', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AttachObjectRequest', ], 'output' => [ 'shape' => 'AttachObjectResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'DirectoryNotEnabledException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LinkNameAlreadyInUseException', ], [ 'shape' => 'InvalidAttachmentException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'FacetValidationException', ], ], ], 'AttachPolicy' => [ 'name' => 'AttachPolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/amazonclouddirectory/2017-01-11/policy/attach', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AttachPolicyRequest', ], 'output' => [ 'shape' => 'AttachPolicyResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'DirectoryNotEnabledException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'NotPolicyException', ], ], ], 'AttachToIndex' => [ 'name' => 'AttachToIndex', 'http' => [ 'method' => 'PUT', 'requestUri' => '/amazonclouddirectory/2017-01-11/index/attach', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AttachToIndexRequest', ], 'output' => [ 'shape' => 'AttachToIndexResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'DirectoryNotEnabledException', ], [ 'shape' => 'InvalidAttachmentException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LinkNameAlreadyInUseException', ], [ 'shape' => 'IndexedAttributeMissingException', ], [ 'shape' => 'NotIndexException', ], ], ], 'AttachTypedLink' => [ 'name' => 'AttachTypedLink', 'http' => [ 'method' => 'PUT', 'requestUri' => '/amazonclouddirectory/2017-01-11/typedlink/attach', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AttachTypedLinkRequest', ], 'output' => [ 'shape' => 'AttachTypedLinkResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'DirectoryNotEnabledException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidAttachmentException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'FacetValidationException', ], ], ], 'BatchRead' => [ 'name' => 'BatchRead', 'http' => [ 'method' => 'POST', 'requestUri' => '/amazonclouddirectory/2017-01-11/batchread', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchReadRequest', ], 'output' => [ 'shape' => 'BatchReadResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'DirectoryNotEnabledException', ], ], ], 'BatchWrite' => [ 'name' => 'BatchWrite', 'http' => [ 'method' => 'PUT', 'requestUri' => '/amazonclouddirectory/2017-01-11/batchwrite', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchWriteRequest', ], 'output' => [ 'shape' => 'BatchWriteResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'DirectoryNotEnabledException', ], [ 'shape' => 'BatchWriteException', ], ], ], 'CreateDirectory' => [ 'name' => 'CreateDirectory', 'http' => [ 'method' => 'PUT', 'requestUri' => '/amazonclouddirectory/2017-01-11/directory/create', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateDirectoryRequest', ], 'output' => [ 'shape' => 'CreateDirectoryResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'DirectoryAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'CreateFacet' => [ 'name' => 'CreateFacet', 'http' => [ 'method' => 'PUT', 'requestUri' => '/amazonclouddirectory/2017-01-11/facet/create', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateFacetRequest', ], 'output' => [ 'shape' => 'CreateFacetResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'FacetAlreadyExistsException', ], [ 'shape' => 'InvalidRuleException', ], [ 'shape' => 'FacetValidationException', ], ], ], 'CreateIndex' => [ 'name' => 'CreateIndex', 'http' => [ 'method' => 'PUT', 'requestUri' => '/amazonclouddirectory/2017-01-11/index', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateIndexRequest', ], 'output' => [ 'shape' => 'CreateIndexResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'DirectoryNotEnabledException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'FacetValidationException', ], [ 'shape' => 'LinkNameAlreadyInUseException', ], [ 'shape' => 'UnsupportedIndexTypeException', ], ], ], 'CreateObject' => [ 'name' => 'CreateObject', 'http' => [ 'method' => 'PUT', 'requestUri' => '/amazonclouddirectory/2017-01-11/object', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateObjectRequest', ], 'output' => [ 'shape' => 'CreateObjectResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'DirectoryNotEnabledException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'FacetValidationException', ], [ 'shape' => 'LinkNameAlreadyInUseException', ], [ 'shape' => 'UnsupportedIndexTypeException', ], ], ], 'CreateSchema' => [ 'name' => 'CreateSchema', 'http' => [ 'method' => 'PUT', 'requestUri' => '/amazonclouddirectory/2017-01-11/schema/create', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateSchemaRequest', ], 'output' => [ 'shape' => 'CreateSchemaResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'SchemaAlreadyExistsException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateTypedLinkFacet' => [ 'name' => 'CreateTypedLinkFacet', 'http' => [ 'method' => 'PUT', 'requestUri' => '/amazonclouddirectory/2017-01-11/typedlink/facet/create', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateTypedLinkFacetRequest', ], 'output' => [ 'shape' => 'CreateTypedLinkFacetResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'FacetAlreadyExistsException', ], [ 'shape' => 'InvalidRuleException', ], [ 'shape' => 'FacetValidationException', ], ], ], 'DeleteDirectory' => [ 'name' => 'DeleteDirectory', 'http' => [ 'method' => 'PUT', 'requestUri' => '/amazonclouddirectory/2017-01-11/directory', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteDirectoryRequest', ], 'output' => [ 'shape' => 'DeleteDirectoryResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'DirectoryNotDisabledException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'DirectoryDeletedException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'InvalidArnException', ], ], ], 'DeleteFacet' => [ 'name' => 'DeleteFacet', 'http' => [ 'method' => 'PUT', 'requestUri' => '/amazonclouddirectory/2017-01-11/facet/delete', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteFacetRequest', ], 'output' => [ 'shape' => 'DeleteFacetResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'FacetNotFoundException', ], [ 'shape' => 'FacetInUseException', ], ], ], 'DeleteObject' => [ 'name' => 'DeleteObject', 'http' => [ 'method' => 'PUT', 'requestUri' => '/amazonclouddirectory/2017-01-11/object/delete', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteObjectRequest', ], 'output' => [ 'shape' => 'DeleteObjectResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'DirectoryNotEnabledException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ObjectNotDetachedException', ], ], ], 'DeleteSchema' => [ 'name' => 'DeleteSchema', 'http' => [ 'method' => 'PUT', 'requestUri' => '/amazonclouddirectory/2017-01-11/schema', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteSchemaRequest', ], 'output' => [ 'shape' => 'DeleteSchemaResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'StillContainsLinksException', ], ], ], 'DeleteTypedLinkFacet' => [ 'name' => 'DeleteTypedLinkFacet', 'http' => [ 'method' => 'PUT', 'requestUri' => '/amazonclouddirectory/2017-01-11/typedlink/facet/delete', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteTypedLinkFacetRequest', ], 'output' => [ 'shape' => 'DeleteTypedLinkFacetResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'FacetNotFoundException', ], ], ], 'DetachFromIndex' => [ 'name' => 'DetachFromIndex', 'http' => [ 'method' => 'PUT', 'requestUri' => '/amazonclouddirectory/2017-01-11/index/detach', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DetachFromIndexRequest', ], 'output' => [ 'shape' => 'DetachFromIndexResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'DirectoryNotEnabledException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ObjectAlreadyDetachedException', ], [ 'shape' => 'NotIndexException', ], ], ], 'DetachObject' => [ 'name' => 'DetachObject', 'http' => [ 'method' => 'PUT', 'requestUri' => '/amazonclouddirectory/2017-01-11/object/detach', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DetachObjectRequest', ], 'output' => [ 'shape' => 'DetachObjectResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'DirectoryNotEnabledException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'NotNodeException', ], ], ], 'DetachPolicy' => [ 'name' => 'DetachPolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/amazonclouddirectory/2017-01-11/policy/detach', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DetachPolicyRequest', ], 'output' => [ 'shape' => 'DetachPolicyResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'DirectoryNotEnabledException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'NotPolicyException', ], ], ], 'DetachTypedLink' => [ 'name' => 'DetachTypedLink', 'http' => [ 'method' => 'PUT', 'requestUri' => '/amazonclouddirectory/2017-01-11/typedlink/detach', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DetachTypedLinkRequest', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'DirectoryNotEnabledException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'FacetValidationException', ], ], ], 'DisableDirectory' => [ 'name' => 'DisableDirectory', 'http' => [ 'method' => 'PUT', 'requestUri' => '/amazonclouddirectory/2017-01-11/directory/disable', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisableDirectoryRequest', ], 'output' => [ 'shape' => 'DisableDirectoryResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'DirectoryDeletedException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'InvalidArnException', ], ], ], 'EnableDirectory' => [ 'name' => 'EnableDirectory', 'http' => [ 'method' => 'PUT', 'requestUri' => '/amazonclouddirectory/2017-01-11/directory/enable', 'responseCode' => 200, ], 'input' => [ 'shape' => 'EnableDirectoryRequest', ], 'output' => [ 'shape' => 'EnableDirectoryResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'DirectoryDeletedException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'InvalidArnException', ], ], ], 'GetAppliedSchemaVersion' => [ 'name' => 'GetAppliedSchemaVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/amazonclouddirectory/2017-01-11/schema/getappliedschema', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAppliedSchemaVersionRequest', ], 'output' => [ 'shape' => 'GetAppliedSchemaVersionResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetDirectory' => [ 'name' => 'GetDirectory', 'http' => [ 'method' => 'POST', 'requestUri' => '/amazonclouddirectory/2017-01-11/directory/get', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDirectoryRequest', ], 'output' => [ 'shape' => 'GetDirectoryResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetFacet' => [ 'name' => 'GetFacet', 'http' => [ 'method' => 'POST', 'requestUri' => '/amazonclouddirectory/2017-01-11/facet', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFacetRequest', ], 'output' => [ 'shape' => 'GetFacetResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'FacetNotFoundException', ], ], ], 'GetLinkAttributes' => [ 'name' => 'GetLinkAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/amazonclouddirectory/2017-01-11/typedlink/attributes/get', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetLinkAttributesRequest', ], 'output' => [ 'shape' => 'GetLinkAttributesResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'DirectoryNotEnabledException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'FacetValidationException', ], ], ], 'GetObjectAttributes' => [ 'name' => 'GetObjectAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/amazonclouddirectory/2017-01-11/object/attributes/get', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetObjectAttributesRequest', ], 'output' => [ 'shape' => 'GetObjectAttributesResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'DirectoryNotEnabledException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'FacetValidationException', ], ], ], 'GetObjectInformation' => [ 'name' => 'GetObjectInformation', 'http' => [ 'method' => 'POST', 'requestUri' => '/amazonclouddirectory/2017-01-11/object/information', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetObjectInformationRequest', ], 'output' => [ 'shape' => 'GetObjectInformationResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'DirectoryNotEnabledException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetSchemaAsJson' => [ 'name' => 'GetSchemaAsJson', 'http' => [ 'method' => 'POST', 'requestUri' => '/amazonclouddirectory/2017-01-11/schema/json', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSchemaAsJsonRequest', ], 'output' => [ 'shape' => 'GetSchemaAsJsonResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetTypedLinkFacetInformation' => [ 'name' => 'GetTypedLinkFacetInformation', 'http' => [ 'method' => 'POST', 'requestUri' => '/amazonclouddirectory/2017-01-11/typedlink/facet/get', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTypedLinkFacetInformationRequest', ], 'output' => [ 'shape' => 'GetTypedLinkFacetInformationResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'FacetNotFoundException', ], ], ], 'ListAppliedSchemaArns' => [ 'name' => 'ListAppliedSchemaArns', 'http' => [ 'method' => 'POST', 'requestUri' => '/amazonclouddirectory/2017-01-11/schema/applied', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAppliedSchemaArnsRequest', ], 'output' => [ 'shape' => 'ListAppliedSchemaArnsResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'ListAttachedIndices' => [ 'name' => 'ListAttachedIndices', 'http' => [ 'method' => 'POST', 'requestUri' => '/amazonclouddirectory/2017-01-11/object/indices', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAttachedIndicesRequest', ], 'output' => [ 'shape' => 'ListAttachedIndicesResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'DirectoryNotEnabledException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListDevelopmentSchemaArns' => [ 'name' => 'ListDevelopmentSchemaArns', 'http' => [ 'method' => 'POST', 'requestUri' => '/amazonclouddirectory/2017-01-11/schema/development', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDevelopmentSchemaArnsRequest', ], 'output' => [ 'shape' => 'ListDevelopmentSchemaArnsResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'ListDirectories' => [ 'name' => 'ListDirectories', 'http' => [ 'method' => 'POST', 'requestUri' => '/amazonclouddirectory/2017-01-11/directory/list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDirectoriesRequest', ], 'output' => [ 'shape' => 'ListDirectoriesResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'ListFacetAttributes' => [ 'name' => 'ListFacetAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/amazonclouddirectory/2017-01-11/facet/attributes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFacetAttributesRequest', ], 'output' => [ 'shape' => 'ListFacetAttributesResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'FacetNotFoundException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'ListFacetNames' => [ 'name' => 'ListFacetNames', 'http' => [ 'method' => 'POST', 'requestUri' => '/amazonclouddirectory/2017-01-11/facet/list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFacetNamesRequest', ], 'output' => [ 'shape' => 'ListFacetNamesResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'ListIncomingTypedLinks' => [ 'name' => 'ListIncomingTypedLinks', 'http' => [ 'method' => 'POST', 'requestUri' => '/amazonclouddirectory/2017-01-11/typedlink/incoming', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListIncomingTypedLinksRequest', ], 'output' => [ 'shape' => 'ListIncomingTypedLinksResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'DirectoryNotEnabledException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'FacetValidationException', ], ], ], 'ListIndex' => [ 'name' => 'ListIndex', 'http' => [ 'method' => 'POST', 'requestUri' => '/amazonclouddirectory/2017-01-11/index/targets', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListIndexRequest', ], 'output' => [ 'shape' => 'ListIndexResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'FacetValidationException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'DirectoryNotEnabledException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'NotIndexException', ], ], ], 'ListManagedSchemaArns' => [ 'name' => 'ListManagedSchemaArns', 'http' => [ 'method' => 'POST', 'requestUri' => '/amazonclouddirectory/2017-01-11/schema/managed', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListManagedSchemaArnsRequest', ], 'output' => [ 'shape' => 'ListManagedSchemaArnsResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'ListObjectAttributes' => [ 'name' => 'ListObjectAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/amazonclouddirectory/2017-01-11/object/attributes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListObjectAttributesRequest', ], 'output' => [ 'shape' => 'ListObjectAttributesResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'DirectoryNotEnabledException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'FacetValidationException', ], ], ], 'ListObjectChildren' => [ 'name' => 'ListObjectChildren', 'http' => [ 'method' => 'POST', 'requestUri' => '/amazonclouddirectory/2017-01-11/object/children', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListObjectChildrenRequest', ], 'output' => [ 'shape' => 'ListObjectChildrenResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'DirectoryNotEnabledException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'NotNodeException', ], ], ], 'ListObjectParentPaths' => [ 'name' => 'ListObjectParentPaths', 'http' => [ 'method' => 'POST', 'requestUri' => '/amazonclouddirectory/2017-01-11/object/parentpaths', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListObjectParentPathsRequest', ], 'output' => [ 'shape' => 'ListObjectParentPathsResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'DirectoryNotEnabledException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListObjectParents' => [ 'name' => 'ListObjectParents', 'http' => [ 'method' => 'POST', 'requestUri' => '/amazonclouddirectory/2017-01-11/object/parent', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListObjectParentsRequest', ], 'output' => [ 'shape' => 'ListObjectParentsResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'DirectoryNotEnabledException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'CannotListParentOfRootException', ], ], ], 'ListObjectPolicies' => [ 'name' => 'ListObjectPolicies', 'http' => [ 'method' => 'POST', 'requestUri' => '/amazonclouddirectory/2017-01-11/object/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListObjectPoliciesRequest', ], 'output' => [ 'shape' => 'ListObjectPoliciesResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'DirectoryNotEnabledException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'ListOutgoingTypedLinks' => [ 'name' => 'ListOutgoingTypedLinks', 'http' => [ 'method' => 'POST', 'requestUri' => '/amazonclouddirectory/2017-01-11/typedlink/outgoing', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListOutgoingTypedLinksRequest', ], 'output' => [ 'shape' => 'ListOutgoingTypedLinksResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'DirectoryNotEnabledException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'FacetValidationException', ], ], ], 'ListPolicyAttachments' => [ 'name' => 'ListPolicyAttachments', 'http' => [ 'method' => 'POST', 'requestUri' => '/amazonclouddirectory/2017-01-11/policy/attachment', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPolicyAttachmentsRequest', ], 'output' => [ 'shape' => 'ListPolicyAttachmentsResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'DirectoryNotEnabledException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'NotPolicyException', ], ], ], 'ListPublishedSchemaArns' => [ 'name' => 'ListPublishedSchemaArns', 'http' => [ 'method' => 'POST', 'requestUri' => '/amazonclouddirectory/2017-01-11/schema/published', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPublishedSchemaArnsRequest', ], 'output' => [ 'shape' => 'ListPublishedSchemaArnsResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/amazonclouddirectory/2017-01-11/tags', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidTaggingRequestException', ], ], ], 'ListTypedLinkFacetAttributes' => [ 'name' => 'ListTypedLinkFacetAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/amazonclouddirectory/2017-01-11/typedlink/facet/attributes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTypedLinkFacetAttributesRequest', ], 'output' => [ 'shape' => 'ListTypedLinkFacetAttributesResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'FacetNotFoundException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'ListTypedLinkFacetNames' => [ 'name' => 'ListTypedLinkFacetNames', 'http' => [ 'method' => 'POST', 'requestUri' => '/amazonclouddirectory/2017-01-11/typedlink/facet/list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTypedLinkFacetNamesRequest', ], 'output' => [ 'shape' => 'ListTypedLinkFacetNamesResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'LookupPolicy' => [ 'name' => 'LookupPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/amazonclouddirectory/2017-01-11/policy/lookup', 'responseCode' => 200, ], 'input' => [ 'shape' => 'LookupPolicyRequest', ], 'output' => [ 'shape' => 'LookupPolicyResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'DirectoryNotEnabledException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'PublishSchema' => [ 'name' => 'PublishSchema', 'http' => [ 'method' => 'PUT', 'requestUri' => '/amazonclouddirectory/2017-01-11/schema/publish', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PublishSchemaRequest', ], 'output' => [ 'shape' => 'PublishSchemaResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'SchemaAlreadyPublishedException', ], ], ], 'PutSchemaFromJson' => [ 'name' => 'PutSchemaFromJson', 'http' => [ 'method' => 'PUT', 'requestUri' => '/amazonclouddirectory/2017-01-11/schema/json', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutSchemaFromJsonRequest', ], 'output' => [ 'shape' => 'PutSchemaFromJsonResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidSchemaDocException', ], [ 'shape' => 'InvalidRuleException', ], ], ], 'RemoveFacetFromObject' => [ 'name' => 'RemoveFacetFromObject', 'http' => [ 'method' => 'PUT', 'requestUri' => '/amazonclouddirectory/2017-01-11/object/facets/delete', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RemoveFacetFromObjectRequest', ], 'output' => [ 'shape' => 'RemoveFacetFromObjectResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'DirectoryNotEnabledException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'FacetValidationException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'PUT', 'requestUri' => '/amazonclouddirectory/2017-01-11/tags/add', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidTaggingRequestException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'PUT', 'requestUri' => '/amazonclouddirectory/2017-01-11/tags/remove', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidTaggingRequestException', ], ], ], 'UpdateFacet' => [ 'name' => 'UpdateFacet', 'http' => [ 'method' => 'PUT', 'requestUri' => '/amazonclouddirectory/2017-01-11/facet', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateFacetRequest', ], 'output' => [ 'shape' => 'UpdateFacetResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidFacetUpdateException', ], [ 'shape' => 'FacetValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'FacetNotFoundException', ], [ 'shape' => 'InvalidRuleException', ], ], ], 'UpdateLinkAttributes' => [ 'name' => 'UpdateLinkAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/amazonclouddirectory/2017-01-11/typedlink/attributes/update', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateLinkAttributesRequest', ], 'output' => [ 'shape' => 'UpdateLinkAttributesResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'DirectoryNotEnabledException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'FacetValidationException', ], ], ], 'UpdateObjectAttributes' => [ 'name' => 'UpdateObjectAttributes', 'http' => [ 'method' => 'PUT', 'requestUri' => '/amazonclouddirectory/2017-01-11/object/update', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateObjectAttributesRequest', ], 'output' => [ 'shape' => 'UpdateObjectAttributesResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'DirectoryNotEnabledException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LinkNameAlreadyInUseException', ], [ 'shape' => 'FacetValidationException', ], ], ], 'UpdateSchema' => [ 'name' => 'UpdateSchema', 'http' => [ 'method' => 'PUT', 'requestUri' => '/amazonclouddirectory/2017-01-11/schema/update', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateSchemaRequest', ], 'output' => [ 'shape' => 'UpdateSchemaResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateTypedLinkFacet' => [ 'name' => 'UpdateTypedLinkFacet', 'http' => [ 'method' => 'PUT', 'requestUri' => '/amazonclouddirectory/2017-01-11/typedlink/facet', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateTypedLinkFacetRequest', ], 'output' => [ 'shape' => 'UpdateTypedLinkFacetResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'FacetValidationException', ], [ 'shape' => 'InvalidFacetUpdateException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'FacetNotFoundException', ], [ 'shape' => 'InvalidRuleException', ], ], ], 'UpgradeAppliedSchema' => [ 'name' => 'UpgradeAppliedSchema', 'http' => [ 'method' => 'PUT', 'requestUri' => '/amazonclouddirectory/2017-01-11/schema/upgradeapplied', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpgradeAppliedSchemaRequest', ], 'output' => [ 'shape' => 'UpgradeAppliedSchemaResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'IncompatibleSchemaException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidAttachmentException', ], [ 'shape' => 'SchemaAlreadyExistsException', ], ], ], 'UpgradePublishedSchema' => [ 'name' => 'UpgradePublishedSchema', 'http' => [ 'method' => 'PUT', 'requestUri' => '/amazonclouddirectory/2017-01-11/schema/upgradepublished', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpgradePublishedSchemaRequest', ], 'output' => [ 'shape' => 'UpgradePublishedSchemaResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'RetryableConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'IncompatibleSchemaException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidAttachmentException', ], [ 'shape' => 'LimitExceededException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'AddFacetToObjectRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', 'SchemaFacet', 'ObjectReference', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'SchemaFacet' => [ 'shape' => 'SchemaFacet', ], 'ObjectAttributeList' => [ 'shape' => 'AttributeKeyAndValueList', ], 'ObjectReference' => [ 'shape' => 'ObjectReference', ], ], ], 'AddFacetToObjectResponse' => [ 'type' => 'structure', 'members' => [], ], 'ApplySchemaRequest' => [ 'type' => 'structure', 'required' => [ 'PublishedSchemaArn', 'DirectoryArn', ], 'members' => [ 'PublishedSchemaArn' => [ 'shape' => 'Arn', ], 'DirectoryArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], ], ], 'ApplySchemaResponse' => [ 'type' => 'structure', 'members' => [ 'AppliedSchemaArn' => [ 'shape' => 'Arn', ], 'DirectoryArn' => [ 'shape' => 'Arn', ], ], ], 'Arn' => [ 'type' => 'string', ], 'Arns' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], ], 'AttachObjectRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', 'ParentReference', 'ChildReference', 'LinkName', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'ParentReference' => [ 'shape' => 'ObjectReference', ], 'ChildReference' => [ 'shape' => 'ObjectReference', ], 'LinkName' => [ 'shape' => 'LinkName', ], ], ], 'AttachObjectResponse' => [ 'type' => 'structure', 'members' => [ 'AttachedObjectIdentifier' => [ 'shape' => 'ObjectIdentifier', ], ], ], 'AttachPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', 'PolicyReference', 'ObjectReference', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'PolicyReference' => [ 'shape' => 'ObjectReference', ], 'ObjectReference' => [ 'shape' => 'ObjectReference', ], ], ], 'AttachPolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'AttachToIndexRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', 'IndexReference', 'TargetReference', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'IndexReference' => [ 'shape' => 'ObjectReference', ], 'TargetReference' => [ 'shape' => 'ObjectReference', ], ], ], 'AttachToIndexResponse' => [ 'type' => 'structure', 'members' => [ 'AttachedObjectIdentifier' => [ 'shape' => 'ObjectIdentifier', ], ], ], 'AttachTypedLinkRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', 'SourceObjectReference', 'TargetObjectReference', 'TypedLinkFacet', 'Attributes', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'SourceObjectReference' => [ 'shape' => 'ObjectReference', ], 'TargetObjectReference' => [ 'shape' => 'ObjectReference', ], 'TypedLinkFacet' => [ 'shape' => 'TypedLinkSchemaAndFacetName', ], 'Attributes' => [ 'shape' => 'AttributeNameAndValueList', ], ], ], 'AttachTypedLinkResponse' => [ 'type' => 'structure', 'members' => [ 'TypedLinkSpecifier' => [ 'shape' => 'TypedLinkSpecifier', ], ], ], 'AttributeKey' => [ 'type' => 'structure', 'required' => [ 'SchemaArn', 'FacetName', 'Name', ], 'members' => [ 'SchemaArn' => [ 'shape' => 'Arn', ], 'FacetName' => [ 'shape' => 'FacetName', ], 'Name' => [ 'shape' => 'AttributeName', ], ], ], 'AttributeKeyAndValue' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'AttributeKey', ], 'Value' => [ 'shape' => 'TypedAttributeValue', ], ], ], 'AttributeKeyAndValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttributeKeyAndValue', ], ], 'AttributeKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttributeKey', ], ], 'AttributeName' => [ 'type' => 'string', 'max' => 230, 'min' => 1, 'pattern' => '^[a-zA-Z0-9._:-]*$', ], 'AttributeNameAndValue' => [ 'type' => 'structure', 'required' => [ 'AttributeName', 'Value', ], 'members' => [ 'AttributeName' => [ 'shape' => 'AttributeName', ], 'Value' => [ 'shape' => 'TypedAttributeValue', ], ], ], 'AttributeNameAndValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttributeNameAndValue', ], ], 'AttributeNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttributeName', ], ], 'BatchAddFacetToObject' => [ 'type' => 'structure', 'required' => [ 'SchemaFacet', 'ObjectAttributeList', 'ObjectReference', ], 'members' => [ 'SchemaFacet' => [ 'shape' => 'SchemaFacet', ], 'ObjectAttributeList' => [ 'shape' => 'AttributeKeyAndValueList', ], 'ObjectReference' => [ 'shape' => 'ObjectReference', ], ], ], 'BatchAddFacetToObjectResponse' => [ 'type' => 'structure', 'members' => [], ], 'BatchAttachObject' => [ 'type' => 'structure', 'required' => [ 'ParentReference', 'ChildReference', 'LinkName', ], 'members' => [ 'ParentReference' => [ 'shape' => 'ObjectReference', ], 'ChildReference' => [ 'shape' => 'ObjectReference', ], 'LinkName' => [ 'shape' => 'LinkName', ], ], ], 'BatchAttachObjectResponse' => [ 'type' => 'structure', 'members' => [ 'attachedObjectIdentifier' => [ 'shape' => 'ObjectIdentifier', ], ], ], 'BatchAttachPolicy' => [ 'type' => 'structure', 'required' => [ 'PolicyReference', 'ObjectReference', ], 'members' => [ 'PolicyReference' => [ 'shape' => 'ObjectReference', ], 'ObjectReference' => [ 'shape' => 'ObjectReference', ], ], ], 'BatchAttachPolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'BatchAttachToIndex' => [ 'type' => 'structure', 'required' => [ 'IndexReference', 'TargetReference', ], 'members' => [ 'IndexReference' => [ 'shape' => 'ObjectReference', ], 'TargetReference' => [ 'shape' => 'ObjectReference', ], ], ], 'BatchAttachToIndexResponse' => [ 'type' => 'structure', 'members' => [ 'AttachedObjectIdentifier' => [ 'shape' => 'ObjectIdentifier', ], ], ], 'BatchAttachTypedLink' => [ 'type' => 'structure', 'required' => [ 'SourceObjectReference', 'TargetObjectReference', 'TypedLinkFacet', 'Attributes', ], 'members' => [ 'SourceObjectReference' => [ 'shape' => 'ObjectReference', ], 'TargetObjectReference' => [ 'shape' => 'ObjectReference', ], 'TypedLinkFacet' => [ 'shape' => 'TypedLinkSchemaAndFacetName', ], 'Attributes' => [ 'shape' => 'AttributeNameAndValueList', ], ], ], 'BatchAttachTypedLinkResponse' => [ 'type' => 'structure', 'members' => [ 'TypedLinkSpecifier' => [ 'shape' => 'TypedLinkSpecifier', ], ], ], 'BatchCreateIndex' => [ 'type' => 'structure', 'required' => [ 'OrderedIndexedAttributeList', 'IsUnique', ], 'members' => [ 'OrderedIndexedAttributeList' => [ 'shape' => 'AttributeKeyList', ], 'IsUnique' => [ 'shape' => 'Bool', ], 'ParentReference' => [ 'shape' => 'ObjectReference', ], 'LinkName' => [ 'shape' => 'LinkName', ], 'BatchReferenceName' => [ 'shape' => 'BatchReferenceName', ], ], ], 'BatchCreateIndexResponse' => [ 'type' => 'structure', 'members' => [ 'ObjectIdentifier' => [ 'shape' => 'ObjectIdentifier', ], ], ], 'BatchCreateObject' => [ 'type' => 'structure', 'required' => [ 'SchemaFacet', 'ObjectAttributeList', ], 'members' => [ 'SchemaFacet' => [ 'shape' => 'SchemaFacetList', ], 'ObjectAttributeList' => [ 'shape' => 'AttributeKeyAndValueList', ], 'ParentReference' => [ 'shape' => 'ObjectReference', ], 'LinkName' => [ 'shape' => 'LinkName', ], 'BatchReferenceName' => [ 'shape' => 'BatchReferenceName', ], ], ], 'BatchCreateObjectResponse' => [ 'type' => 'structure', 'members' => [ 'ObjectIdentifier' => [ 'shape' => 'ObjectIdentifier', ], ], ], 'BatchDeleteObject' => [ 'type' => 'structure', 'required' => [ 'ObjectReference', ], 'members' => [ 'ObjectReference' => [ 'shape' => 'ObjectReference', ], ], ], 'BatchDeleteObjectResponse' => [ 'type' => 'structure', 'members' => [], ], 'BatchDetachFromIndex' => [ 'type' => 'structure', 'required' => [ 'IndexReference', 'TargetReference', ], 'members' => [ 'IndexReference' => [ 'shape' => 'ObjectReference', ], 'TargetReference' => [ 'shape' => 'ObjectReference', ], ], ], 'BatchDetachFromIndexResponse' => [ 'type' => 'structure', 'members' => [ 'DetachedObjectIdentifier' => [ 'shape' => 'ObjectIdentifier', ], ], ], 'BatchDetachObject' => [ 'type' => 'structure', 'required' => [ 'ParentReference', 'LinkName', ], 'members' => [ 'ParentReference' => [ 'shape' => 'ObjectReference', ], 'LinkName' => [ 'shape' => 'LinkName', ], 'BatchReferenceName' => [ 'shape' => 'BatchReferenceName', ], ], ], 'BatchDetachObjectResponse' => [ 'type' => 'structure', 'members' => [ 'detachedObjectIdentifier' => [ 'shape' => 'ObjectIdentifier', ], ], ], 'BatchDetachPolicy' => [ 'type' => 'structure', 'required' => [ 'PolicyReference', 'ObjectReference', ], 'members' => [ 'PolicyReference' => [ 'shape' => 'ObjectReference', ], 'ObjectReference' => [ 'shape' => 'ObjectReference', ], ], ], 'BatchDetachPolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'BatchDetachTypedLink' => [ 'type' => 'structure', 'required' => [ 'TypedLinkSpecifier', ], 'members' => [ 'TypedLinkSpecifier' => [ 'shape' => 'TypedLinkSpecifier', ], ], ], 'BatchDetachTypedLinkResponse' => [ 'type' => 'structure', 'members' => [], ], 'BatchGetLinkAttributes' => [ 'type' => 'structure', 'required' => [ 'TypedLinkSpecifier', 'AttributeNames', ], 'members' => [ 'TypedLinkSpecifier' => [ 'shape' => 'TypedLinkSpecifier', ], 'AttributeNames' => [ 'shape' => 'AttributeNameList', ], ], ], 'BatchGetLinkAttributesResponse' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'AttributeKeyAndValueList', ], ], ], 'BatchGetObjectAttributes' => [ 'type' => 'structure', 'required' => [ 'ObjectReference', 'SchemaFacet', 'AttributeNames', ], 'members' => [ 'ObjectReference' => [ 'shape' => 'ObjectReference', ], 'SchemaFacet' => [ 'shape' => 'SchemaFacet', ], 'AttributeNames' => [ 'shape' => 'AttributeNameList', ], ], ], 'BatchGetObjectAttributesResponse' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'AttributeKeyAndValueList', ], ], ], 'BatchGetObjectInformation' => [ 'type' => 'structure', 'required' => [ 'ObjectReference', ], 'members' => [ 'ObjectReference' => [ 'shape' => 'ObjectReference', ], ], ], 'BatchGetObjectInformationResponse' => [ 'type' => 'structure', 'members' => [ 'SchemaFacets' => [ 'shape' => 'SchemaFacetList', ], 'ObjectIdentifier' => [ 'shape' => 'ObjectIdentifier', ], ], ], 'BatchListAttachedIndices' => [ 'type' => 'structure', 'required' => [ 'TargetReference', ], 'members' => [ 'TargetReference' => [ 'shape' => 'ObjectReference', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'NumberResults', ], ], ], 'BatchListAttachedIndicesResponse' => [ 'type' => 'structure', 'members' => [ 'IndexAttachments' => [ 'shape' => 'IndexAttachmentList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'BatchListIncomingTypedLinks' => [ 'type' => 'structure', 'required' => [ 'ObjectReference', ], 'members' => [ 'ObjectReference' => [ 'shape' => 'ObjectReference', ], 'FilterAttributeRanges' => [ 'shape' => 'TypedLinkAttributeRangeList', ], 'FilterTypedLink' => [ 'shape' => 'TypedLinkSchemaAndFacetName', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'NumberResults', ], ], ], 'BatchListIncomingTypedLinksResponse' => [ 'type' => 'structure', 'members' => [ 'LinkSpecifiers' => [ 'shape' => 'TypedLinkSpecifierList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'BatchListIndex' => [ 'type' => 'structure', 'required' => [ 'IndexReference', ], 'members' => [ 'RangesOnIndexedValues' => [ 'shape' => 'ObjectAttributeRangeList', ], 'IndexReference' => [ 'shape' => 'ObjectReference', ], 'MaxResults' => [ 'shape' => 'NumberResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'BatchListIndexResponse' => [ 'type' => 'structure', 'members' => [ 'IndexAttachments' => [ 'shape' => 'IndexAttachmentList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'BatchListObjectAttributes' => [ 'type' => 'structure', 'required' => [ 'ObjectReference', ], 'members' => [ 'ObjectReference' => [ 'shape' => 'ObjectReference', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'NumberResults', ], 'FacetFilter' => [ 'shape' => 'SchemaFacet', ], ], ], 'BatchListObjectAttributesResponse' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'AttributeKeyAndValueList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'BatchListObjectChildren' => [ 'type' => 'structure', 'required' => [ 'ObjectReference', ], 'members' => [ 'ObjectReference' => [ 'shape' => 'ObjectReference', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'NumberResults', ], ], ], 'BatchListObjectChildrenResponse' => [ 'type' => 'structure', 'members' => [ 'Children' => [ 'shape' => 'LinkNameToObjectIdentifierMap', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'BatchListObjectParentPaths' => [ 'type' => 'structure', 'required' => [ 'ObjectReference', ], 'members' => [ 'ObjectReference' => [ 'shape' => 'ObjectReference', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'NumberResults', ], ], ], 'BatchListObjectParentPathsResponse' => [ 'type' => 'structure', 'members' => [ 'PathToObjectIdentifiersList' => [ 'shape' => 'PathToObjectIdentifiersList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'BatchListObjectParents' => [ 'type' => 'structure', 'required' => [ 'ObjectReference', ], 'members' => [ 'ObjectReference' => [ 'shape' => 'ObjectReference', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'NumberResults', ], ], ], 'BatchListObjectParentsResponse' => [ 'type' => 'structure', 'members' => [ 'ParentLinks' => [ 'shape' => 'ObjectIdentifierAndLinkNameList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'BatchListObjectPolicies' => [ 'type' => 'structure', 'required' => [ 'ObjectReference', ], 'members' => [ 'ObjectReference' => [ 'shape' => 'ObjectReference', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'NumberResults', ], ], ], 'BatchListObjectPoliciesResponse' => [ 'type' => 'structure', 'members' => [ 'AttachedPolicyIds' => [ 'shape' => 'ObjectIdentifierList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'BatchListOutgoingTypedLinks' => [ 'type' => 'structure', 'required' => [ 'ObjectReference', ], 'members' => [ 'ObjectReference' => [ 'shape' => 'ObjectReference', ], 'FilterAttributeRanges' => [ 'shape' => 'TypedLinkAttributeRangeList', ], 'FilterTypedLink' => [ 'shape' => 'TypedLinkSchemaAndFacetName', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'NumberResults', ], ], ], 'BatchListOutgoingTypedLinksResponse' => [ 'type' => 'structure', 'members' => [ 'TypedLinkSpecifiers' => [ 'shape' => 'TypedLinkSpecifierList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'BatchListPolicyAttachments' => [ 'type' => 'structure', 'required' => [ 'PolicyReference', ], 'members' => [ 'PolicyReference' => [ 'shape' => 'ObjectReference', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'NumberResults', ], ], ], 'BatchListPolicyAttachmentsResponse' => [ 'type' => 'structure', 'members' => [ 'ObjectIdentifiers' => [ 'shape' => 'ObjectIdentifierList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'BatchLookupPolicy' => [ 'type' => 'structure', 'required' => [ 'ObjectReference', ], 'members' => [ 'ObjectReference' => [ 'shape' => 'ObjectReference', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'NumberResults', ], ], ], 'BatchLookupPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'PolicyToPathList' => [ 'shape' => 'PolicyToPathList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'BatchOperationIndex' => [ 'type' => 'integer', ], 'BatchReadException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'BatchReadExceptionType', ], 'Message' => [ 'shape' => 'ExceptionMessage', ], ], ], 'BatchReadExceptionType' => [ 'type' => 'string', 'enum' => [ 'ValidationException', 'InvalidArnException', 'ResourceNotFoundException', 'InvalidNextTokenException', 'AccessDeniedException', 'NotNodeException', 'FacetValidationException', 'CannotListParentOfRootException', 'NotIndexException', 'NotPolicyException', 'DirectoryNotEnabledException', 'LimitExceededException', 'InternalServiceException', ], ], 'BatchReadOperation' => [ 'type' => 'structure', 'members' => [ 'ListObjectAttributes' => [ 'shape' => 'BatchListObjectAttributes', ], 'ListObjectChildren' => [ 'shape' => 'BatchListObjectChildren', ], 'ListAttachedIndices' => [ 'shape' => 'BatchListAttachedIndices', ], 'ListObjectParentPaths' => [ 'shape' => 'BatchListObjectParentPaths', ], 'GetObjectInformation' => [ 'shape' => 'BatchGetObjectInformation', ], 'GetObjectAttributes' => [ 'shape' => 'BatchGetObjectAttributes', ], 'ListObjectParents' => [ 'shape' => 'BatchListObjectParents', ], 'ListObjectPolicies' => [ 'shape' => 'BatchListObjectPolicies', ], 'ListPolicyAttachments' => [ 'shape' => 'BatchListPolicyAttachments', ], 'LookupPolicy' => [ 'shape' => 'BatchLookupPolicy', ], 'ListIndex' => [ 'shape' => 'BatchListIndex', ], 'ListOutgoingTypedLinks' => [ 'shape' => 'BatchListOutgoingTypedLinks', ], 'ListIncomingTypedLinks' => [ 'shape' => 'BatchListIncomingTypedLinks', ], 'GetLinkAttributes' => [ 'shape' => 'BatchGetLinkAttributes', ], ], ], 'BatchReadOperationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchReadOperation', ], ], 'BatchReadOperationResponse' => [ 'type' => 'structure', 'members' => [ 'SuccessfulResponse' => [ 'shape' => 'BatchReadSuccessfulResponse', ], 'ExceptionResponse' => [ 'shape' => 'BatchReadException', ], ], ], 'BatchReadOperationResponseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchReadOperationResponse', ], ], 'BatchReadRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', 'Operations', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'Operations' => [ 'shape' => 'BatchReadOperationList', ], 'ConsistencyLevel' => [ 'shape' => 'ConsistencyLevel', 'location' => 'header', 'locationName' => 'x-amz-consistency-level', ], ], ], 'BatchReadResponse' => [ 'type' => 'structure', 'members' => [ 'Responses' => [ 'shape' => 'BatchReadOperationResponseList', ], ], ], 'BatchReadSuccessfulResponse' => [ 'type' => 'structure', 'members' => [ 'ListObjectAttributes' => [ 'shape' => 'BatchListObjectAttributesResponse', ], 'ListObjectChildren' => [ 'shape' => 'BatchListObjectChildrenResponse', ], 'GetObjectInformation' => [ 'shape' => 'BatchGetObjectInformationResponse', ], 'GetObjectAttributes' => [ 'shape' => 'BatchGetObjectAttributesResponse', ], 'ListAttachedIndices' => [ 'shape' => 'BatchListAttachedIndicesResponse', ], 'ListObjectParentPaths' => [ 'shape' => 'BatchListObjectParentPathsResponse', ], 'ListObjectPolicies' => [ 'shape' => 'BatchListObjectPoliciesResponse', ], 'ListPolicyAttachments' => [ 'shape' => 'BatchListPolicyAttachmentsResponse', ], 'LookupPolicy' => [ 'shape' => 'BatchLookupPolicyResponse', ], 'ListIndex' => [ 'shape' => 'BatchListIndexResponse', ], 'ListOutgoingTypedLinks' => [ 'shape' => 'BatchListOutgoingTypedLinksResponse', ], 'ListIncomingTypedLinks' => [ 'shape' => 'BatchListIncomingTypedLinksResponse', ], 'GetLinkAttributes' => [ 'shape' => 'BatchGetLinkAttributesResponse', ], 'ListObjectParents' => [ 'shape' => 'BatchListObjectParentsResponse', ], ], ], 'BatchReferenceName' => [ 'type' => 'string', ], 'BatchRemoveFacetFromObject' => [ 'type' => 'structure', 'required' => [ 'SchemaFacet', 'ObjectReference', ], 'members' => [ 'SchemaFacet' => [ 'shape' => 'SchemaFacet', ], 'ObjectReference' => [ 'shape' => 'ObjectReference', ], ], ], 'BatchRemoveFacetFromObjectResponse' => [ 'type' => 'structure', 'members' => [], ], 'BatchUpdateLinkAttributes' => [ 'type' => 'structure', 'required' => [ 'TypedLinkSpecifier', 'AttributeUpdates', ], 'members' => [ 'TypedLinkSpecifier' => [ 'shape' => 'TypedLinkSpecifier', ], 'AttributeUpdates' => [ 'shape' => 'LinkAttributeUpdateList', ], ], ], 'BatchUpdateLinkAttributesResponse' => [ 'type' => 'structure', 'members' => [], ], 'BatchUpdateObjectAttributes' => [ 'type' => 'structure', 'required' => [ 'ObjectReference', 'AttributeUpdates', ], 'members' => [ 'ObjectReference' => [ 'shape' => 'ObjectReference', ], 'AttributeUpdates' => [ 'shape' => 'ObjectAttributeUpdateList', ], ], ], 'BatchUpdateObjectAttributesResponse' => [ 'type' => 'structure', 'members' => [ 'ObjectIdentifier' => [ 'shape' => 'ObjectIdentifier', ], ], ], 'BatchWriteException' => [ 'type' => 'structure', 'members' => [ 'Index' => [ 'shape' => 'BatchOperationIndex', ], 'Type' => [ 'shape' => 'BatchWriteExceptionType', ], 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'BatchWriteExceptionType' => [ 'type' => 'string', 'enum' => [ 'InternalServiceException', 'ValidationException', 'InvalidArnException', 'LinkNameAlreadyInUseException', 'StillContainsLinksException', 'FacetValidationException', 'ObjectNotDetachedException', 'ResourceNotFoundException', 'AccessDeniedException', 'InvalidAttachmentException', 'NotIndexException', 'NotNodeException', 'IndexedAttributeMissingException', 'ObjectAlreadyDetachedException', 'NotPolicyException', 'DirectoryNotEnabledException', 'LimitExceededException', 'UnsupportedIndexTypeException', ], ], 'BatchWriteOperation' => [ 'type' => 'structure', 'members' => [ 'CreateObject' => [ 'shape' => 'BatchCreateObject', ], 'AttachObject' => [ 'shape' => 'BatchAttachObject', ], 'DetachObject' => [ 'shape' => 'BatchDetachObject', ], 'UpdateObjectAttributes' => [ 'shape' => 'BatchUpdateObjectAttributes', ], 'DeleteObject' => [ 'shape' => 'BatchDeleteObject', ], 'AddFacetToObject' => [ 'shape' => 'BatchAddFacetToObject', ], 'RemoveFacetFromObject' => [ 'shape' => 'BatchRemoveFacetFromObject', ], 'AttachPolicy' => [ 'shape' => 'BatchAttachPolicy', ], 'DetachPolicy' => [ 'shape' => 'BatchDetachPolicy', ], 'CreateIndex' => [ 'shape' => 'BatchCreateIndex', ], 'AttachToIndex' => [ 'shape' => 'BatchAttachToIndex', ], 'DetachFromIndex' => [ 'shape' => 'BatchDetachFromIndex', ], 'AttachTypedLink' => [ 'shape' => 'BatchAttachTypedLink', ], 'DetachTypedLink' => [ 'shape' => 'BatchDetachTypedLink', ], 'UpdateLinkAttributes' => [ 'shape' => 'BatchUpdateLinkAttributes', ], ], ], 'BatchWriteOperationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchWriteOperation', ], ], 'BatchWriteOperationResponse' => [ 'type' => 'structure', 'members' => [ 'CreateObject' => [ 'shape' => 'BatchCreateObjectResponse', ], 'AttachObject' => [ 'shape' => 'BatchAttachObjectResponse', ], 'DetachObject' => [ 'shape' => 'BatchDetachObjectResponse', ], 'UpdateObjectAttributes' => [ 'shape' => 'BatchUpdateObjectAttributesResponse', ], 'DeleteObject' => [ 'shape' => 'BatchDeleteObjectResponse', ], 'AddFacetToObject' => [ 'shape' => 'BatchAddFacetToObjectResponse', ], 'RemoveFacetFromObject' => [ 'shape' => 'BatchRemoveFacetFromObjectResponse', ], 'AttachPolicy' => [ 'shape' => 'BatchAttachPolicyResponse', ], 'DetachPolicy' => [ 'shape' => 'BatchDetachPolicyResponse', ], 'CreateIndex' => [ 'shape' => 'BatchCreateIndexResponse', ], 'AttachToIndex' => [ 'shape' => 'BatchAttachToIndexResponse', ], 'DetachFromIndex' => [ 'shape' => 'BatchDetachFromIndexResponse', ], 'AttachTypedLink' => [ 'shape' => 'BatchAttachTypedLinkResponse', ], 'DetachTypedLink' => [ 'shape' => 'BatchDetachTypedLinkResponse', ], 'UpdateLinkAttributes' => [ 'shape' => 'BatchUpdateLinkAttributesResponse', ], ], ], 'BatchWriteOperationResponseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchWriteOperationResponse', ], ], 'BatchWriteRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', 'Operations', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'Operations' => [ 'shape' => 'BatchWriteOperationList', ], ], ], 'BatchWriteResponse' => [ 'type' => 'structure', 'members' => [ 'Responses' => [ 'shape' => 'BatchWriteOperationResponseList', ], ], ], 'BinaryAttributeValue' => [ 'type' => 'blob', ], 'Bool' => [ 'type' => 'boolean', ], 'BooleanAttributeValue' => [ 'type' => 'boolean', ], 'CannotListParentOfRootException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ConsistencyLevel' => [ 'type' => 'string', 'enum' => [ 'SERIALIZABLE', 'EVENTUAL', ], ], 'CreateDirectoryRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'SchemaArn', ], 'members' => [ 'Name' => [ 'shape' => 'DirectoryName', ], 'SchemaArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], ], ], 'CreateDirectoryResponse' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', 'Name', 'ObjectIdentifier', 'AppliedSchemaArn', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'DirectoryArn', ], 'Name' => [ 'shape' => 'DirectoryName', ], 'ObjectIdentifier' => [ 'shape' => 'ObjectIdentifier', ], 'AppliedSchemaArn' => [ 'shape' => 'Arn', ], ], ], 'CreateFacetRequest' => [ 'type' => 'structure', 'required' => [ 'SchemaArn', 'Name', ], 'members' => [ 'SchemaArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'Name' => [ 'shape' => 'FacetName', ], 'Attributes' => [ 'shape' => 'FacetAttributeList', ], 'ObjectType' => [ 'shape' => 'ObjectType', ], 'FacetStyle' => [ 'shape' => 'FacetStyle', ], ], ], 'CreateFacetResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateIndexRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', 'OrderedIndexedAttributeList', 'IsUnique', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'OrderedIndexedAttributeList' => [ 'shape' => 'AttributeKeyList', ], 'IsUnique' => [ 'shape' => 'Bool', ], 'ParentReference' => [ 'shape' => 'ObjectReference', ], 'LinkName' => [ 'shape' => 'LinkName', ], ], ], 'CreateIndexResponse' => [ 'type' => 'structure', 'members' => [ 'ObjectIdentifier' => [ 'shape' => 'ObjectIdentifier', ], ], ], 'CreateObjectRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', 'SchemaFacets', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'SchemaFacets' => [ 'shape' => 'SchemaFacetList', ], 'ObjectAttributeList' => [ 'shape' => 'AttributeKeyAndValueList', ], 'ParentReference' => [ 'shape' => 'ObjectReference', ], 'LinkName' => [ 'shape' => 'LinkName', ], ], ], 'CreateObjectResponse' => [ 'type' => 'structure', 'members' => [ 'ObjectIdentifier' => [ 'shape' => 'ObjectIdentifier', ], ], ], 'CreateSchemaRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'SchemaName', ], ], ], 'CreateSchemaResponse' => [ 'type' => 'structure', 'members' => [ 'SchemaArn' => [ 'shape' => 'Arn', ], ], ], 'CreateTypedLinkFacetRequest' => [ 'type' => 'structure', 'required' => [ 'SchemaArn', 'Facet', ], 'members' => [ 'SchemaArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'Facet' => [ 'shape' => 'TypedLinkFacet', ], ], ], 'CreateTypedLinkFacetResponse' => [ 'type' => 'structure', 'members' => [], ], 'Date' => [ 'type' => 'timestamp', ], 'DatetimeAttributeValue' => [ 'type' => 'timestamp', ], 'DeleteDirectoryRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], ], ], 'DeleteDirectoryResponse' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteFacetRequest' => [ 'type' => 'structure', 'required' => [ 'SchemaArn', 'Name', ], 'members' => [ 'SchemaArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'Name' => [ 'shape' => 'FacetName', ], ], ], 'DeleteFacetResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteObjectRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', 'ObjectReference', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'ObjectReference' => [ 'shape' => 'ObjectReference', ], ], ], 'DeleteObjectResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSchemaRequest' => [ 'type' => 'structure', 'required' => [ 'SchemaArn', ], 'members' => [ 'SchemaArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], ], ], 'DeleteSchemaResponse' => [ 'type' => 'structure', 'members' => [ 'SchemaArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteTypedLinkFacetRequest' => [ 'type' => 'structure', 'required' => [ 'SchemaArn', 'Name', ], 'members' => [ 'SchemaArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'Name' => [ 'shape' => 'TypedLinkName', ], ], ], 'DeleteTypedLinkFacetResponse' => [ 'type' => 'structure', 'members' => [], ], 'DetachFromIndexRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', 'IndexReference', 'TargetReference', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'IndexReference' => [ 'shape' => 'ObjectReference', ], 'TargetReference' => [ 'shape' => 'ObjectReference', ], ], ], 'DetachFromIndexResponse' => [ 'type' => 'structure', 'members' => [ 'DetachedObjectIdentifier' => [ 'shape' => 'ObjectIdentifier', ], ], ], 'DetachObjectRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', 'ParentReference', 'LinkName', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'ParentReference' => [ 'shape' => 'ObjectReference', ], 'LinkName' => [ 'shape' => 'LinkName', ], ], ], 'DetachObjectResponse' => [ 'type' => 'structure', 'members' => [ 'DetachedObjectIdentifier' => [ 'shape' => 'ObjectIdentifier', ], ], ], 'DetachPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', 'PolicyReference', 'ObjectReference', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'PolicyReference' => [ 'shape' => 'ObjectReference', ], 'ObjectReference' => [ 'shape' => 'ObjectReference', ], ], ], 'DetachPolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'DetachTypedLinkRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', 'TypedLinkSpecifier', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'TypedLinkSpecifier' => [ 'shape' => 'TypedLinkSpecifier', ], ], ], 'Directory' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'DirectoryName', ], 'DirectoryArn' => [ 'shape' => 'DirectoryArn', ], 'State' => [ 'shape' => 'DirectoryState', ], 'CreationDateTime' => [ 'shape' => 'Date', ], ], ], 'DirectoryAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'DirectoryArn' => [ 'type' => 'string', ], 'DirectoryDeletedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'DirectoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Directory', ], ], 'DirectoryName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9._-]*$', ], 'DirectoryNotDisabledException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'DirectoryNotEnabledException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'DirectoryState' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', 'DELETED', ], ], 'DisableDirectoryRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], ], ], 'DisableDirectoryResponse' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', ], ], ], 'EnableDirectoryRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], ], ], 'EnableDirectoryResponse' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', ], ], ], 'ExceptionMessage' => [ 'type' => 'string', ], 'Facet' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'FacetName', ], 'ObjectType' => [ 'shape' => 'ObjectType', ], 'FacetStyle' => [ 'shape' => 'FacetStyle', ], ], ], 'FacetAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'FacetAttribute' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'AttributeName', ], 'AttributeDefinition' => [ 'shape' => 'FacetAttributeDefinition', ], 'AttributeReference' => [ 'shape' => 'FacetAttributeReference', ], 'RequiredBehavior' => [ 'shape' => 'RequiredAttributeBehavior', ], ], ], 'FacetAttributeDefinition' => [ 'type' => 'structure', 'required' => [ 'Type', ], 'members' => [ 'Type' => [ 'shape' => 'FacetAttributeType', ], 'DefaultValue' => [ 'shape' => 'TypedAttributeValue', ], 'IsImmutable' => [ 'shape' => 'Bool', ], 'Rules' => [ 'shape' => 'RuleMap', ], ], ], 'FacetAttributeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FacetAttribute', ], ], 'FacetAttributeReference' => [ 'type' => 'structure', 'required' => [ 'TargetFacetName', 'TargetAttributeName', ], 'members' => [ 'TargetFacetName' => [ 'shape' => 'FacetName', ], 'TargetAttributeName' => [ 'shape' => 'AttributeName', ], ], ], 'FacetAttributeType' => [ 'type' => 'string', 'enum' => [ 'STRING', 'BINARY', 'BOOLEAN', 'NUMBER', 'DATETIME', 'VARIANT', ], ], 'FacetAttributeUpdate' => [ 'type' => 'structure', 'members' => [ 'Attribute' => [ 'shape' => 'FacetAttribute', ], 'Action' => [ 'shape' => 'UpdateActionType', ], ], ], 'FacetAttributeUpdateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FacetAttributeUpdate', ], ], 'FacetInUseException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'FacetName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9._-]*$', ], 'FacetNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FacetName', ], ], 'FacetNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'FacetStyle' => [ 'type' => 'string', 'enum' => [ 'STATIC', 'DYNAMIC', ], ], 'FacetValidationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'GetAppliedSchemaVersionRequest' => [ 'type' => 'structure', 'required' => [ 'SchemaArn', ], 'members' => [ 'SchemaArn' => [ 'shape' => 'Arn', ], ], ], 'GetAppliedSchemaVersionResponse' => [ 'type' => 'structure', 'members' => [ 'AppliedSchemaArn' => [ 'shape' => 'Arn', ], ], ], 'GetDirectoryRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'DirectoryArn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], ], ], 'GetDirectoryResponse' => [ 'type' => 'structure', 'required' => [ 'Directory', ], 'members' => [ 'Directory' => [ 'shape' => 'Directory', ], ], ], 'GetFacetRequest' => [ 'type' => 'structure', 'required' => [ 'SchemaArn', 'Name', ], 'members' => [ 'SchemaArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'Name' => [ 'shape' => 'FacetName', ], ], ], 'GetFacetResponse' => [ 'type' => 'structure', 'members' => [ 'Facet' => [ 'shape' => 'Facet', ], ], ], 'GetLinkAttributesRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', 'TypedLinkSpecifier', 'AttributeNames', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'TypedLinkSpecifier' => [ 'shape' => 'TypedLinkSpecifier', ], 'AttributeNames' => [ 'shape' => 'AttributeNameList', ], 'ConsistencyLevel' => [ 'shape' => 'ConsistencyLevel', ], ], ], 'GetLinkAttributesResponse' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'AttributeKeyAndValueList', ], ], ], 'GetObjectAttributesRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', 'ObjectReference', 'SchemaFacet', 'AttributeNames', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'ObjectReference' => [ 'shape' => 'ObjectReference', ], 'ConsistencyLevel' => [ 'shape' => 'ConsistencyLevel', 'location' => 'header', 'locationName' => 'x-amz-consistency-level', ], 'SchemaFacet' => [ 'shape' => 'SchemaFacet', ], 'AttributeNames' => [ 'shape' => 'AttributeNameList', ], ], ], 'GetObjectAttributesResponse' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'AttributeKeyAndValueList', ], ], ], 'GetObjectInformationRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', 'ObjectReference', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'ObjectReference' => [ 'shape' => 'ObjectReference', ], 'ConsistencyLevel' => [ 'shape' => 'ConsistencyLevel', 'location' => 'header', 'locationName' => 'x-amz-consistency-level', ], ], ], 'GetObjectInformationResponse' => [ 'type' => 'structure', 'members' => [ 'SchemaFacets' => [ 'shape' => 'SchemaFacetList', ], 'ObjectIdentifier' => [ 'shape' => 'ObjectIdentifier', ], ], ], 'GetSchemaAsJsonRequest' => [ 'type' => 'structure', 'required' => [ 'SchemaArn', ], 'members' => [ 'SchemaArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], ], ], 'GetSchemaAsJsonResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'SchemaName', ], 'Document' => [ 'shape' => 'SchemaJsonDocument', ], ], ], 'GetTypedLinkFacetInformationRequest' => [ 'type' => 'structure', 'required' => [ 'SchemaArn', 'Name', ], 'members' => [ 'SchemaArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'Name' => [ 'shape' => 'TypedLinkName', ], ], ], 'GetTypedLinkFacetInformationResponse' => [ 'type' => 'structure', 'members' => [ 'IdentityAttributeOrder' => [ 'shape' => 'AttributeNameList', ], ], ], 'IncompatibleSchemaException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'IndexAttachment' => [ 'type' => 'structure', 'members' => [ 'IndexedAttributes' => [ 'shape' => 'AttributeKeyAndValueList', ], 'ObjectIdentifier' => [ 'shape' => 'ObjectIdentifier', ], ], ], 'IndexAttachmentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IndexAttachment', ], ], 'IndexedAttributeMissingException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InternalServiceException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, ], 'InvalidArnException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidAttachmentException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidFacetUpdateException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidNextTokenException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidRuleException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidSchemaDocException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidTaggingRequestException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'LinkAttributeAction' => [ 'type' => 'structure', 'members' => [ 'AttributeActionType' => [ 'shape' => 'UpdateActionType', ], 'AttributeUpdateValue' => [ 'shape' => 'TypedAttributeValue', ], ], ], 'LinkAttributeUpdate' => [ 'type' => 'structure', 'members' => [ 'AttributeKey' => [ 'shape' => 'AttributeKey', ], 'AttributeAction' => [ 'shape' => 'LinkAttributeAction', ], ], ], 'LinkAttributeUpdateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LinkAttributeUpdate', ], ], 'LinkName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[^\\/\\[\\]\\(\\):\\{\\}#@!?\\s\\\\;]+', ], 'LinkNameAlreadyInUseException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'LinkNameToObjectIdentifierMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'LinkName', ], 'value' => [ 'shape' => 'ObjectIdentifier', ], ], 'ListAppliedSchemaArnsRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', ], 'SchemaArn' => [ 'shape' => 'Arn', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'NumberResults', ], ], ], 'ListAppliedSchemaArnsResponse' => [ 'type' => 'structure', 'members' => [ 'SchemaArns' => [ 'shape' => 'Arns', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAttachedIndicesRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', 'TargetReference', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'TargetReference' => [ 'shape' => 'ObjectReference', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'NumberResults', ], 'ConsistencyLevel' => [ 'shape' => 'ConsistencyLevel', 'location' => 'header', 'locationName' => 'x-amz-consistency-level', ], ], ], 'ListAttachedIndicesResponse' => [ 'type' => 'structure', 'members' => [ 'IndexAttachments' => [ 'shape' => 'IndexAttachmentList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDevelopmentSchemaArnsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'NumberResults', ], ], ], 'ListDevelopmentSchemaArnsResponse' => [ 'type' => 'structure', 'members' => [ 'SchemaArns' => [ 'shape' => 'Arns', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDirectoriesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'NumberResults', ], 'state' => [ 'shape' => 'DirectoryState', ], ], ], 'ListDirectoriesResponse' => [ 'type' => 'structure', 'required' => [ 'Directories', ], 'members' => [ 'Directories' => [ 'shape' => 'DirectoryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListFacetAttributesRequest' => [ 'type' => 'structure', 'required' => [ 'SchemaArn', 'Name', ], 'members' => [ 'SchemaArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'Name' => [ 'shape' => 'FacetName', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'NumberResults', ], ], ], 'ListFacetAttributesResponse' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'FacetAttributeList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListFacetNamesRequest' => [ 'type' => 'structure', 'required' => [ 'SchemaArn', ], 'members' => [ 'SchemaArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'NumberResults', ], ], ], 'ListFacetNamesResponse' => [ 'type' => 'structure', 'members' => [ 'FacetNames' => [ 'shape' => 'FacetNameList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListIncomingTypedLinksRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', 'ObjectReference', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'ObjectReference' => [ 'shape' => 'ObjectReference', ], 'FilterAttributeRanges' => [ 'shape' => 'TypedLinkAttributeRangeList', ], 'FilterTypedLink' => [ 'shape' => 'TypedLinkSchemaAndFacetName', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'NumberResults', ], 'ConsistencyLevel' => [ 'shape' => 'ConsistencyLevel', ], ], ], 'ListIncomingTypedLinksResponse' => [ 'type' => 'structure', 'members' => [ 'LinkSpecifiers' => [ 'shape' => 'TypedLinkSpecifierList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListIndexRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', 'IndexReference', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'RangesOnIndexedValues' => [ 'shape' => 'ObjectAttributeRangeList', ], 'IndexReference' => [ 'shape' => 'ObjectReference', ], 'MaxResults' => [ 'shape' => 'NumberResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'ConsistencyLevel' => [ 'shape' => 'ConsistencyLevel', 'location' => 'header', 'locationName' => 'x-amz-consistency-level', ], ], ], 'ListIndexResponse' => [ 'type' => 'structure', 'members' => [ 'IndexAttachments' => [ 'shape' => 'IndexAttachmentList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListManagedSchemaArnsRequest' => [ 'type' => 'structure', 'members' => [ 'SchemaArn' => [ 'shape' => 'Arn', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'NumberResults', ], ], ], 'ListManagedSchemaArnsResponse' => [ 'type' => 'structure', 'members' => [ 'SchemaArns' => [ 'shape' => 'Arns', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListObjectAttributesRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', 'ObjectReference', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'ObjectReference' => [ 'shape' => 'ObjectReference', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'NumberResults', ], 'ConsistencyLevel' => [ 'shape' => 'ConsistencyLevel', 'location' => 'header', 'locationName' => 'x-amz-consistency-level', ], 'FacetFilter' => [ 'shape' => 'SchemaFacet', ], ], ], 'ListObjectAttributesResponse' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'AttributeKeyAndValueList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListObjectChildrenRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', 'ObjectReference', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'ObjectReference' => [ 'shape' => 'ObjectReference', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'NumberResults', ], 'ConsistencyLevel' => [ 'shape' => 'ConsistencyLevel', 'location' => 'header', 'locationName' => 'x-amz-consistency-level', ], ], ], 'ListObjectChildrenResponse' => [ 'type' => 'structure', 'members' => [ 'Children' => [ 'shape' => 'LinkNameToObjectIdentifierMap', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListObjectParentPathsRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', 'ObjectReference', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'ObjectReference' => [ 'shape' => 'ObjectReference', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'NumberResults', ], ], ], 'ListObjectParentPathsResponse' => [ 'type' => 'structure', 'members' => [ 'PathToObjectIdentifiersList' => [ 'shape' => 'PathToObjectIdentifiersList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListObjectParentsRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', 'ObjectReference', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'ObjectReference' => [ 'shape' => 'ObjectReference', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'NumberResults', ], 'ConsistencyLevel' => [ 'shape' => 'ConsistencyLevel', 'location' => 'header', 'locationName' => 'x-amz-consistency-level', ], 'IncludeAllLinksToEachParent' => [ 'shape' => 'Bool', ], ], ], 'ListObjectParentsResponse' => [ 'type' => 'structure', 'members' => [ 'Parents' => [ 'shape' => 'ObjectIdentifierToLinkNameMap', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'ParentLinks' => [ 'shape' => 'ObjectIdentifierAndLinkNameList', ], ], ], 'ListObjectPoliciesRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', 'ObjectReference', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'ObjectReference' => [ 'shape' => 'ObjectReference', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'NumberResults', ], 'ConsistencyLevel' => [ 'shape' => 'ConsistencyLevel', 'location' => 'header', 'locationName' => 'x-amz-consistency-level', ], ], ], 'ListObjectPoliciesResponse' => [ 'type' => 'structure', 'members' => [ 'AttachedPolicyIds' => [ 'shape' => 'ObjectIdentifierList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListOutgoingTypedLinksRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', 'ObjectReference', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'ObjectReference' => [ 'shape' => 'ObjectReference', ], 'FilterAttributeRanges' => [ 'shape' => 'TypedLinkAttributeRangeList', ], 'FilterTypedLink' => [ 'shape' => 'TypedLinkSchemaAndFacetName', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'NumberResults', ], 'ConsistencyLevel' => [ 'shape' => 'ConsistencyLevel', ], ], ], 'ListOutgoingTypedLinksResponse' => [ 'type' => 'structure', 'members' => [ 'TypedLinkSpecifiers' => [ 'shape' => 'TypedLinkSpecifierList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListPolicyAttachmentsRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', 'PolicyReference', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'PolicyReference' => [ 'shape' => 'ObjectReference', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'NumberResults', ], 'ConsistencyLevel' => [ 'shape' => 'ConsistencyLevel', 'location' => 'header', 'locationName' => 'x-amz-consistency-level', ], ], ], 'ListPolicyAttachmentsResponse' => [ 'type' => 'structure', 'members' => [ 'ObjectIdentifiers' => [ 'shape' => 'ObjectIdentifierList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListPublishedSchemaArnsRequest' => [ 'type' => 'structure', 'members' => [ 'SchemaArn' => [ 'shape' => 'Arn', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'NumberResults', ], ], ], 'ListPublishedSchemaArnsResponse' => [ 'type' => 'structure', 'members' => [ 'SchemaArns' => [ 'shape' => 'Arns', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'TagsNumberResults', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTypedLinkFacetAttributesRequest' => [ 'type' => 'structure', 'required' => [ 'SchemaArn', 'Name', ], 'members' => [ 'SchemaArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'Name' => [ 'shape' => 'TypedLinkName', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'NumberResults', ], ], ], 'ListTypedLinkFacetAttributesResponse' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'TypedLinkAttributeDefinitionList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTypedLinkFacetNamesRequest' => [ 'type' => 'structure', 'required' => [ 'SchemaArn', ], 'members' => [ 'SchemaArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'NumberResults', ], ], ], 'ListTypedLinkFacetNamesResponse' => [ 'type' => 'structure', 'members' => [ 'FacetNames' => [ 'shape' => 'TypedLinkNameList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'LookupPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', 'ObjectReference', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'ObjectReference' => [ 'shape' => 'ObjectReference', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'NumberResults', ], ], ], 'LookupPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'PolicyToPathList' => [ 'shape' => 'PolicyToPathList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'NextToken' => [ 'type' => 'string', ], 'NotIndexException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'NotNodeException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'NotPolicyException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'NumberAttributeValue' => [ 'type' => 'string', ], 'NumberResults' => [ 'type' => 'integer', 'min' => 1, ], 'ObjectAlreadyDetachedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ObjectAttributeAction' => [ 'type' => 'structure', 'members' => [ 'ObjectAttributeActionType' => [ 'shape' => 'UpdateActionType', ], 'ObjectAttributeUpdateValue' => [ 'shape' => 'TypedAttributeValue', ], ], ], 'ObjectAttributeRange' => [ 'type' => 'structure', 'members' => [ 'AttributeKey' => [ 'shape' => 'AttributeKey', ], 'Range' => [ 'shape' => 'TypedAttributeValueRange', ], ], ], 'ObjectAttributeRangeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ObjectAttributeRange', ], ], 'ObjectAttributeUpdate' => [ 'type' => 'structure', 'members' => [ 'ObjectAttributeKey' => [ 'shape' => 'AttributeKey', ], 'ObjectAttributeAction' => [ 'shape' => 'ObjectAttributeAction', ], ], ], 'ObjectAttributeUpdateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ObjectAttributeUpdate', ], ], 'ObjectIdentifier' => [ 'type' => 'string', ], 'ObjectIdentifierAndLinkNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ObjectIdentifierAndLinkNameTuple', ], ], 'ObjectIdentifierAndLinkNameTuple' => [ 'type' => 'structure', 'members' => [ 'ObjectIdentifier' => [ 'shape' => 'ObjectIdentifier', ], 'LinkName' => [ 'shape' => 'LinkName', ], ], ], 'ObjectIdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ObjectIdentifier', ], ], 'ObjectIdentifierToLinkNameMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ObjectIdentifier', ], 'value' => [ 'shape' => 'LinkName', ], ], 'ObjectNotDetachedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ObjectReference' => [ 'type' => 'structure', 'members' => [ 'Selector' => [ 'shape' => 'SelectorObjectReference', ], ], ], 'ObjectType' => [ 'type' => 'string', 'enum' => [ 'NODE', 'LEAF_NODE', 'POLICY', 'INDEX', ], ], 'PathString' => [ 'type' => 'string', ], 'PathToObjectIdentifiers' => [ 'type' => 'structure', 'members' => [ 'Path' => [ 'shape' => 'PathString', ], 'ObjectIdentifiers' => [ 'shape' => 'ObjectIdentifierList', ], ], ], 'PathToObjectIdentifiersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PathToObjectIdentifiers', ], ], 'PolicyAttachment' => [ 'type' => 'structure', 'members' => [ 'PolicyId' => [ 'shape' => 'ObjectIdentifier', ], 'ObjectIdentifier' => [ 'shape' => 'ObjectIdentifier', ], 'PolicyType' => [ 'shape' => 'PolicyType', ], ], ], 'PolicyAttachmentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PolicyAttachment', ], ], 'PolicyToPath' => [ 'type' => 'structure', 'members' => [ 'Path' => [ 'shape' => 'PathString', ], 'Policies' => [ 'shape' => 'PolicyAttachmentList', ], ], ], 'PolicyToPathList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PolicyToPath', ], ], 'PolicyType' => [ 'type' => 'string', ], 'PublishSchemaRequest' => [ 'type' => 'structure', 'required' => [ 'DevelopmentSchemaArn', 'Version', ], 'members' => [ 'DevelopmentSchemaArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'Version' => [ 'shape' => 'Version', ], 'MinorVersion' => [ 'shape' => 'Version', ], 'Name' => [ 'shape' => 'SchemaName', ], ], ], 'PublishSchemaResponse' => [ 'type' => 'structure', 'members' => [ 'PublishedSchemaArn' => [ 'shape' => 'Arn', ], ], ], 'PutSchemaFromJsonRequest' => [ 'type' => 'structure', 'required' => [ 'SchemaArn', 'Document', ], 'members' => [ 'SchemaArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'Document' => [ 'shape' => 'SchemaJsonDocument', ], ], ], 'PutSchemaFromJsonResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], ], ], 'RangeMode' => [ 'type' => 'string', 'enum' => [ 'FIRST', 'LAST', 'LAST_BEFORE_MISSING_VALUES', 'INCLUSIVE', 'EXCLUSIVE', ], ], 'RemoveFacetFromObjectRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', 'SchemaFacet', 'ObjectReference', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'SchemaFacet' => [ 'shape' => 'SchemaFacet', ], 'ObjectReference' => [ 'shape' => 'ObjectReference', ], ], ], 'RemoveFacetFromObjectResponse' => [ 'type' => 'structure', 'members' => [], ], 'RequiredAttributeBehavior' => [ 'type' => 'string', 'enum' => [ 'REQUIRED_ALWAYS', 'NOT_REQUIRED', ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'RetryableConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'Rule' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'RuleType', ], 'Parameters' => [ 'shape' => 'RuleParameterMap', ], ], ], 'RuleKey' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9._-]*$', ], 'RuleMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'RuleKey', ], 'value' => [ 'shape' => 'Rule', ], ], 'RuleParameterKey' => [ 'type' => 'string', ], 'RuleParameterMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'RuleParameterKey', ], 'value' => [ 'shape' => 'RuleParameterValue', ], ], 'RuleParameterValue' => [ 'type' => 'string', ], 'RuleType' => [ 'type' => 'string', 'enum' => [ 'BINARY_LENGTH', 'NUMBER_COMPARISON', 'STRING_FROM_SET', 'STRING_LENGTH', ], ], 'SchemaAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'SchemaAlreadyPublishedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'SchemaFacet' => [ 'type' => 'structure', 'members' => [ 'SchemaArn' => [ 'shape' => 'Arn', ], 'FacetName' => [ 'shape' => 'FacetName', ], ], ], 'SchemaFacetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SchemaFacet', ], ], 'SchemaJsonDocument' => [ 'type' => 'string', ], 'SchemaName' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '^[a-zA-Z0-9._-]*$', ], 'SelectorObjectReference' => [ 'type' => 'string', ], 'StillContainsLinksException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'StringAttributeValue' => [ 'type' => 'string', ], 'Tag' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', ], 'TagsNumberResults' => [ 'type' => 'integer', 'min' => 50, ], 'TypedAttributeValue' => [ 'type' => 'structure', 'members' => [ 'StringValue' => [ 'shape' => 'StringAttributeValue', ], 'BinaryValue' => [ 'shape' => 'BinaryAttributeValue', ], 'BooleanValue' => [ 'shape' => 'BooleanAttributeValue', ], 'NumberValue' => [ 'shape' => 'NumberAttributeValue', ], 'DatetimeValue' => [ 'shape' => 'DatetimeAttributeValue', ], ], ], 'TypedAttributeValueRange' => [ 'type' => 'structure', 'required' => [ 'StartMode', 'EndMode', ], 'members' => [ 'StartMode' => [ 'shape' => 'RangeMode', ], 'StartValue' => [ 'shape' => 'TypedAttributeValue', ], 'EndMode' => [ 'shape' => 'RangeMode', ], 'EndValue' => [ 'shape' => 'TypedAttributeValue', ], ], ], 'TypedLinkAttributeDefinition' => [ 'type' => 'structure', 'required' => [ 'Name', 'Type', 'RequiredBehavior', ], 'members' => [ 'Name' => [ 'shape' => 'AttributeName', ], 'Type' => [ 'shape' => 'FacetAttributeType', ], 'DefaultValue' => [ 'shape' => 'TypedAttributeValue', ], 'IsImmutable' => [ 'shape' => 'Bool', ], 'Rules' => [ 'shape' => 'RuleMap', ], 'RequiredBehavior' => [ 'shape' => 'RequiredAttributeBehavior', ], ], ], 'TypedLinkAttributeDefinitionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TypedLinkAttributeDefinition', ], ], 'TypedLinkAttributeRange' => [ 'type' => 'structure', 'required' => [ 'Range', ], 'members' => [ 'AttributeName' => [ 'shape' => 'AttributeName', ], 'Range' => [ 'shape' => 'TypedAttributeValueRange', ], ], ], 'TypedLinkAttributeRangeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TypedLinkAttributeRange', ], ], 'TypedLinkFacet' => [ 'type' => 'structure', 'required' => [ 'Name', 'Attributes', 'IdentityAttributeOrder', ], 'members' => [ 'Name' => [ 'shape' => 'TypedLinkName', ], 'Attributes' => [ 'shape' => 'TypedLinkAttributeDefinitionList', ], 'IdentityAttributeOrder' => [ 'shape' => 'AttributeNameList', ], ], ], 'TypedLinkFacetAttributeUpdate' => [ 'type' => 'structure', 'required' => [ 'Attribute', 'Action', ], 'members' => [ 'Attribute' => [ 'shape' => 'TypedLinkAttributeDefinition', ], 'Action' => [ 'shape' => 'UpdateActionType', ], ], ], 'TypedLinkFacetAttributeUpdateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TypedLinkFacetAttributeUpdate', ], ], 'TypedLinkName' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9._-]*$', ], 'TypedLinkNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TypedLinkName', ], ], 'TypedLinkSchemaAndFacetName' => [ 'type' => 'structure', 'required' => [ 'SchemaArn', 'TypedLinkName', ], 'members' => [ 'SchemaArn' => [ 'shape' => 'Arn', ], 'TypedLinkName' => [ 'shape' => 'TypedLinkName', ], ], ], 'TypedLinkSpecifier' => [ 'type' => 'structure', 'required' => [ 'TypedLinkFacet', 'SourceObjectReference', 'TargetObjectReference', 'IdentityAttributeValues', ], 'members' => [ 'TypedLinkFacet' => [ 'shape' => 'TypedLinkSchemaAndFacetName', ], 'SourceObjectReference' => [ 'shape' => 'ObjectReference', ], 'TargetObjectReference' => [ 'shape' => 'ObjectReference', ], 'IdentityAttributeValues' => [ 'shape' => 'AttributeNameAndValueList', ], ], ], 'TypedLinkSpecifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TypedLinkSpecifier', ], ], 'UnsupportedIndexTypeException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateActionType' => [ 'type' => 'string', 'enum' => [ 'CREATE_OR_UPDATE', 'DELETE', ], ], 'UpdateFacetRequest' => [ 'type' => 'structure', 'required' => [ 'SchemaArn', 'Name', ], 'members' => [ 'SchemaArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'Name' => [ 'shape' => 'FacetName', ], 'AttributeUpdates' => [ 'shape' => 'FacetAttributeUpdateList', ], 'ObjectType' => [ 'shape' => 'ObjectType', ], ], ], 'UpdateFacetResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateLinkAttributesRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', 'TypedLinkSpecifier', 'AttributeUpdates', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'TypedLinkSpecifier' => [ 'shape' => 'TypedLinkSpecifier', ], 'AttributeUpdates' => [ 'shape' => 'LinkAttributeUpdateList', ], ], ], 'UpdateLinkAttributesResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateObjectAttributesRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryArn', 'ObjectReference', 'AttributeUpdates', ], 'members' => [ 'DirectoryArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'ObjectReference' => [ 'shape' => 'ObjectReference', ], 'AttributeUpdates' => [ 'shape' => 'ObjectAttributeUpdateList', ], ], ], 'UpdateObjectAttributesResponse' => [ 'type' => 'structure', 'members' => [ 'ObjectIdentifier' => [ 'shape' => 'ObjectIdentifier', ], ], ], 'UpdateSchemaRequest' => [ 'type' => 'structure', 'required' => [ 'SchemaArn', 'Name', ], 'members' => [ 'SchemaArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'Name' => [ 'shape' => 'SchemaName', ], ], ], 'UpdateSchemaResponse' => [ 'type' => 'structure', 'members' => [ 'SchemaArn' => [ 'shape' => 'Arn', ], ], ], 'UpdateTypedLinkFacetRequest' => [ 'type' => 'structure', 'required' => [ 'SchemaArn', 'Name', 'AttributeUpdates', 'IdentityAttributeOrder', ], 'members' => [ 'SchemaArn' => [ 'shape' => 'Arn', 'location' => 'header', 'locationName' => 'x-amz-data-partition', ], 'Name' => [ 'shape' => 'TypedLinkName', ], 'AttributeUpdates' => [ 'shape' => 'TypedLinkFacetAttributeUpdateList', ], 'IdentityAttributeOrder' => [ 'shape' => 'AttributeNameList', ], ], ], 'UpdateTypedLinkFacetResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpgradeAppliedSchemaRequest' => [ 'type' => 'structure', 'required' => [ 'PublishedSchemaArn', 'DirectoryArn', ], 'members' => [ 'PublishedSchemaArn' => [ 'shape' => 'Arn', ], 'DirectoryArn' => [ 'shape' => 'Arn', ], 'DryRun' => [ 'shape' => 'Bool', ], ], ], 'UpgradeAppliedSchemaResponse' => [ 'type' => 'structure', 'members' => [ 'UpgradedSchemaArn' => [ 'shape' => 'Arn', ], 'DirectoryArn' => [ 'shape' => 'Arn', ], ], ], 'UpgradePublishedSchemaRequest' => [ 'type' => 'structure', 'required' => [ 'DevelopmentSchemaArn', 'PublishedSchemaArn', 'MinorVersion', ], 'members' => [ 'DevelopmentSchemaArn' => [ 'shape' => 'Arn', ], 'PublishedSchemaArn' => [ 'shape' => 'Arn', ], 'MinorVersion' => [ 'shape' => 'Version', ], 'DryRun' => [ 'shape' => 'Bool', ], ], ], 'UpgradePublishedSchemaResponse' => [ 'type' => 'structure', 'members' => [ 'UpgradedSchemaArn' => [ 'shape' => 'Arn', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'Version' => [ 'type' => 'string', 'max' => 10, 'min' => 1, 'pattern' => '^[a-zA-Z0-9._-]*$', ], ],];
