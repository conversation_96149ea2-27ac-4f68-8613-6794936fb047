<?php
// This file was auto-generated from sdk-root/src/data/cloudtrail/2013-11-01/paginators-1.json
return [ 'pagination' => [ 'DescribeTrails' => [ 'result_key' => 'trailList', ], 'GetQueryResults' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', ], 'ListChannels' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', ], 'ListEventDataStores' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', ], 'ListImportFailures' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'Failures', ], 'ListImports' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'Imports', ], 'ListInsightsMetricData' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', ], 'ListPublicKeys' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'result_key' => 'PublicKeyList', ], 'ListQueries' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', ], 'ListTags' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'result_key' => 'ResourceTagList', ], 'ListTrails' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'result_key' => 'Trails', ], 'LookupEvents' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'Events', ], ],];
