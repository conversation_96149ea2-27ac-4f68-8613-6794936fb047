{"name": "ramsey/collection", "description": "A PHP library for representing and manipulating collections.", "license": "MIT", "type": "library", "keywords": ["array", "collection", "hash", "map", "queue", "set"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}], "require": {"php": "^8.1"}, "require-dev": {"captainhook/plugin-composer": "^5.3", "ergebnis/composer-normalize": "^2.28.3", "fakerphp/faker": "^1.21", "hamcrest/hamcrest-php": "^2.0", "jangregor/phpstan-prophecy": "^1.0", "mockery/mockery": "^1.5", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.3", "phpcsstandards/phpcsutils": "^1.0.0-rc1", "phpspec/prophecy-phpunit": "^2.0", "phpstan/extension-installer": "^1.2", "phpstan/phpstan": "^1.9", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-phpunit": "^1.3", "phpunit/phpunit": "^9.5", "psalm/plugin-mockery": "^1.1", "psalm/plugin-phpunit": "^0.18.4", "ramsey/coding-standard": "^2.0.3", "ramsey/conventional-commits": "^1.3", "vimeo/psalm": "^5.4"}, "minimum-stability": "RC", "prefer-stable": true, "autoload": {"psr-4": {"Ramsey\\Collection\\": "src/"}}, "autoload-dev": {"psr-4": {"Ramsey\\Collection\\Test\\": "tests/", "Ramsey\\Test\\Generics\\": "tests/generics/"}, "files": ["vendor/hamcrest/hamcrest-php/hamcrest/Hamcrest.php"]}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "ergebnis/composer-normalize": true, "phpstan/extension-installer": true, "captainhook/plugin-composer": true}, "sort-packages": true}, "extra": {"captainhook": {"force-install": true}, "ramsey/conventional-commits": {"configFile": "conventional-commits.json"}}, "scripts": {"dev:analyze": ["@dev:analyze:phpstan", "@dev:analyze:psalm"], "dev:analyze:phpstan": "phpstan analyse --ansi --memory-limit=1G", "dev:analyze:psalm": "psalm", "dev:build:clean": "git clean -fX build/", "dev:lint": ["@dev:lint:syntax", "@dev:lint:style"], "dev:lint:fix": "phpcbf", "dev:lint:style": "phpcs --colors", "dev:lint:syntax": "parallel-lint --colors src/ tests/", "dev:test": ["@dev:lint", "@dev:analyze", "@dev:test:unit"], "dev:test:coverage:ci": "phpunit --colors=always --coverage-text --coverage-clover build/coverage/clover.xml --coverage-cobertura build/coverage/cobertura.xml --coverage-crap4j build/coverage/crap4j.xml --coverage-xml build/coverage/coverage-xml --log-junit build/junit.xml", "dev:test:coverage:html": "phpunit --colors=always --coverage-html build/coverage/coverage-html/", "dev:test:unit": "phpunit --colors=always", "test": "@dev:test"}, "scripts-descriptions": {"dev:analyze": "Runs all static analysis checks.", "dev:analyze:phpstan": "Runs the PHPStan static analyzer.", "dev:analyze:psalm": "Runs the Psalm static analyzer.", "dev:build:clean": "Cleans the build/ directory.", "dev:lint": "Runs all linting checks.", "dev:lint:fix": "Auto-fixes coding standards issues, if possible.", "dev:lint:style": "Checks for coding standards issues.", "dev:lint:syntax": "Checks for syntax errors.", "dev:test": "Runs linting, static analysis, and unit tests.", "dev:test:coverage:ci": "Runs unit tests and generates CI coverage reports.", "dev:test:coverage:html": "Runs unit tests and generates HTML coverage report.", "dev:test:unit": "Runs unit tests.", "test": "Runs linting, static analysis, and unit tests."}}